import * as React from 'react';
import {useState} from 'react';
import AppBar from '@mui/material/AppBar';
import Box from '@mui/material/Box';
import Toolbar from '@mui/material/Toolbar';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import IconButton from '@mui/material/IconButton';
import {Divider, Popover, Stack, Tooltip} from '@mui/material';
import {useRouter} from "next/router";
import InfoIcon from '@mui/icons-material/Info';
import SearchAppBar from "@/components/common/SearchAppBar";
import HelpAndFeedback from "@/components/common/HelpAndFeedback";
import {useAuthStore} from "@/store/authStore";
import Link from "next/link";
import LockPersonIcon from '@mui/icons-material/LockPerson';


export default function TopNav() {

    const router = useRouter();

    const user = useAuthStore((state) => state.user);
    const setUser = useAuthStore((state) => state.setUser);
    const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
    const basePath = process.env.NEXT_PUBLIC_BASE_PATH || '';


    const [profileMenu, setProfileMenu] = useState<null | HTMLElement>(null);


    const handleProfileClick = (event: React.MouseEvent<HTMLElement>) => {
        setProfileMenu(event.currentTarget);
    };

    const handleClientLogout = () => {
        setUser(null);
        handleCloseProfile();

        window.location.href = `${basePath}/api/auth/saml/logout`;
    };


    const handleCloseProfile = () => {
        setProfileMenu(null);
    };


    return (
        <Box>
            <AppBar position="fixed" sx={{backgroundColor: 'rgb(34,44,103)'}}>
                <Toolbar variant="dense">
                    <Box
                        component="img"
                        sx={{
                            height: 40,
                            maxHeight: {xs: 25, md: 40},
                            paddingRight: 5,
                            paddingLeft: 2,
                            borderRight: '2px solid white',
                        }}
                        alt="App Logo"
                        src={`${process.env.NEXT_PUBLIC_BASE_PATH}/img/fda-logo-with-text.png`}
                    />
                    <Box>
                        <>
                            <Button onClick={() => router.push('/')}>
                                <Typography
                                    variant='h6'
                                    sx={{
                                        ml: 3,
                                        color: 'white',
                                        fontWeight: 500,
                                        lineHeight: 'normal',
                                    }}
                                >{'OFFICE OF DIGITAL TRANSFORMATION'}
                                </Typography>
                            </Button>
                        </>
                    </Box>
                    <Box sx={{flex: 1}}/>
                    <SearchAppBar/>
                    <Tooltip title="Data Access Request" placement="bottom-end">
                        <Button component={Link} variant="contained" size="small" sx={{
                            margin: '0 8px 0 8px',
                            backgroundColor: 'transparent', "&:hover": {
                                backgroundColor: 'rgb(67,75,126)',
                            }
                        }} href="/data-governance/data-access-request"
                                startIcon={<LockPersonIcon/>}> Request Access
                        </Button>
                    </Tooltip>


                    <div>


                        <HelpAndFeedback/>
                        <IconButton
                            size="large"
                            onClick={handleProfileClick}
                            color="inherit"
                        >
                            <Box
                                component="img"
                                src={`${process.env.NEXT_PUBLIC_BASE_PATH}/img/profile-icon.png`}
                                sx={{
                                    width: 20,
                                    height: 20,
                                    objectFit: 'cover',
                                }}
                            />
                        </IconButton>
                        <Popover
                            id="profile-menu"
                            open={Boolean(profileMenu)}
                            anchorEl={profileMenu}
                            onClose={handleCloseProfile}
                            anchorOrigin={{
                                vertical: 'bottom',
                                horizontal: 'right',
                            }}
                            transformOrigin={{
                                vertical: 'top',
                                horizontal: 'right',
                            }}

                            slotProps={{
                                paper: {
                                    sx: {
                                        minWidth: 240,
                                        overflow: 'inherit',
                                    }
                                }
                            }}
                        >
                            <Box sx={{p: 2}}>
                                {isAuthenticated && user != null ? (

                                    <Stack spacing={1}>
                                        <Typography variant="subtitle1" fontWeight="bold">
                                            {user?.displayName || 'User'}
                                        </Typography>
                                        <Typography variant="body2" color="text.secondary">
                                            {user?.email}
                                        </Typography>
                                        {user?.officeName && (
                                            <Typography variant="body2" color="text.secondary">
                                                <b>Office:</b> {user.officeName}
                                            </Typography>
                                        )}


                                        <Divider sx={{my: 1}}/>


                                        <Button
                                            variant="contained"
                                            color="primary"
                                            size="small"
                                            fullWidth
                                            onClick={handleClientLogout}
                                        >
                                            Log Out
                                        </Button>
                                    </Stack>
                                ) : (
                                    <Typography variant="body2" color="text.secondary">
                                        No user details found.
                                    </Typography>
                                )}
                            </Box>
                        </Popover>
                        <Tooltip title={"Data Concierge v" + process.env.version}>
                            <IconButton>
                                <InfoIcon style={{color: 'white'}}/>
                            </IconButton>
                        </Tooltip>
                    </div>
                </Toolbar>
            </AppBar>
        </Box>
    )

}
