import {<PERSON>, <PERSON><PERSON>, List, ListItem, <PERSON>I<PERSON><PERSON><PERSON>on, ListItemText, Typography} from "@mui/material";
import {useRouter} from 'next/router';
import React, {useEffect, useState} from "react";
import Link from "next/link";
import {leftNavStyles} from "@/styles/leftNavStyles";
import {useRecentActivityStore} from "@/store/activityStore";
import {RESOURCE_URLS} from "@/constants/constants";


const LeftNav = () => {
    const router = useRouter();
    const {pageVisits} = useRecentActivityStore();
    const {pathname} = router;

    const classes = leftNavStyles();

    const [selectedPath, setSelectedPath] = useState<string | ''>(pathname);

    const handleNavigation = (path: string) => {
        router.push(path);
        setSelectedPath(path ?? null);
    };

    useEffect(() => {
        setSelectedPath(router.asPath ?? null);
    }, [router.asPath]);

    const catalogItems = [
        {
            image: process.env.NEXT_PUBLIC_BASE_PATH + '/img/data-product-light.svg',
            imageSelected: process.env.NEXT_PUBLIC_BASE_PATH + '/img/data-product-dark.svg',
            label: "DATA PRODUCTS & SERVICES",
            count: 3,
            path: "/data-product",
            breadCrumbLabel: 'Data Products & Services'
        },
        {
            image: process.env.NEXT_PUBLIC_BASE_PATH + '/img/data-governance-light.svg',
            imageSelected: process.env.NEXT_PUBLIC_BASE_PATH + '/img/data-governance-dark.svg',
            label: "DATA GOVERNANCE",
            count: 7,
            path: "/data-governance",
            breadCrumbLabel: 'Data Governance'
        },
        {
            image: process.env.NEXT_PUBLIC_BASE_PATH + '/img/ai-service-light.svg',
            imageSelected: process.env.NEXT_PUBLIC_BASE_PATH + '/img/ai-service-dark.svg',
            label: "DATA MARKETPLACE",
            count: 3,
            path: "/data-marketplace",
            breadCrumbLabel: 'Data Marketplace Catalog'
        },
    ]

    return (
        <Box>
            <Button type='button' onClick={() => handleNavigation('/')}><Typography
                variant="h4" align="left">
                <span className={classes.leftNavDataHeader}>Data</span> <span
                className={classes.leftNavHeader}>Concierge</span>
            </Typography>
            </Button>
            <Box className={`${classes.commonContainer} ${classes.catalogContainer}`}>
                <Typography variant="h4" align="left">
                    CATALOGS
                </Typography>
                <Box>
                    <List>
                        {catalogItems.map((item) => (
                            <ListItem key={item.path} disablePadding>
                                <ListItemButton
                                    onClick={() => handleNavigation(item.path)}
                                    selected={selectedPath.includes(item.path)}
                                >
                                    <Box
                                        component="img"
                                        src={selectedPath.includes(item.path) ? item.imageSelected : item.image}
                                        sx={{
                                            width: 40,
                                            height: 40,
                                            marginRight: 1,
                                            objectFit: 'cover',
                                        }}
                                    />
                                    <ListItemText className={classes.listItem} primary={item.label.toUpperCase()}/>
                                    <Typography variant='caption'>({item.count})</Typography>
                                </ListItemButton>
                            </ListItem>
                        ))}
                    </List>
                </Box>
            </Box>


            <Box className={`${classes.commonContainer} ${classes.recentActivityContainer}`}>
                <Typography className={classes.listItem} sx={{marginBottom: '20px'}} variant="h5" align="left">
                    RECENT ACTIVITY
                </Typography>
                {pageVisits.slice(0, 6).map((item, index) => (
                    <Link
                        key={index}
                        className={`${classes.recentListItem} ${classes.commonLinkContainer}`}
                        href={item.path}

                    >
                        <div className={classes.recentActivity}>
                            {item.name}
                        </div>
                    </Link>
                ))}

                {/*<Button className={classes.button}>View More Activity</Button>*/}

            </Box>


            <Box className={`${classes.commonContainer} ${classes.fdaResourcesContainer}`}>
                <Typography variant="h5" align="left" className={classes.listItem}>
                    FDA RESOURCES
                </Typography>
                {RESOURCE_URLS.map((item, index) => (
                    <Link
                        key={index}
                        className={`${classes.recentListItem} ${classes.commonLinkContainer}`}
                        href={item.url}
                        target="_blank" rel="noopener noreferrer"
                        style={{
                            textDecoration: "none",
                            marginBottom: "15px",
                            display: "block",
                            marginTop: '15px',
                            marginLeft: '15px',
                        }}
                    >
                        <div className={classes.helpResources}>
                            {item.resource}
                        </div>
                    </Link>
                ))}
            </Box>
        </Box>


    )
};

export default LeftNav;