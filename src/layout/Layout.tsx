import React, {ReactNode} from "react";
import {Button, CssBaseline} from "@mui/material";
import TopNav from "./TopNav";
import LeftNav from "./LeftNav";
import {layoutStyles} from "@/styles/layoutStyles";
import Box from "@mui/material/Box";
import ChatBox from "@/components/common/ChatBox";

interface LayoutProps {
    children: ReactNode;
}

const Layout: React.FC<LayoutProps> = ({children}) => {

    const classes = layoutStyles();

    return (
        <>
            <CssBaseline/>
            <TopNav/>
            <div className={classes.innerContainer}>
                <div className={classes.leftNav}>
                    <LeftNav/>
                </div>
                <div className={classes.childrenContainer}>
                    <div style={{flex: 1}}>
                        {children}
                    </div>
                    {/*<div className={classes.buttonContainer}>*/}
                    {/*    <ChatBox></ChatBox>*/}
                    {/*</div>*/}
                </div>
            </div>
        </>
    );
};

export const getLayout = (page: React.ReactNode) => <Layout>{page}</Layout>;


export default Layout;
