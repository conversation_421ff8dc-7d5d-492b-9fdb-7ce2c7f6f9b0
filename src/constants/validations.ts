import * as Yup from 'yup';
export const duns_validation_schema = Yup.object().shape({
    dunsNumber: Yup.number().typeError("DUNS Number should be numeric").required("DUNS Number is required"),
});

export const duns_validation_validation_schema = Yup.object().shape({
    legalName:Yup.string().required("Business name is required"),
    addressLine1:Yup.string().required("Street Address is required"),
    city:Yup.string().required("City is required"),
    state:Yup.string().required("State is required"),
    countryCode:Yup.string().required("Country is required"),
    dunsNumber:Yup.number().typeError("DUNS Number should be a number").required("DUNS Number is required")
});

export const duns_name_address_lookup_validation_schema = Yup.object().shape({
    legalName:Yup.string().required("Business name is required"),
    countryCode:Yup.string().required("Country is required"),
    phoneNumber:Yup.number().typeError("Phone number should be a number")
});

export const business_glossary_validation_schema = Yup.object().shape({
    searchTerm:Yup.string().required("Search term is required")
});

export const data_dictionary_validation_schema = Yup.object().shape({
    searchTerm:Yup.string().required("Search term is required")
});

export const address_book_validation_schema = Yup.object().shape({
    primaryName:Yup.string().required("Firm Name is required")
});

export const geocode_schema = Yup.object().shape({
    addressLine1: Yup.string().required("Address is required"),
    countryCode: Yup.string().required("Country name is required"),
    state: Yup.string().notRequired(),
    city: Yup.string().required("City name is required"),
    zip_code: Yup.string().notRequired(),
});

export const fei_schema = Yup.object().shape({
    feiNumber: Yup.number().typeError("Please enter a FEI Number").required("Fei Number is required"),
});

export const address_doctor_schema = Yup.object().shape({
    addressLine1: Yup.string().required("Address is required"),
    countryCode: Yup.string().required("Country name is required"),
    stateCode: Yup.string().notRequired(),
    city: Yup.string().required("City name is required"),
    postalCode: Yup.string().notRequired(),
});