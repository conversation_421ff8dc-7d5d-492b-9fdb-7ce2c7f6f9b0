import {
    ALATION_URL,
    DMSC_URL,
    DSAB_URL,
    EDC_URL,
    EMDM_DUNS_REPOSITORY_URL,
    FDA_AI_URL,
    ORA_IMPORTS_DASHBOARD_URL
} from "@/constants/constants";

export const CATALOG_TILES = {
    governance: {
        catalog_name: "DATA GOVERNANCE",
        items: [
            {
                image: process.env.NEXT_PUBLIC_BASE_PATH + "/img/business-glossary.svg",
                name: "Business Glossary",
                description: "Lookup terms related to business",
                path: "/data-governance/business-glossary",
            },
            {
                image: process.env.NEXT_PUBLIC_BASE_PATH + "/img/data-dictionary.svg",
                name: "Data Dictionary",
                description: "Lookup terms related to Data Dictionary",
                path: "/data-governance/data-dictionary",
            },
            {
                image: process.env.NEXT_PUBLIC_BASE_PATH + "/img/policy.svg",
                name: "Policy",
                description: "Search Data Governance Policies and Information",
                path: "/data-governance/policy"
            },
            {
                image: process.env.NEXT_PUBLIC_BASE_PATH + "/img/data-standards.svg",
                name: "Data Standards",
                description: "Access FDA Data Standards and Information",
                show_options: true,
                path: "",
                options: [{
                    name: "FDA Data Standards Advisory Board (DSAB)",
                    path: DSAB_URL,
                    external_url: true,
                    description: "Access FDA Data Standards Advisory Board (DSAB) Sharepoint site."
                }]
            },
            {
                image: process.env.NEXT_PUBLIC_BASE_PATH + "/img/data-asset.svg",
                name: "FDA Data Catalog",
                description: "Access FDA Data Assets (Metadata)",
                path: "/data-governance/data-catalog"
            },
            {
                image: process.env.NEXT_PUBLIC_BASE_PATH + "/img/committee.svg",
                name: "Data Modernization Steering Committee",
                subtext: "",
                description: "Navigate to the Data Modernization Steering Committee (DMSC) Sharepoint site",
                path: DMSC_URL,
                external_url: true
            },
            {
                image: process.env.NEXT_PUBLIC_BASE_PATH + "/img/access-request.svg",
                name: "Data Access Request",
                description: "Process Data Access Request using ABAC/RBAC",
                path: "/data-governance/data-access-request"
            },
            {
                image: process.env.NEXT_PUBLIC_BASE_PATH + "/img/mou_builder.jfif",
                name: "Data Governance Document Builder",
                subtext: "(Coming Soon)",
                description: "Generate Data Governance Documents",
                path: "",
            },

        ]
    },
    products_services: {
        catalog_name: "DATA PRODUCTS & SERVICES",
        items: [
            {
                image: process.env.NEXT_PUBLIC_BASE_PATH + "/img/FDA-address-book.svg",
                name: "FDAddress Book",
                description: "Advance Search for the firm details referring DUNS, FEI, Address and Open Text.",
                path: "/data-product/address-book"
            },
            {
                image: process.env.NEXT_PUBLIC_BASE_PATH + "/img/D&B.svg",
                name: "D&B",
                description: "Identify firms on location-specific basis.",
                path: "/data-product/d&b"
            },
            {
                image: process.env.NEXT_PUBLIC_BASE_PATH + "/img/eMDM.svg",
                name: "eMDM APIs",
                description: "Access enterprise Master Data Records.",
                path: "/data-product/emdm-apis"
            },
            {
                image: process.env.NEXT_PUBLIC_BASE_PATH + "/img/emdm_toolkit.svg",
                name: "eMDM Toolkit",
                description: "Explore the eMDM RESTFul API calls and its illustration referring the common use-cases.",
                path: "/data-product/emdm-toolkit"
            },

        ]
    },
    marketplace: {
        catalog_name: "DATA MARKETPLACE",
        items: [
            {
                image: process.env.NEXT_PUBLIC_BASE_PATH + "/img/ai.svg",
                name: "FDA Artificial Intelligence (AI)",
                subtext: "",
                description: "Navigate to the FDA AI programs.",
                path: FDA_AI_URL,
                external_url: true
            },
            {
                image: process.env.NEXT_PUBLIC_BASE_PATH + "/img/center-look-up.svg",
                name: "Center Resources",
                description: "Access Center-Specific Dashboards.",
                path: "/data-marketplace/center-lookup"
            },
            {
                image: process.env.NEXT_PUBLIC_BASE_PATH + "/img/D&B.svg",
                name: "eMDM DUNS Repository",
                description: "Access the D&B information hosted on eMDM, including details about corporate lineage.",
                path: EMDM_DUNS_REPOSITORY_URL,
                external_url: true
            },
            {
                image: process.env.NEXT_PUBLIC_BASE_PATH + "/img/external-link.svg",
                name: "ORA Imports Dashboard",
                description: "Access the Imports Dashboard managed by ORA.",
                path: ORA_IMPORTS_DASHBOARD_URL,
                external_url: true
            },
            {
                image: process.env.NEXT_PUBLIC_BASE_PATH + "/img/third-party.svg",
                name: "Third-Party Data",
                subtext: "(Coming Soon)",
                description: "Access data from Third Parties",
                path: ""
            },
        ]
    },
    data_catalog: {
        catalog_name: "FDA Data Catalog",
        items: [{
            name: "Alation",
            image: process.env.NEXT_PUBLIC_BASE_PATH + "/img/alation.svg",
            path: ALATION_URL,
            external_url: true,
            description: "Access FDA data assets present on Alation platform."
        }, {
            name: "Informatica Enterprise Data Catalog (EDC)",
            image: process.env.NEXT_PUBLIC_BASE_PATH + "/img/informatica.svg",
            path: EDC_URL,
            external_url: true,
            description: "Access the FDA data assets present on Informatica EDC platform."
        }, {
            name: "FDA Data Assets Dashboard",
            path: "",
            image: process.env.NEXT_PUBLIC_BASE_PATH + "/img/dashboard.svg",
            external_url: true,
            description: "Download the FDA Data Assets Dashboard",
            show_options: true,
            options: [
                {
                    name: "FDA Data Assets Dashboard (PDF)",
                    path: process.env.NEXT_PUBLIC_BASE_PATH + "/docs/FDA Data Assets Dashboard.pdf",
                    external_url: true,
                    description: "View/Download FDA Data Assets Dashboard in PDF format"
                },
                {
                    name: "FDA Data Assets Dashboard (Power BI File)",
                    path: process.env.NEXT_PUBLIC_BASE_PATH + "/docs/FDA Data Assets Dashboard.pbix",
                    external_url: true,
                    description: "Download FDA Data Assets Dashboard to view in Power BI"
                }
            ]
        }]
    },
    emdm_toolkit: {
        catalog_name: "eMDM ToolKit",
        items: [
            {
                image: process.env.NEXT_PUBLIC_BASE_PATH + "/img/geo_code.svg",
                name: "GeoCode Service",
                subtext: "",
                description: "Obtain precise GeoCode details for a company/firm address based on specific address parameters",
                path: "/data-product/emdm-toolkit/geocode"
            },
            {
                image: process.env.NEXT_PUBLIC_BASE_PATH + "/img/center-look-up.svg",
                name: "FEI Lookup Service",
                description: "FEI (Facility Establishment Identifier) details for a FEI Number",
                path: "/data-product/emdm-toolkit/fei-lookup"
            },
            {
                image: process.env.NEXT_PUBLIC_BASE_PATH + "/img/third-party.svg",
                name: "Address Lookup By Address Doctor Service",
                description: "Validate the company/firm address based on address",
                path: "/data-product/emdm-toolkit/address-doctor"
            },
            {
                image: process.env.NEXT_PUBLIC_BASE_PATH + "/img/D&B.svg",
                name: "DUNS Service",
                description: "Retrieve company/firm details based on Firm Name and Address or DUNS Number",
                path: "/data-product/d&b"
            },

        ]
    }
}