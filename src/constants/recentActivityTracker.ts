import {useEffect} from 'react';
import {useRecentActivityStore} from "@/store/activityStore";
import {useRouter} from "next/router";

const RecentActivityTracker = (pageName: string) => {
    const router = useRouter();
    const {addPageVisit} = useRecentActivityStore();
    useEffect(() => {
        addPageVisit(pageName, router.asPath);
    }, [router.asPath,pageName,addPageVisit]);
};

export default RecentActivityTracker;