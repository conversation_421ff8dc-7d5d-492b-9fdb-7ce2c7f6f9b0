import { makeStyles } from '@mui/styles';
export const catalogStyles = makeStyles({
    textContent: {
        fontFamily: 'Roboto',
        fontWeight: '400',
        fontSize: '1rem',
        lineHeight: '38px',
    },

    heading: {
        fontWeight: '700',
        fontSize: '1.25rem',
        lineHeight: '48px',
        marginLeft: '10px',
        color: '#222C67'
    },

    outerContainer: {
        display: "flex",
        flexDirection: "row",
        alignItems: "center",
        marginLeft: '-7px'
    },
    divider: {
        marginTop: "30px",
        borderBottom: '1px solid black',
        width: '95%'
    },
    cardContentBox: {
        display: 'flex',
        flexWrap: 'wrap',
        gap: '35px',
        justifyContent: 'flex-start',
    }
});