import { makeStyles } from "@mui/styles";

export const layoutStyles = makeStyles({
    helpText: {
        width: 15,
        height: 15,
        marginRight: '10px',
        objectFit: 'cover',
    },
    childrenContainer: {
        maxWidth:'80vw',
        width:'80vw',
        display: 'flex',
        flexDirection: 'column',
        marginLeft: '20px',
        marginRight: '10px',
    },
    button: {
        padding: "5px 5px",
        borderRadius: "5px",
        cursor: "pointer",
        border: '2px solid #007CBA',
        color: '#000000',
        lineHeight: '24px',
    },
    buttonContainer: {
        position: 'fixed',
        bottom: 30,
        right: 30,
        backgroundColor: 'White',
        borderRadius: "5px",
    },
    innerContainer: {
        display: 'flex',
        flex: 1,
        marginTop: '50px'
    },
    leftNav: {
        flexBasis:'20%',
        width: '20%',
        marginLeft: '10px',
        marginRight:'10px',
    },



});
