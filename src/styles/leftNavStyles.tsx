import {makeStyles} from "@mui/styles";

export const leftNavStyles = makeStyles({
    leftNavDataHeader: {
        fontWeight: '700',
        fontSize: '1.5rem',
        color: '#222C67',
        lineHeight: '40px',
    },
    leftNavHeader: {
        fontWeight: '700',
        fontSize: '1.5rem',
        color: '#007CBA',
        lineHeight: '40px',
    },
    catalogInfo: {
        fontWeight: '700',
        fontSize: '0.8rem',
        color: '#000000'
    },
    commonContainer: {
        border: '1px solid #D9D9DA',
        borderRadius: '4px',
        padding: '12px 8px',
        marginBottom: '10px',
        marginTop: '10px',
    },
    catalogContainer: {
        '& > h4': {
            fontWeight: '700',
            fontSize: '1rem',
            lineHeight: '24px',
        }
    },
    listItem: {
        '& .MuiTypography-root': {
            fontWeight: '700',
            fontSize: '1rem',
            lineHeight: '24px'
        },
    },
    recentActivityContainer: {
        '& > h5': {
            fontWeight: '700',
            fontSize: '1rem',
            lineHeight: '24px',
        }
    },
    button: {
        border: '1px solid #007CBA',
        width: '368px',
        height: '32px',
        borderRadius: '2px',
        gap: '6px',
        padding: '8px 12px',
    },

    fdaResourcesContainer: {
        '& > h5': {
            fontWeight: '700',
            fontSize: '1rem',
            color: '#000000'
        }
    },
    recentListItem: {
        fontWeight: '400',
        fontSize: '0.95rem',
        lineHeight: '18px',
        color: '#0071BC',
    },
    recentActivity: {
        fontWeight: '400',
        lineHeight: '10px',
        color: '#0071BC',
        gap: '10px',
        fontSize: '0.95rem',
        textDecoration: 'none',
    },
    helpResources: {
        fontWeight: '500',
        fontSize: '0.95rem',
        lineHeight: '10px',
        color: '#0071BC',
        fontFamily: 'Roboto',
    },
    commonLinkContainer: {
        textDecoration: "none",
        marginBottom: "15px",
        display: "block",
        marginTop: '15px',
        marginLeft: '15px',
    },

    button_help: {
        padding: "10px 10px",
        borderRadius: "5px",
        cursor: "pointer",
        border: '2px solid #007CBA',
        color: '#000000',
        fontWeight: '700',
        fontSize: '1rem',
        lineHeight: '24px',
    },
    buttonContainer: {
        display: 'flex',
        justifyContent: 'flex-start',
        padding: '10px',

    },

    helpText: {
        width: 28,
        height: 28,
        marginRight: '10px',
        objectFit: 'cover',
    },

});
