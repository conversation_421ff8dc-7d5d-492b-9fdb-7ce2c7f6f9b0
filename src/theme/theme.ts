import {createTheme} from '@mui/material/styles';
import type {} from '@mui/lab/themeAugmentation';
import type {} from '@mui/x-data-grid/themeAugmentation';

const theme = createTheme({
    typography: {
        fontFamily: [
            "Roboto Condensed",
            "Roboto",
            'Inter',
            'sans-serif',
            '"Helvetica Neue"',
            'Arial'
        ].join(','),
        fontSize: 14,
        fontWeightLight: 300,
        fontWeightRegular: 400,
        fontWeightMedium: 500,
        fontWeightBold: 700,
        h1: {
            fontFamily: 'Roboto',
            fontWeight: 700,
            fontSize: '40px',
            lineHeight: '48px'
        },
        body2: {
            fontFamily: 'Roboto',
            fontWeight: 400
        },
    },

    components: {
        MuiBreadcrumbs: {
            styleOverrides: {
                root: {
                    fontSize: '0.85rem',
                },

            },
        },
        MuiMenuItem: {
            styleOverrides: {
                root: {
                    fontFamily: 'Roboto',
                },
            },
        },
        MuiButton: {
            styleOverrides: {
                root: {
                    fontFamily: 'Roboto',
                },
            },
        },
        MuiTabs: {
            styleOverrides: {
                root: {
                    minHeight: '35px',
                    height: '35px',
                    '& .MuiTabs-indicator': {
                        backgroundColor: 'white',
                        height: 0
                    },
                },
            },
        },
        MuiTab: {
            styleOverrides: {

                root: {
                    padding: 10,
                    color: '#007cba',
                    minHeight: '35px',
                    height: '35px',
                    fontWeight: 'bold',
                    borderRadius: '6px 6px 0 0',
                    border: '1px solid #007cba',
                    marginRight: '3px',
                    '&.Mui-selected': {
                        color: '#fdfbff',
                        background: '#222c67',
                        borderColor: '#222c67',
                        "&:hover": {
                            backgroundColor: '#222c67',
                        }
                    },
                    "&:hover": {
                        backgroundColor: '#007cba',
                        color: '#fff',
                    }


                }
            }
        },
        MuiTabPanel: {
            styleOverrides: {
                root: {
                    padding: '10px 0 0 0'
                }
            }
        },
        MuiDataGrid: {
            styleOverrides: {
                root: {
                    fontFamily: 'Roboto Condensed',
                    '&.MuiDataGrid-cell':{
                        overflowWrap: 'break-word',
                    },
                    '&.MuiDataGrid-root--densityCompact .MuiDataGrid-cell': {
                        paddingTop: '1px',
                        paddingBottom: '1px',
                        fontSize: '0.8rem',
                        // textAlign: 'justify',
                        overflowWrap: 'break-word',
                    },
                    '&.MuiDataGrid-root--densityStandard .MuiDataGrid-cell': {
                        paddingTop: '5px',
                        paddingBottom: '5px',
                        fontSize: '0.8rem',
                        // textAlign: 'justify',
                        overflowWrap: 'break-word',
                    },
                    '&.MuiDataGrid-root--densityComfortable .MuiDataGrid-cell': {
                        paddingTop: '10px',
                        paddingBottom: '10px',
                        fontSize: '0.8rem',
                        // textAlign: 'justify',
                        overflowWrap: 'break-word',
                    },
                    '& .MuiDataGrid-selectedRowCount': {
                        color: 'white'
                    },
                    '& .MuiCheckbox-root': {
                        color: '#222c67'
                    },
                    '& .MuiDataGrid-row:nth-child(odd)': {
                        backgroundColor: 'rgba(0, 0, 0, 0.04)'
                    },
                    '& .MuiDataGrid-columnHeaderSortIcon': {
                        color: 'white',
                    },
                    "&.MuiDataGrid-root .MuiDataGrid-row:hover": {
                        backgroundColor: "inherit",
                    },
                    "&.MuiDataGrid-root .Mui-hovered": {
                        backgroundColor: "inherit",
                    },
                    "&.MuiDataGrid-root .MuiDataGrid-columnHeaders": {
                        color: 'white',
                    },
                    "& .wrapHeader .MuiDataGrid-columnHeaderTitle": {

                    }
                },


                columnHeader: {
                    backgroundColor: '#222c67',
                    maxHeight: '40px',
                    minHeight:'35px',
                    '& .MuiSvgIcon-root': {
                        color: 'white'
                    }
                },
                columnHeaderTitle: {
                    color: 'white',
                    overflow: "hidden",
                    lineHeight: "20px",
                    whiteSpace: "normal",
                }

            },
        }
    },

});

export default theme;
