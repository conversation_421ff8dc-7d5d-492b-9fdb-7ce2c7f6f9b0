import {create} from 'zustand';
import type {User} from '@/lib/types';

interface AuthState {
    //The currently authenticated user object, or null if not authenticated.
    user: User | null;
    //Boolean flag indicating if a user is currently authenticated. Derived from user state.
    isAuthenticated: boolean;
    //Represents if the client-side authentication state is currently being determined (e.g., fetching /api/user).
    isLoading: boolean;
    setUser: (user: User | null) => void;
    setLoading: (loading: boolean) => void;
}

export const useAuthStore = create<AuthState>((set, get) => ({
    user: null,
    isAuthenticated: false,
    isLoading: true,

    setUser: (user) => {
        const currentUser = get().user;
        if (user?.id !== currentUser?.id || (!user && currentUser) || (user && !currentUser)) {
            set({
                user: user,
                isAuthenticated: !!user,
                isLoading: false,
            });
        } else {
            if (get().isLoading) {
                set({isLoading: false});
            }
        }
    },

    setLoading: (loading) => {
        if (get().isLoading !== loading) {
            set({isLoading: loading});
        }
    },
}));