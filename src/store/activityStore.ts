import {create} from 'zustand';
import {createJSONStorage, persist} from 'zustand/middleware';

interface PageVisit {
    name: string;
    path: string;
}

interface RecentActivityStore {
    pageVisits: PageVisit[];
    addPageVisit: (name: string, path: string) => void;
    clearPageVisits: () => void; // Add a clear function for demonstration
}

const MAX_VISITS = 15;

export const useRecentActivityStore = create<RecentActivityStore>()(
    persist(
        (set) => ({
            pageVisits: [],
            addPageVisit: (name, path) => {
                set((state) => {
                    const existingVisitIndex = state.pageVisits.findIndex((visit) => visit.path === path);

                    if (existingVisitIndex !== -1) {
                        // If the page is already in history, move it to the top
                        const existingVisit = state.pageVisits[existingVisitIndex];
                        const updatedVisits = state.pageVisits.filter((_, index) => index !== existingVisitIndex);
                        return {pageVisits: [existingVisit, ...updatedVisits]};
                    } else {
                        // If it's a new page visit, add it to the top
                        const updatedVisits = [{name, path}, ...state.pageVisits];
                        // Limit the history to MAX_VISITS
                        if (updatedVisits.length > MAX_VISITS) {
                            updatedVisits.pop(); // Remove the oldest visit from the end
                        }
                        return {pageVisits: updatedVisits};
                    }
                });
            },
            clearPageVisits: () => {
                set({pageVisits: []});
            },
        }),
        {
            name: 'recent-activity-store', // unique name for the store in indexedDB
            storage: createJSONStorage(() => localStorage),
        }
    )
);