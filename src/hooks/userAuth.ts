import useSWR, {SWRConfiguration} from 'swr';
import {useEffect} from 'react';
import {useAuthStore} from '@/store/authStore';
import type {User} from '@/lib/types';

interface UserApiResponse {
    user: User | null;
}

const basePath = process.env.NEXT_PUBLIC_BASE_PATH || ''

const fetcher = async (url: string): Promise<UserApiResponse> => {
    const res = await fetch(url);

    // Handle Not Authenticated explicitly
    if (res.status === 401) {
        return {user: null};
    }

    // Handle other errors
    if (!res.ok) {
        const error = new Error(`API Error fetching user (${res.status})`);
        try {
            (error as any).info = await res.json();
            (error as any).status = res.status;
        } catch { /* Ignore if response isn't JSON */
        }
        throw error; // Throw error to be caught by SWR
    }
    try {
        return await res.json();
    } catch (e) {
        console.error("Fetcher: Failed to parse JSON response.", e);
        throw new Error("Invalid JSON response from API");
    }
};

export function useAuth() {

    const userFromStore = useAuthStore((state) => state.user);
    const isStoreLoading = useAuthStore((state) => state.isLoading);
    const setUser = useAuthStore((state) => state.setUser);
    const setLoading = useAuthStore((state) => state.setLoading);

    // SWR Configuration
    const swrOptions: SWRConfiguration<UserApiResponse, Error> = {
        revalidateOnMount: true,
        revalidateOnFocus: false,      // Check when user returns to tab
        revalidateOnReconnect: true,
        shouldRetryOnError: false,
    };

    // SWR hook to fetch user status
    const {data, error, isLoading: isSWRLoading, mutate} = useSWR<UserApiResponse, Error>(
        basePath+'/api/user', // The API endpoint key
        fetcher,
        swrOptions
    );

    // Effect to synchronize SWR results with the Zustand store
    useEffect(() => {
        const overallLoading = isStoreLoading || isSWRLoading;
        setLoading(overallLoading);

        if (!isSWRLoading) {
            if (error) {
                console.error('[useAuth] SWR error fetching user status:', error);
            } else if (data !== undefined) {
                if (data.user?.username !== userFromStore?.username || (!data.user && userFromStore)) {
                    setUser(data.user);
                } else {
                    if (isStoreLoading) setLoading(false);
                }
            } else if (isStoreLoading) {
                setLoading(false);
            }
        }
    }, [data, error, isSWRLoading, isStoreLoading, userFromStore, setUser, setLoading]);

    return {
        user: userFromStore,
        isAuthenticated: !!userFromStore,
        isLoading: isStoreLoading,
        error,
        mutate,
    };
}