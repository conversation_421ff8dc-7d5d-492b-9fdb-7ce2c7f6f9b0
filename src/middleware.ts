import type {NextRequest} from "next/server";
import {NextResponse} from "next/server";


const UNPROTECTED_PATHS = [
    '/api/auth/saml/login',
    '/api/auth/saml/callback',
    '/api/auth/saml/metadata',
    '/saml-error',
    '/logged-out',
    '/api/health'
];

// Define paths for static assets, Next.js internals etc.
const PUBLIC_FILE = /\.(.*)$/;

export async function middleware(req: NextRequest) {

    const {pathname, search, origin} = req.nextUrl;

    // 1. Check if the path is explicitly unprotected
    const isUnprotected = UNPROTECTED_PATHS.some(path => pathname.startsWith(path));
    if (isUnprotected) {
        return NextResponse.next();
    }

    // 2. Check for static files, image optimization, fonts etc.
    if (
        pathname.startsWith('/_next') ||           // Next.js internals
        pathname.startsWith('/api/') ||            // Allow other API routes (unless listed above) - adjust if needed
        pathname.startsWith('/static') ||          // Public static files folder
        PUBLIC_FILE.test(pathname)                 // Files with extensions (e.g. .png, .ico)
    ) {
        return NextResponse.next();
    }

    // 3. Check for the existence of the Iron Session cookie
    const sessionCookieName = process.env.SESSION_COOKIE_NAME || 'emdm-session'; // Use the same name as in session.ts
    const sessionCookie = req.cookies.get(sessionCookieName);

    // 4. If no session cookie found, redirect to SAML login
    if (!sessionCookie) {
        const basePath = process.env.NEXT_PUBLIC_BASE_PATH || '';
        let loginUrl: URL;


        // --- DEVELOPMENT REDIRECT ---
        if (process.env.NODE_ENV === 'development') {
            console.log(`[Middleware DEV MODE] Cookie MISSING. Redirecting to DEV LOGIN.`);
            loginUrl = new URL(`${basePath}/api/auth/dev-login`, origin);
        } else {
            console.log(`[Middleware Production] Cookie MISSING. Redirecting to SAML login.`);
            loginUrl = new URL(`${basePath}/api/auth/saml/login`, origin);
        }


        //Add returnTo so user will be redirected to correct location after logging in
        if (!pathname.startsWith(`/api/`) && !pathname.startsWith('/_next/')) {
            const originalPath = `${pathname}${search}`; // Combine path and original query string
            loginUrl.searchParams.set('returnTo', originalPath);
        }

        return NextResponse.redirect(loginUrl);
    }

    // 5. If token EXISTS, allow the request to proceed
    return NextResponse.next();
}