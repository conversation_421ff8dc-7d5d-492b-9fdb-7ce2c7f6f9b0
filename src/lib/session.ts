import type {SessionOptions} from 'iron-session';

// Define iron-session options
export const sessionOptions: SessionOptions = {
    password: process.env.SESSION_SECRET as string,
    cookieName: process.env.SESSION_COOKIE_NAME || 'emdm-session',
    cookieOptions: {
        secure: process.env.NODE_ENV === 'production',
        httpOnly: true,
        sameSite: 'lax',
        maxAge: 86400,
        path: '/',
    },
};

// Basic validation for the secret
if (!process.env.SESSION_SECRET || process.env.SESSION_SECRET.length < 32) {
    const message = 'WARNING: SESSION_SECRET environment variable is not set, is too short (requires >= 32 chars), or not available. Session security is compromised.';
    console.warn(message);
}