export interface SamlProfile {
    email?: string | string[];
    username?: string | string[];
    displayName?: string | string[];
    officeName?: string | string[];
    firstName?: string | string[];
    lastName?: string | string[];
    [key: string]: any;
}

export interface User extends Record<string, any> {
    email: string;
    username: string;
    firstName?: string;
    lastName?: string;
    displayName?: string;
    officeName?: string;
    token?:string;
    samlAttributes?: SamlProfile
}

// Extend IronSessionData for type safety with iron-session
declare module 'iron-session' {
    interface IronSessionData {
        user?: User;
        returnTo?: string;
    }
}