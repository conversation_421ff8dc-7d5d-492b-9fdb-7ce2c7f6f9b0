import passport from 'passport';
import {Profile, SamlConfig, Strategy as SamlStrategy, VerifiedCallback} from '@node-saml/passport-saml';
import fs from 'fs';
import path from 'path';
import {Parser as XmlParser} from 'xml2js'
import type {SamlProfile as LocalSamlProfile} from './types';

// Cleans up base64 content, removes potential headers/footers if present and Re-add headers/footers required by passport-saml
const formatCert = (certContent: string | undefined): string | null => {
    if (!certContent) return null;
    const formatted = certContent.replace(/-----(BEGIN|END) CERTIFICATE-----/g, '').replace(/\s+/g, '').trim();
    return `-----BEGIN CERTIFICATE-----\n${formatted.match(/.{1,64}/g)?.join('\n') ?? ''}\n-----END CERTIFICATE-----`;
};

const readKeyCert = (filePath: string | undefined): string | undefined => {
    if (!filePath) return undefined;
    try {
        const resolvedPath = path.resolve(process.cwd(), filePath); // Ensure correct path resolution
        return fs.readFileSync(resolvedPath, 'utf-8');
    } catch (error: any) {
        console.error(`Error reading file at ${filePath}: ${error.message}`);
        return undefined;
    }
};

//Load IdP Configuration from Metadata XML
interface IdpConfig {
    entryPoint?: string;
    issuer?: string;
    cert: string;
}

let loadedIdpConfig: IdpConfig | null = null;
let loadError: Error | null = null;

function loadIdpConfigFromMetadata(): IdpConfig {
    if (loadedIdpConfig) return loadedIdpConfig;
    if (loadError) throw loadError;

    const metadataPath = process.env.IDP_METADATA_PATH || '';
    if (!metadataPath || metadataPath === '') {
        console.error('[SAML Config] Metadata file path not set');
    }
    const resolvedMetadataPath = path.resolve(process.cwd(), metadataPath);

    try {
        if (!fs.existsSync(resolvedMetadataPath)) {
            throw new Error(`IdP metadata file not found at configured path: ${resolvedMetadataPath}`);
        }

        const xmlData = fs.readFileSync(resolvedMetadataPath, 'utf-8');
        const parser = new XmlParser({explicitRoot: false, explicitArray: false, mergeAttrs: true});
        let parsedData: any;

        parser.parseString(xmlData, (err: Error | null, result: any) => {
            if (err) {
                throw new Error(`Failed to parse IdP metadata XML: ${err.message}`);
            }
            parsedData = result;
        });

        if (!parsedData) {
            throw new Error("XML parsing resulted in undefined data.");
        }

        // 1. Issuer (EntityID)
        const issuer = parsedData?.entityID; // Often an attribute on the root EntityDescriptor
        if (!issuer || typeof issuer !== 'string') {
            throw new Error('Could not find entityID attribute in IdP metadata.');
        }

        // 2. EntryPoint (SingleSignOnService Location)
        const idpSSODescriptor = parsedData['md:IDPSSODescriptor'];
        if (!idpSSODescriptor) throw new Error('md:IDPSSODescriptor not found in IdP metadata.');

        const ssoServices = Array.isArray(idpSSODescriptor['md:SingleSignOnService'])
            ? idpSSODescriptor['md:SingleSignOnService']
            : [idpSSODescriptor['md:SingleSignOnService']]; // Ensure it's an array

        // Prefer HTTP-Redirect, then HTTP-POST binding
        const redirectBinding = "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect";
        const postBinding = "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST";

        let entryPoint = ssoServices.find((s: any) => s?.Binding === redirectBinding)?.Location;
        if (!entryPoint) {
            entryPoint = ssoServices.find((s: any) => s?.Binding === postBinding)?.Location;
        }
        if (!entryPoint || typeof entryPoint !== 'string') {
            throw new Error(`Could not find suitable SingleSignOnService Location (Binding: ${redirectBinding} or ${postBinding}) in IdP metadata.`);
        }

        // 3. Certificate (Signing Certificate)
        const keyDescriptors = Array.isArray(idpSSODescriptor['md:KeyDescriptor'])
            ? idpSSODescriptor['md:KeyDescriptor']
            : [idpSSODescriptor['md:KeyDescriptor']]; // Ensure array

        const signingDescriptor = keyDescriptors.find((kd: any) => kd?.use === 'signing');
        if (!signingDescriptor) throw new Error('Signing KeyDescriptor (use="signing") not found in IdP metadata.');

        const certContent = signingDescriptor['ds:KeyInfo']?.['ds:X509Data']?.['ds:X509Certificate'];
        if (!certContent || typeof certContent !== 'string') {
            throw new Error('X509Certificate content not found within signing KeyDescriptor in IdP metadata.');
        }

        const formattedCert = formatCert(certContent) || ''; // Format for passport-saml

        loadedIdpConfig = {
            issuer: issuer,
            entryPoint: entryPoint,
            cert: formattedCert,
        };

        return loadedIdpConfig;

    } catch (error: any) {
        console.error(`[SAML Config] FATAL ERROR loading IdP metadata from ${resolvedMetadataPath}: ${error.message}`);
        loadError = error;
        throw error;
    }
}

const idpConfig = loadIdpConfigFromMetadata();
const spPrivateKey = readKeyCert(process.env.SP_PRIVATE_KEY_PATH);

const samlCallbackUrl = `${process.env.APP_BASE_URL}${process.env.NEXT_PUBLIC_BASE_PATH}${process.env.SP_CALLBACK_PATH}`;

if (!process.env.SP_ISSUER || !process.env.APP_BASE_URL || !process.env.SP_CALLBACK_PATH) {
    console.error("FATAL ERROR: Missing required SP/App environment variables (SP_ISSUER, APP_BASE_URL, SP_CALLBACK_PATH).");
    throw new Error("Missing critical SP/App SAML configuration.");
}


const samlConfig: SamlConfig = {
    entryPoint: idpConfig.entryPoint,
    issuer: process.env.SP_ISSUER,
    idpCert: idpConfig.cert,
    callbackUrl: samlCallbackUrl,
    identifierFormat: 'urn:oasis:names:tc:SAML:1.1:nameid-format:unspecified',
    acceptedClockSkewMs: 10000,
    disableRequestedAuthnContext: true,
    decryptionPvk: spPrivateKey,
    wantAssertionsSigned: false,
    wantAuthnResponseSigned: false,
};

const samlStrategy: any = new SamlStrategy(
    samlConfig,
    (profile: Profile | null | undefined, done: VerifiedCallback) => {
        if (!profile) {
            console.error("SAML Error: Profile is missing.");
            return done(new Error('SAML profile is missing'), undefined);
        }

        const samlProfile = profile as LocalSamlProfile;

        const getSingleValue = (value: string | string[] | undefined): string | undefined => (Array.isArray(value) ? value[0] : value);

        const email = getSingleValue(samlProfile.email);
        const firstName = getSingleValue(samlProfile.givenName);
        const lastName = getSingleValue(samlProfile.lastName);
        const displayName = getSingleValue(samlProfile.displayName);
        const officeName = getSingleValue(samlProfile.officeName);
        const username = getSingleValue(samlProfile.username);
        const user: Record<string, any> = {
            email: email,
            firstName: firstName,
            lastName: lastName,
            officeName: officeName,
            displayName: displayName,
            username: username
        };

        console.log("SAML Strategy: Verification successful. Mapped User:", JSON.stringify(user));
        return done(null, user);
    }, () => {
    }
);

passport.use('saml', samlStrategy);

export {passport, samlStrategy};