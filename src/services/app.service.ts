import {useAuthStore} from "@/store/authStore";

class AppService {


    private async fetchWithUserToken(endpoint: string, options: any = {}) {
        // Get the User from the Zustand store
        const user = useAuthStore.getState().user;

        const headers = new Headers(options.headers || {});
        headers.set('Accept', 'application/json');

        // Add Authorization header if token exists
        if (user) {
            headers.set('X-EMDMDC-USER', btoa(JSON.stringify(user)));
        }
        if (options.body && !headers.has('Content-Type')) {
            headers.set('Content-Type', 'application/json');
        }

        return await fetch(endpoint, {
            ...options,
            headers: headers,
        });
    }

    async getDunsLookupData(dunsNumber?: string) {
        const params = new URLSearchParams();
        params.set('dunsNumber', dunsNumber || '');
        return await this.fetchWithUserToken(`${process.env.NEXT_PUBLIC_BASE_API_URL}/api/product/dunsLookup?${params.toString()}`)
            .then((response) => response.json())
            .then((data) => data?.dunsMatchDetails);
    }

    async getDunsNameAddressLookupData(searchParams?: any) {
        return await this.fetchWithUserToken(`${process.env.NEXT_PUBLIC_BASE_API_URL}/api/product/dunsMatch?${new URLSearchParams(searchParams).toString()}`)
            .then((response) => response.json())
            .then((data) => data?.dunsMatchDetails);
    }

    async getBusinessGlossaryResults(searchParams?: any) {
        return await this.fetchWithUserToken(`${process.env.NEXT_PUBLIC_BASE_API_URL}/api/governance/glossary?${new URLSearchParams(searchParams).toString()}`)
            .then((response) => response.json())
    }

    async getDataDictionaryResults(searchParams?: any) {
        return await this.fetchWithUserToken(`${process.env.NEXT_PUBLIC_BASE_API_URL}/api/governance/dataDictionary?${new URLSearchParams(searchParams).toString()}`)
            .then((response) => response.json())
    }

    async getPolicyResults() {
        return await this.fetchWithUserToken(`${process.env.NEXT_PUBLIC_BASE_API_URL}/api/governance/policy`)
            .then((response) => response.json())
    }

    async getDataStandardsResults() {
        return await this.fetchWithUserToken(`${process.env.NEXT_PUBLIC_BASE_API_URL}/api/governance/dataStandard`)
            .then((response) => response.json())
    }

    async getFirmDetails(searchParams?: any) {
        return await this.fetchWithUserToken(`${process.env.NEXT_PUBLIC_BASE_API_URL}/api/product/addressBook/firm/firmdetails?${new URLSearchParams(searchParams).toString()}`)
            .then((response) => response.json())
    }

    async getAddressDoctorAddress(primaryAddress: {
        addressLine1: any;
        city: any;
        sourceCity: any;
        postalCode: any;
        srcPostalCode: any;
        countryCode: any;
        sourceCountryCode: any;
        stateProvinceName: any;
    }) {
        return await this.fetchWithUserToken(`${process.env.NEXT_PUBLIC_BASE_API_URL}/api/product/addressBook/address/find/byAddress?addressLine1=${primaryAddress?.addressLine1}&city=${primaryAddress?.city || primaryAddress?.sourceCity}&postalCode=${primaryAddress?.postalCode || primaryAddress?.srcPostalCode}&countryCode=${primaryAddress?.countryCode || primaryAddress?.sourceCountryCode}&stateCode=${primaryAddress?.stateProvinceName}`)
            .then((response) => response.json())
    }

    async getAddressDoctorAddressDetails(searchParams?: any) {
        return await this.fetchWithUserToken(`${process.env.NEXT_PUBLIC_BASE_API_URL}/api/product/addressBook/address/find/byAddress?${new URLSearchParams(searchParams).toString()}`)
            .then((response) => response.json())
    }

    async getAddressBookSearchResults(searchParams?: any) {
        return await this.fetchWithUserToken(`${process.env.NEXT_PUBLIC_BASE_API_URL}/api/product/addressBook/firm?${new URLSearchParams(searchParams).toString()}`)
            .then((response) => response.json())
    }

    async saveFeedBack(feedback: any) {
        return await fetch(`${process.env.NEXT_PUBLIC_BASE_API_URL}/api/feedback`, {
            method: 'POST',
            headers: {
                'Content-type': 'application/json',
            },
            body: JSON.stringify(feedback),
        })
            .then((response) => response)
    }


    async fetchGeoCodeAddressDetails(searchParams: any) {
        return await this.fetchWithUserToken(`${process.env.NEXT_PUBLIC_BASE_API_URL}/api/product/geocode/find/byGeoCodeAddressDetails?${new URLSearchParams(searchParams).toString()}`)
            .then((response) => response.json())
    };


    async fetchFEIDetails(searchParams: any) {
        return await this.fetchWithUserToken(`${process.env.NEXT_PUBLIC_BASE_API_URL}/api/product/feiMatch/find/byFeiNumber?${new URLSearchParams(searchParams).toString()}`)
            .then((response) => response.json())
    };


}

const appService: AppService = new AppService();

export default appService;