import React, {useState} from "react";
import {<PERSON><PERSON>, <PERSON><PERSON>, CircularProgress, TextField} from "@mui/material";
import Box from "@mui/material/Box";
import Grid from '@mui/material/Grid2';
import PaginatedTable from "@/components/common/PaginatedTable";
import {duns_validation_schema} from "@/constants/validations";
import {useFormik} from "formik";
import appService from "@/services/app.service";



function DunsLookup() {

    const [dunsLookUpData, setDunsLookUpData] = useState([]);
    const [initialLoad, setInitialLoad] = useState(true);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');


    const formik = useFormik({
        initialValues: {dunsNumber: ""},
        validationSchema: duns_validation_schema,
        onSubmit: (values) => {
            fetchData(values)
        },
    });

    function resetValues() {
        setLoading(false);
        setError('');
        setDunsLookUpData([]);
        setInitialLoad(true);
    }

    const fetchData = async (values: { dunsNumber: any; }) => {
        resetValues();
        setLoading(true);
        try {
            await appService.getDunsLookupData(values?.dunsNumber).then(data => setDunsLookUpData(data));
        } catch (err) {
            setError(String(err));
            console.error("Error fetching data:", err);
        } finally {
            setLoading(false);
            setInitialLoad(false);
        }
    };

    const dunsLookupHeaders = [
        {label: 'Duns Number', key: 'dunsNumber', flex: 1},
        {label: 'Business Name', key: 'dnbBusinessName', flex: 1},
        {label: 'Address', key: 'dnbAddress', flex: 2},
        {label: 'City', key: 'dnbCity', flex: 1},
        {label: 'Country', key: 'dnbCountryName', flex: 1},
        {label: 'State/Province', key: 'dnbStateProvince', flex: 1},
        {label: 'Postal Code', key: 'dnbPostalCode', flex: 1},
        {label: 'Phone Number', key: 'dnbPhoneNumber', flex: 1},
        {label: 'Address Score', key: 'matchGrade', flex: 1},
        {label: 'Confidence Code', key: 'confidenceCode', flex: 1}
    ]

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid container spacing={2}>
                <Grid size={{xs: 4, md: 4}}>
                    <TextField fullWidth
                               id="dunsNumber"
                               name="dunsNumber"
                               label="DUNS Number"
                               value={formik.values.dunsNumber}
                               onChange={formik.handleChange}
                               onBlur={formik.handleBlur}
                               error={formik.touched.dunsNumber && Boolean(formik.errors.dunsNumber)}
                               helperText={formik.touched.dunsNumber && formik.errors.dunsNumber}
                               variant="outlined"
                               size="small"

                    />
                </Grid>
                <Grid size={{md: 4}}>
                    <Button type="submit" size="medium" sx={{mr: 2}} variant="contained">Search</Button>
                    <Button size="medium" sx={{mr: 2}} variant="contained"
                            onClick={() => {
                                formik.resetForm();
                                resetValues();
                                setLoading(false);
                            }}>Clear</Button>
                </Grid>
            </Grid>
            {
                loading && <Box sx={{width: '100%', mt: 1}}>
                    <CircularProgress/>
                </Box>

            }

            {!initialLoad && !loading && error === '' && dunsLookUpData.length === 0 &&
                <Alert sx={{margin: '10px 0 0 0', width: 'fit-content'}} severity="error">No results
                    found</Alert>}


            {
                dunsLookUpData.length != 0 && <Box sx={{width: '100%', typography: 'body1', mt: 2}}>
                    <PaginatedTable system='d&b' headers={dunsLookupHeaders} data={dunsLookUpData}/>
                </Box>
            }
        </form>


    )
}

export default React.memo(DunsLookup)