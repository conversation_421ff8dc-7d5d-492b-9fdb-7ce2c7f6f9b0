import {<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ir<PERSON><PERSON><PERSON>ress, TextField} from "@mui/material";
import Grid from "@mui/material/Grid2"
import React, {useState} from "react";
import Box from "@mui/material/Box";
import {useFormik} from "formik";
import {duns_validation_validation_schema} from "@/constants/validations";
import appService from "@/services/app.service";
import DunsComparision from "@/components/d&b/DunsComparision";
import CountryAutoComplete from "@/components/common/CountryAutoComplete";



function DunsValidation() {

    const [dunsLookupData, setDunsLookupData] = useState({});
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [initialLoad, setInitialLoad] = useState(true);


    const formik = useFormik({
        initialValues: {
            legalName: '',
            addressLine1: '',
            city: '',
            state: '',
            countryCode: '',
            postalCode: '',
            phoneNumber: '',
            dunsNumber: ''
        },
        validationSchema: duns_validation_validation_schema,
        onSubmit: (values) => {
            fetchData(values)
        },
    });

    function resetValues() {
        setLoading(false);
        setError('');
        setDunsLookupData([]);
        setInitialLoad(true);
    }

    const fetchData = async (searchParams: any) => {
        setLoading(true);
        setError('');

        try {
            await appService.getDunsLookupData(searchParams?.dunsNumber).then(data => {
                const dunsItem = data[0];
                setDunsLookupData({
                    legalName: dunsItem?.dnbBusinessName,
                    addressLine1: dunsItem?.dnbAddress,
                    city: dunsItem?.dnbCity,
                    state: dunsItem?.dnbStateProvince,
                    countryCode: dunsItem?.dnbCountryName,
                    postalCode: dunsItem?.dnbPostalCode,
                    phoneNumber: dunsItem?.dnbPhoneNumber,
                    dunsNumber: dunsItem?.dunsNumber
                });
            });

        } catch (err) {
            setError(String(err));
            console.error("Error fetching data:", err);
        } finally {
            setLoading(false);
            setInitialLoad(false);
        }
    };


    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid container spacing={2}>
                <Grid size={{xs: 4, md: 1.5}}>
                    <TextField fullWidth
                               id="dunsNumber"
                               name="dunsNumber"
                               label="DUNS Number"
                               value={formik.values.dunsNumber}
                               onChange={formik.handleChange}
                               onBlur={formik.handleBlur}
                               error={formik.touched.dunsNumber && Boolean(formik.errors.dunsNumber)}
                               helperText={formik.touched.dunsNumber && formik.errors.dunsNumber}
                               variant="outlined"
                               size="small"

                    />
                </Grid>
                <Grid size={{xs: 4, md: 1.5}}>
                    <TextField fullWidth
                               id="legalName"
                               name="legalName"
                               label="Business Name"
                               value={formik.values.legalName}
                               onChange={formik.handleChange}
                               onBlur={formik.handleBlur}
                               error={formik.touched.legalName && Boolean(formik.errors.legalName)}
                               helperText={formik.touched.legalName && formik.errors.legalName}
                               variant="outlined"
                               size="small"

                    />
                </Grid>
                <Grid size={{xs: 4, md: 1.5}}>
                    <TextField fullWidth
                               id="addressLine1"
                               name="addressLine1"
                               label="Street Address"
                               value={formik.values.addressLine1}
                               onChange={formik.handleChange}
                               onBlur={formik.handleBlur}
                               error={formik.touched.addressLine1 && Boolean(formik.errors.addressLine1)}
                               helperText={formik.touched.addressLine1 && formik.errors.addressLine1}
                               variant="outlined"
                               size="small"

                    />
                </Grid>
                <Grid size={{xs: 4, md: 1.5}}>
                    <TextField fullWidth
                               id="city"
                               name="city"
                               label="City"
                               value={formik.values.city}
                               onChange={formik.handleChange}
                               onBlur={formik.handleBlur}
                               error={formik.touched.city && Boolean(formik.errors.city)}
                               helperText={formik.touched.city && formik.errors.city}
                               variant="outlined"
                               size="small"

                    />
                </Grid>
                <Grid size={{xs: 4, md: 1.5}}>
                    <CountryAutoComplete formik={formik}/>
                </Grid>
                <Grid size={{xs: 4, md: 1.5}}>
                    <TextField fullWidth
                               id="state"
                               name="state"
                               label="State/Province"
                               value={formik.values.state}
                               onChange={formik.handleChange}
                               onBlur={formik.handleBlur}
                               error={formik.touched.state && Boolean(formik.errors.state)}
                               helperText={formik.touched.state && formik.errors.state}
                               variant="outlined"
                               size="small"

                    />
                </Grid>
                <Grid size={{xs: 4, md: 1.5}}>
                    <TextField fullWidth
                               id="postalCode"
                               name="postalCode"
                               label="Zip/Postal Code"
                               value={formik.values.postalCode}
                               onChange={formik.handleChange}
                               onBlur={formik.handleBlur}
                               error={formik.touched.postalCode && Boolean(formik.errors.postalCode)}
                               helperText={formik.touched.postalCode && formik.errors.postalCode}
                               variant="outlined"
                               size="small"

                    />
                </Grid>
                <Grid size={{xs: 4, md: 1.5}}>
                    <TextField fullWidth
                               id="phoneNumber"
                               name="phoneNumber"
                               label="Phone Number"
                               value={formik.values.phoneNumber}
                               onChange={formik.handleChange}
                               onBlur={formik.handleBlur}
                               error={formik.touched.phoneNumber && Boolean(formik.errors.phoneNumber)}
                               helperText={formik.touched.phoneNumber && formik.errors.phoneNumber}
                               variant="outlined"
                               size="small"

                    />
                </Grid>
                <Grid size={{md: 4}}>
                    <Button type="submit" size="medium" sx={{mr: 2}} variant="contained">Validate</Button>
                    <Button size="medium" sx={{mr: 2}} variant="contained"
                            onClick={() => {
                                formik.resetForm();
                                resetValues();
                                setLoading(false);
                            }}>Clear</Button>

                </Grid>
            </Grid>
            {
                loading && <Box sx={{width: '100%', mt: 1}}>
                    <CircularProgress/>
                </Box>

            }
            {!initialLoad && !loading && error === '' && Object.keys(dunsLookupData).length === 0 &&
                <Alert sx={{margin: '10px 0 0 0', width: 'fit-content'}} severity="error">No results
                    found</Alert>}

            {
                Object.keys(dunsLookupData).length != 0 && <Box sx={{width: '100%', typography: 'body1', mt: 2}}>
                    <DunsComparision dunsMatchData={formik.values} dunsLookupData={dunsLookupData}/>
                </Box>
            }
        </form>


    )
}

export default React.memo(DunsValidation)
