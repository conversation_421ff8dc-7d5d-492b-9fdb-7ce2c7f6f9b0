import React from 'react';
import {Badge, Grid, styled, Typography} from '@mui/material';
import Fuse from 'fuse.js';

interface ComparisonResult {
    field: string;
    value1: string;
    value2: string;
    matchStatus: 'full-match' | 'partial-match' | 'no-match' | 'unknown';
}

interface ComparisonProps {
    dunsMatchData: any;
    dunsLookupData: any;
}

const dunsFields: { [index: string]: any } = {
    'legalName': 'Business Name',
    'addressLine1': 'Street Address',
    'city': 'City',
    'state': 'State/Province',
    'postalCode': 'Zip/Postal Code',
    'countryCode': 'Country',
    'phoneNumber': 'Phone Number',
    'dunsNumber': 'Duns Number'
};

// Styled Badge for increased width AND consistent spacing
const StyledBadge = styled(Badge)(() => ({
    '& .MuiBadge-badge': {
        minWidth: 120,
        padding: '0 10px',
    },
}));

// Styled container for summary item (text + count)
const SummaryItem = styled('div')({
    display: 'flex',
    alignItems: 'center',
    marginRight: 0, // Add consistent spacing between summary items
});

const DunsComparison: React.FC<ComparisonProps> = ({dunsMatchData, dunsLookupData}) => {
    const compareJSONs = (): ComparisonResult[] => {
        const comparisonResults: ComparisonResult[] = [];
        const keys1 = Object.keys(dunsMatchData);

        const fullMatchThreshold = 0.6;
        const partialMatchThreshold = 0.2;

        for (const key of keys1) {
            const value1 = dunsMatchData[key];
            const value2 = dunsLookupData[key];

            let matchStatus: ComparisonResult['matchStatus'] = 'no-match';

            if (typeof value1 === 'string' && typeof value2 === 'string') {
                const fuse = new Fuse([value1], {
                    shouldSort: false,
                    isCaseSensitive: false,
                    includeScore: true,
                });
                const result = fuse.search(value2);
                let similarityScore = 1;

                if (result.length > 0) {
                    similarityScore = result[0].score !== undefined ? result[0].score : 1;
                }

                if(similarityScore==1&&(key=='postalCode'||key=='phoneNumber'||key=='dunsNumber')){
                    matchStatus='full-match'
                } else if (similarityScore <= 1 - fullMatchThreshold) {
                    matchStatus = 'full-match';
                } else if (similarityScore <= 1 - partialMatchThreshold) {
                    matchStatus = 'partial-match';
                } else {
                    matchStatus = 'no-match';
                }
            } else if (typeof value1 === 'number' && typeof value2 === 'number') {
                if (value1 === value2) {
                    matchStatus = 'full-match';
                } else {
                    matchStatus = 'no-match';
                }
            } else if (value1 === value2) {
                matchStatus = 'full-match';
            } else {
                matchStatus = 'no-match';
            }

            comparisonResults.push({
                field: key,
                value1: value1 !== undefined ? String(value1) : 'N/A',
                value2: value2 !== undefined ? String(value2) : 'N/A',
                matchStatus,
            });
        }
        return comparisonResults;
    };

    const results: ComparisonResult[] = compareJSONs();

    const getBadgeColor = (status: ComparisonResult['matchStatus']) => {
        switch (status) {
            case 'full-match':
                return 'success';
            case 'partial-match':
                return 'warning';
            case 'no-match':
                return 'error';
            default:
                return 'default';
        }
    };

    const getBadgeLabel = (status: ComparisonResult['matchStatus']) => {
        switch (status) {
            case 'full-match':
                return 'Full Match';
            case 'partial-match':
                return 'Partial Match';
            case 'no-match':
                return 'No Match';
            default:
                return 'Unknown';
        }
    };

    const fullMatchCount = results.filter((res) => res.matchStatus === 'full-match').length;
    const partialMatchCount = results.filter((res) => res.matchStatus === 'partial-match').length;
    const noMatchCount = results.filter((res) => res.matchStatus === 'no-match').length;

    return (
        <>
            <Typography variant="h5" gutterBottom>
                DUNS Validation
            </Typography>

            {/* Summary Report at the top - Use a simple flex container */}
            <div style={{display: 'flex', alignItems: 'center', marginBottom: 16}}>
                <SummaryItem>
                    <Typography variant="body1" ml={1}>
                        Full Match: {fullMatchCount}
                    </Typography>
                </SummaryItem>
                <SummaryItem>
                    <Typography variant="body1" ml={1}>
                        Partial Match: {partialMatchCount}
                    </Typography>
                </SummaryItem>
                <SummaryItem>
                    <Typography variant="body1" ml={1}>
                        No Match: {noMatchCount}
                    </Typography>
                </SummaryItem>
            </div>

            <Grid container spacing={2} sx={{border: '1px solid lightgray'}}>
                <Grid item xs={12}>
                    <Grid container spacing={2} sx={{fontWeight: 'bold', borderBottom: '1px solid lightgray'}}>
                        <Grid item xs={3} sx={{borderRight: '1px solid lightgray'}}>
                            Field
                        </Grid>
                        <Grid item xs={3} sx={{borderRight: '1px solid lightgray'}}>
                            Provided Address
                        </Grid>
                        <Grid item xs={3} sx={{borderRight: '1px solid lightgray'}}>
                            DUNS Address
                        </Grid>
                        <Grid item xs={3} sx={{p: 0.5, display: 'flex', alignItems: 'center', justifyContent: 'center'}}>
                            Match Status
                        </Grid>
                    </Grid>
                </Grid>

                {results.map((result, index) => (
                    <Grid item xs={12} key={index} sx={{borderBottom: '1px solid lightgray'}}>
                        <Grid container spacing={2} alignItems="center">
                            <Grid item xs={3} sx={{borderRight: '1px solid lightgray'}}>
                                <Typography variant="body2">{dunsFields[result.field]}</Typography>
                            </Grid>
                            <Grid item xs={3} sx={{borderRight: '1px solid lightgray'}}>
                                <Typography variant="body2">{result.value1}</Typography>
                            </Grid>
                            <Grid item xs={3} sx={{borderRight: '1px solid lightgray'}}>
                                <Typography variant="body2">{result.value2}</Typography>
                            </Grid>
                            <Grid item xs={3}
                                  sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center'}}>
                                <StyledBadge badgeContent={getBadgeLabel(result.matchStatus)}
                                             color={getBadgeColor(result.matchStatus)}/>
                            </Grid>
                        </Grid>
                    </Grid>
                ))}
            </Grid>
        </>
    );
};

export default DunsComparison;