import {<PERSON><PERSON>, <PERSON><PERSON>, CircularProgress, TextField} from "@mui/material";
import Grid from "@mui/material/Grid2"
import React, {useState} from "react";
import PaginatedTable from "@/components/common/PaginatedTable";
import Box from "@mui/material/Box";
import {useFormik} from "formik";
import {duns_name_address_lookup_validation_schema} from "@/constants/validations";
import appService from "@/services/app.service";
import CountryAutoComplete from "@/components/common/CountryAutoComplete";



function DunsNameAddressLookup() {

    const formik = useFormik({
        initialValues: {
            legalName: '',
            addressLine1: '',
            city: '',
            state: '',
            countryCode: '',
            postalCode: '',
            phoneNumber: ''
        },
        validationSchema: duns_name_address_lookup_validation_schema,
        onSubmit: (values) => {
            fetchData(values)
        },
    });

    const [dunsSearchData, setDunsSearchData] = useState([]);
    const [initialLoad, setInitialLoad] = useState(true);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');

    function resetValues() {
        setLoading(false);
        setError('');
        setDunsSearchData([]);
        setInitialLoad(true);
    }

    const fetchData = async (searchParams: any) => {
        resetValues();
        setLoading(true);
        try {
            await appService.getDunsNameAddressLookupData(searchParams).then(data => setDunsSearchData(data));
        } catch (err) {
            setError(String(err));
            console.error("Error fetching data:", err);
        } finally {
            setLoading(false);
            setInitialLoad(false);
        }
    };

    const dunsSearchHeaders = [
        {label: 'Duns Number', key: 'dunsNumber', flex: 1},
        {label: 'Business Name', key: 'dnbBusinessName', flex: 1},
        {label: 'Address', key: 'dnbAddress', flex: 2},
        {label: 'City', key: 'dnbCity', flex: 1},
        {label: 'Country', key: 'dnbCountryName', flex: 1},
        {label: 'State/Province', key: 'dnbStateProvince', flex: 1},
        {label: 'Postal Code', key: 'dnbPostalCode', flex: 1},
        {label: 'Phone Number', key: 'dnbPhoneNumber', flex: 1},
        {label: 'Address Score', key: 'matchGrade', flex: 1},
        {label: 'Confidence Code', key: 'confidenceCode', flex: 1}
    ]


    return (
        <>
            <form onSubmit={formik.handleSubmit}>
                <Grid container spacing={2}>
                    <Grid size={{xs: 4, md: 1.5}}>
                        <TextField fullWidth
                                   id="legalName"
                                   name="legalName"
                                   label="Business Name"
                                   value={formik.values.legalName}
                                   onChange={formik.handleChange}
                                   onBlur={formik.handleBlur}
                                   error={formik.touched.legalName && Boolean(formik.errors.legalName)}
                                   helperText={formik.touched.legalName && formik.errors.legalName}
                                   variant="outlined"
                                   size="small"

                        />
                    </Grid>
                    <Grid size={{xs: 4, md: 1.5}}>
                        <TextField fullWidth
                                   id="addressLine1"
                                   name="addressLine1"
                                   label="Street Address"
                                   value={formik.values.addressLine1}
                                   onChange={formik.handleChange}
                                   onBlur={formik.handleBlur}
                                   error={formik.touched.addressLine1 && Boolean(formik.errors.addressLine1)}
                                   helperText={formik.touched.addressLine1 && formik.errors.addressLine1}
                                   variant="outlined"
                                   size="small"

                        />
                    </Grid>
                    <Grid size={{xs: 4, md: 1.5}}>
                        <TextField fullWidth
                                   id="city"
                                   name="city"
                                   label="City"
                                   value={formik.values.city}
                                   onChange={formik.handleChange}
                                   onBlur={formik.handleBlur}
                                   error={formik.touched.city && Boolean(formik.errors.city)}
                                   helperText={formik.touched.city && formik.errors.city}
                                   variant="outlined"
                                   size="small"

                        />
                    </Grid>
                    <Grid size={{xs: 4, md: 2}}>
                        <CountryAutoComplete formik={formik}/>
                    </Grid>
                    <Grid size={{xs: 4, md: 1.5}}>
                        <TextField fullWidth
                                   id="state"
                                   name="state"
                                   label="State/Province"
                                   value={formik.values.state}
                                   onChange={formik.handleChange}
                                   onBlur={formik.handleBlur}
                                   error={formik.touched.state && Boolean(formik.errors.state)}
                                   helperText={formik.touched.state && formik.errors.state}
                                   variant="outlined"
                                   size="small"

                        />
                    </Grid>
                    <Grid size={{xs: 4, md: 1.5}}>
                        <TextField fullWidth
                                   id="postalCode"
                                   name="postalCode"
                                   label="Zip/Postal Code"
                                   value={formik.values.postalCode}
                                   onChange={formik.handleChange}
                                   onBlur={formik.handleBlur}
                                   error={formik.touched.postalCode && Boolean(formik.errors.postalCode)}
                                   helperText={formik.touched.postalCode && formik.errors.postalCode}
                                   variant="outlined"
                                   size="small"

                        />
                    </Grid>
                    <Grid size={{xs: 4, md: 1.5}}>
                        <TextField fullWidth
                                   id="phoneNumber"
                                   name="phoneNumber"
                                   label="Phone Number"
                                   value={formik.values.phoneNumber}
                                   onChange={formik.handleChange}
                                   onBlur={formik.handleBlur}
                                   error={formik.touched.phoneNumber && Boolean(formik.errors.phoneNumber)}
                                   helperText={formik.touched.phoneNumber && formik.errors.phoneNumber}
                                   variant="outlined"
                                   size="small"

                        />
                    </Grid>
                    <Grid size={{md: 4}}>
                        <Button type="submit" size="medium" sx={{mr: 2}} variant="contained">Search</Button>
                        <Button size="medium" sx={{mr: 2}} variant="contained"
                                onClick={() => {
                                    formik.resetForm();
                                    resetValues();
                                    setLoading(false);
                                }}>Clear</Button>

                    </Grid>
                </Grid>
                {
                    loading && <Box sx={{width: '100%', mt: 1}}>
                        <CircularProgress/>
                    </Box>

                }

                {!initialLoad && !loading && error === '' && dunsSearchData.length === 0 &&
                    <Alert sx={{margin: '10px 0 0 0', width: 'fit-content'}} severity="error">No results
                        found</Alert>}


                {
                    dunsSearchData.length != 0 && <Box sx={{width: '100%', typography: 'body1', mt: 2}}>
                        <PaginatedTable system='d&b' headers={dunsSearchHeaders} data={dunsSearchData}/>
                    </Box>
                }
            </form>
        </>


    )
}

export default React.memo(DunsNameAddressLookup)
