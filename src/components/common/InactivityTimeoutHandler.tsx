import React, {useCallback, useEffect, useRef, useState} from 'react';
import {useAuthStore} from '@/store/authStore';

import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';


const getTimeoutValue = (envVar: string | undefined, defaultValueSeconds: number): { ms: number; seconds: number } => {
    const parsedValue = parseInt(envVar || '', 10);
    const seconds = !isNaN(parsedValue) && parsedValue > 0 ? parsedValue : defaultValueSeconds;
    return {ms: seconds * 1000, seconds: seconds};
};

const inactivityConfig = getTimeoutValue(process.env.NEXT_PUBLIC_INACTIVITY_TIMEOUT_SECONDS, 600);
const confirmationConfig = getTimeoutValue(process.env.NEXT_PUBLIC_CONFIRMATION_TIMEOUT_SECONDS, 30);

export const InactivityTimeoutHandler: React.FC = () => {
    // Get only the necessary state from Zustand
    const isAuthenticated = useAuthStore((state) => state.isAuthenticated);

    const [showConfirmation, setShowConfirmation] = useState(false);
    const [countdown, setCountdown] = useState(confirmationConfig.seconds);

    // Refs for timers
    const inactivityTimerRef: any = useRef<any>(null);
    const confirmationTimerRef: any = useRef<any>(null);
    const countdownIntervalRef: any = useRef<any>(null);

    // Logout: No external dependencies needed within the function itself
    const handleLogout = useCallback(() => {
        if (inactivityTimerRef.current) clearTimeout(inactivityTimerRef.current);
        if (confirmationTimerRef.current) clearTimeout(confirmationTimerRef.current);
        if (countdownIntervalRef.current) clearInterval(countdownIntervalRef.current);
        setShowConfirmation(false);

        const basePath = process.env.NEXT_PUBLIC_BASE_PATH || '';
        window.location.href = `${basePath}/api/auth/saml/logout`; // Prepend basePath
    }, []);

    // Start Confirmation Dialog sequence
    const startConfirmation = useCallback(() => {
        setCountdown(confirmationConfig.seconds);
        setShowConfirmation(true);

        if (countdownIntervalRef.current) clearInterval(countdownIntervalRef.current);
        if (confirmationTimerRef.current) clearTimeout(confirmationTimerRef.current);

        // Start visual countdown
        countdownIntervalRef.current = setInterval(() => {
            setCountdown((prev) => (prev > 0 ? prev - 1 : 0));
        }, 1000);

        // Start automatic logout timer
        confirmationTimerRef.current = setTimeout(() => {
            handleLogout();
        }, confirmationConfig.ms);
    }, [handleLogout]);

    // Reset the main inactivity timer (due to user activity)
    const resetInactivityTimer = useCallback(() => {

        // Clear all potentially active timers
        if (inactivityTimerRef.current) clearTimeout(inactivityTimerRef.current);
        if (confirmationTimerRef.current) clearTimeout(confirmationTimerRef.current);
        if (countdownIntervalRef.current) clearInterval(countdownIntervalRef.current);

        setShowConfirmation(false);

        // Only start a new timer if the user is actually authenticated
        if (isAuthenticated) {
            inactivityTimerRef.current = setTimeout(startConfirmation, inactivityConfig.ms); // Use ms value
        }
    }, [isAuthenticated, startConfirmation]); // Depends on auth state and the stable startConfirmation

    useEffect(() => {
        const handleActivity = () => {
            resetInactivityTimer();
        };

        // Reduced list of less noisy events
        const eventListeners: Array<any> = ['keydown', 'click', 'touchstart'];

        // Setup runs only when isAuthenticated changes to true
        if (isAuthenticated) {
            eventListeners.forEach(event => window.addEventListener(event, handleActivity));
            resetInactivityTimer(); // Start the timer initially

            // Return the cleanup function for when user logs out OR component unmounts
            return () => {
                if (inactivityTimerRef.current) clearTimeout(inactivityTimerRef.current);
                if (confirmationTimerRef.current) clearTimeout(confirmationTimerRef.current);
                if (countdownIntervalRef.current) clearInterval(countdownIntervalRef.current);
                eventListeners.forEach(event => window.removeEventListener(event, handleActivity));
                setShowConfirmation(false); // Ensure modal is closed on cleanup
            };
        }

    }, [isAuthenticated, resetInactivityTimer]);

    // --- Modal Action Handlers ---
    const handleStayLoggedIn = useCallback(() => {
        resetInactivityTimer();
    }, [resetInactivityTimer]);

    return (
        <Dialog
            open={showConfirmation}
            // Closing the dialog via backdrop or escape key should mean "Stay Logged In"
            onClose={handleStayLoggedIn}
            disableEscapeKeyDown={false} // Allow escape key to close = stay logged in
            aria-labelledby="inactivity-dialog-title"
            aria-describedby="inactivity-dialog-description"
        >
            <DialogTitle id="inactivity-dialog-title">
                Session Timeout Warning
            </DialogTitle>
            <DialogContent>
                <DialogContentText id="inactivity-dialog-description">
                    You&apos;ve been inactive for {Math.ceil(inactivityConfig.seconds / 60)} minutes . You will be logged out
                    automatically in {countdown} seconds.
                </DialogContentText>
            </DialogContent>
            <DialogActions sx={{padding: '16px 24px'}}>
                <Button onClick={handleLogout} color="warning" variant="outlined">
                    Log Out Now
                </Button>
                <Button onClick={handleStayLoggedIn} variant="contained" color="primary" autoFocus>
                    Stay Logged In
                </Button>
            </DialogActions>
        </Dialog>
    );
};