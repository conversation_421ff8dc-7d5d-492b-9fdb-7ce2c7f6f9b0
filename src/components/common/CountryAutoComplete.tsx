import { Autocomplete, TextField } from "@mui/material";
import React from "react";
import { COUNTRIES } from "@/constants/constants"; // Corrected import order

interface CountryAutoCompleteProps {
    formik: any;
}

function CountryAutoComplete({ formik }: CountryAutoCompleteProps) {

    return (
        <Autocomplete
            disablePortal
            options={COUNTRIES?.map((p) => p.code) ?? []}
            getOptionLabel={(countryCode: string) => {
                return COUNTRIES.find((p) => p.code === countryCode)?.label ?? "";
            }}
            sx={{ mr: 2, width: "100%" }}
            size="small"
            value={formik.values.countryCode}
            onChange={(e, value) => {
                formik.setFieldValue("countryCode", value === null ? formik.initialValues.countryCode : value);
            }}
            renderInput={(params) => (
                <TextField
                    {...params}
                    label="Country"
                    variant="outlined"
                    name="countryCode"
                    onBlur={formik.handleBlur}
                    error={formik.touched.countryCode && Boolean(formik.errors.countryCode)}
                    helperText={formik.touched.countryCode && formik.errors.countryCode}
                />
            )}
        />
    );
}

export default CountryAutoComplete;