"use client";
import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import InputBase from '@mui/material/InputBase';
import SearchIcon from '@mui/icons-material/Search';
import Popper from '@mui/material/Popper';
import Paper from '@mui/material/Paper';
import Box from '@mui/material/Box';
import Alert from '@mui/material/Alert';
import {alpha, styled} from '@mui/material/styles';
import {ClickAwayListener, debounce, Typography} from '@mui/material';
import {CATALOG_TILES} from "@/constants/catalogs";
import CatalogCard from "@/components/common/CatalogCard";


const ALL_CATALOG_ITEMS: any[] = Object.values(CATALOG_TILES || {}).flatMap(
    categoryValue => (categoryValue && Array.isArray(categoryValue.items)) ? categoryValue.items : []
);


const Search = styled('div')(({theme}) => ({
    position: 'relative',
    borderRadius: theme.shape.borderRadius,
    backgroundColor: alpha(theme.palette.common.white, 0.15),
    '&:hover': {
        backgroundColor: alpha(theme.palette.common.white, 0.25),
    },
    width: '100%',
    [theme.breakpoints.up('sm')]: {
        width: 'auto',
    },
}));

const SearchIconWrapper = styled('div')(({theme}) => ({
    padding: theme.spacing(0, 2),
    height: '100%',
    position: 'absolute',
    pointerEvents: 'none',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
}));

const StyledInputBase = styled(InputBase)(({theme}) => ({
    color: 'inherit',
    width: '100%',
    '& .MuiInputBase-input': {
        padding: theme.spacing(1, 1, 1, 0),
        paddingLeft: `calc(1em + ${theme.spacing(4)})`,
        transition: theme.transitions.create('width'),
        width: '100%', // Ensure it takes full width on mobile
        [theme.breakpoints.up('md')]: { // Adjust breakpoint as needed
            width: '25ch', // Wider on larger screens
        },
    },
}));


function SearchAppBar() {
    const [searchQuery, setSearchQuery] = useState(''); // Renamed from searchTerm
    const [searchResults, setSearchResults] = useState<any[]>([]);
    const [open, setOpen] = useState(false);
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const searchContainerRef = useRef<HTMLDivElement>(null);


    const filteredResults = useMemo(() => {
        const query = searchQuery.trim(); // Use searchQuery state
        if (!query) {
            return [];
        }

        const lowerCaseSearchTerm = query.toLowerCase();

        return ALL_CATALOG_ITEMS.filter((item: any) => {
            const lowerCaseName = item.name?.toLowerCase() || '';
            const lowerCaseDescription = item.description?.toLowerCase() || '';

            if (lowerCaseName.includes(lowerCaseSearchTerm) || lowerCaseDescription.includes(lowerCaseSearchTerm)) {
                return true;
            }

            // Check options (if they exist)
            if (item.options && Array.isArray(item.options)) {
                for (const option of item.options) {
                    const lowerCaseOptionName = option.name?.toLowerCase() || '';
                    const lowerCaseOptionDescription = option.description?.toLowerCase() || '';
                    if (lowerCaseOptionName.includes(lowerCaseSearchTerm) || lowerCaseOptionDescription.includes(lowerCaseSearchTerm)) {
                        return true; // Match found in option
                    }
                }
            }
            return false;
        });
    }, [searchQuery]);

    const debouncedFilter = useCallback(
        debounce(() => {
            const results = filteredResults; // Get the memoized results
            setSearchResults(results);
            // Open popper only if there's a query and results exist
            setOpen(searchQuery.trim() !== '');
            // Ensure anchorEl is set if opening
            if (searchQuery.trim() !== '' && results.length > 0 && !anchorEl && searchContainerRef.current) {
                setAnchorEl(searchContainerRef.current);
            } else if (searchQuery.trim() === '') {
                // Close if query becomes empty
                setOpen(false);
            }
        }, 150), // Adjust debounce delay (e.g., 300ms)
        [searchQuery, filteredResults, anchorEl] // Dependencies for the debounce callback
    );

    useEffect(() => {
        debouncedFilter();
        // Cleanup the debounce timer if component unmounts or dependencies change
        return () => {
            debouncedFilter.clear();
        };
    }, [searchQuery, debouncedFilter]); // Run effect when query changes


    const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const newQuery = event.target.value;
        setSearchQuery(newQuery);
        // Set anchor on input interaction if not already set
        if (!anchorEl && searchContainerRef.current) {
            setAnchorEl(searchContainerRef.current);
        }
    };

    const handleClickAway = () => {
        setOpen(false);
        setSearchQuery('');
    };


    const handleFocus = (event: React.FocusEvent<HTMLInputElement>) => {
        if (searchContainerRef.current) {
            setAnchorEl(searchContainerRef.current);
        }
        // Re-open if focused and there are results for the current query
        if (searchQuery.trim()) {
            setOpen(true);
        }
    };


    return (

        <ClickAwayListener onClickAway={handleClickAway}>
            <div>
                <Search ref={searchContainerRef}> {/* Attach ref here */}
                    <SearchIconWrapper>
                        <SearchIcon/>
                    </SearchIconWrapper>
                    <StyledInputBase
                        placeholder="Search for apps and services" // Updated placeholder
                        inputProps={{'aria-label': 'search catalog'}}
                        value={searchQuery}
                        onChange={handleInputChange}
                        onFocus={handleFocus} // Handle focus to set anchor/reopen
                    />
                </Search>
                <Popper
                    open={open && !!anchorEl}
                    anchorEl={anchorEl}
                    placement="bottom-end" // Align to the end of the search box
                    disablePortal // Keep in DOM flow for ClickAwayListener
                    modifiers={[
                        {
                            name: 'preventOverflow', // Prevent Popper from being cut off by viewport edges
                            options: {
                                boundary: 'clippingParents', // Check against nearest scrollable ancestors
                            },
                        },
                        {
                            name: 'offset', // Add a small gap between search input and Popper
                            options: {
                                offset: [0, 4], // [skidding, distance]
                            },
                        },
                    ]}
                    sx={{
                        zIndex: (theme) => theme.zIndex.drawer + 1, // Ensure it's above AppBar/Drawers
                        width: anchorEl ? Math.max(anchorEl.clientWidth, 320) : 320, // Use anchor width or min width
                        maxWidth: '90vw', // Prevent excessive width on small screens
                        mt: 0.5,
                    }}
                >
                    <Paper elevation={10}>
                        <Box sx={{
                            p: 1,
                            display: 'grid',
                            gap: 1,
                            maxHeight: '60vh',
                            overflowY: 'auto',
                        }}>
                            <Typography variant="caption" gutterBottom sx={{display: 'block'}}>
                                {searchResults.length} Results Found
                            </Typography>
                            {searchResults.length > 0 ? (

                                searchResults.map((item, index) => (
                                    <div key={item.id || index}>

                                        <CatalogCard
                                            key={index}
                                            name={item.name}
                                            path={item.path}
                                            iconPath={item.image}
                                            subtext={item.subtext}
                                            show_options={item.show_options}
                                            options={item.options}
                                            description={item.description}
                                            external_url={item.external_url}
                                            layout={"horizontal"}
                                        />
                                    </div>
                                ))

                            ) : (
                                searchQuery && (
                                    <Box sx={{
                                        p: 1,
                                        display: 'grid',
                                        gap: 1,
                                    }}>
                                        <Alert severity="error">No results
                                            found for
                                            {searchQuery}</Alert>
                                    </Box>
                                )
                            )
                            }
                        </Box>
                    </Paper>
                </Popper>
            </div>

        </ClickAwayListener>
    )
}

export default SearchAppBar;