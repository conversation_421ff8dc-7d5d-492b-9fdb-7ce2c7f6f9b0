import React, {useState} from 'react';
import {Box, Link} from '@mui/material';
import * as he from 'he';
import {
    DataGrid,
    GridColDef,
    GridPagination,
    GridRenderCellParams,
    GridToolbarColumnsButton,
    GridToolbarContainer,
    GridToolbarDensitySelector,
    GridToolbarExport,
    GridToolbarFilterButton,
    GridToolbarQuickFilter,
} from "@mui/x-data-grid";
import {usePathname} from "next/navigation";
import FirmDetailsPage from "@/pages/data-product/address-book/firm-details";

interface Header {
    label: string;
    key: string;
    flex: number;
}

interface RowData {
    [key: string]: any
}

interface CustomTableProps {
    system: string;
    headers: Header[];
    data: RowData[];
    rowsPerPageOptions?: number[];
}


const PaginatedTable: React.FC<CustomTableProps> = ({
                                                        system,
                                                        headers,
                                                        data,
                                                        rowsPerPageOptions = [10, 25, 50]
                                                    }) => {
    const [drawerOpen, setDrawerOpen] = useState(false);
    const [firmRowId, setFrimRowIdState] = useState('');

    const pathName = usePathname();

    function generateExportFileName() {
        return pathName != null ? pathName.split('/').pop() : "export";
    }


    function CustomToolbar() {
        return (
            <GridToolbarContainer>
                <GridToolbarColumnsButton/>
                <GridToolbarFilterButton/>
                <GridToolbarDensitySelector
                    slotProps={{tooltip: {title: 'Change density'}}}
                />
                <GridToolbarExport csvOptions={{fileName: generateExportFileName()}}
                                   printOptions={{disableToolbarButton: true}}
                                   slotProps={{
                                       tooltip: {title: 'Export data'},
                                   }}
                />

                <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    flexGrow: 6,
                }}><GridToolbarQuickFilter debounceMs={500} variant="outlined" size='small' fullWidth
                                           sx={{width: '80%'}}/></Box>
                <GridPagination/>

            </GridToolbarContainer>
        );
    }

    const handleFirmClick = (rowId: string) => {
        setFrimRowIdState(rowId);
        setDrawerOpen(true);
    };

    const handleDrawerClose = () => {
        setDrawerOpen(false);
        setFrimRowIdState('');
    };


    const dataWithIDs = data.map((row, index) => ({...row, _id: row.id || index}));

    const columns: GridColDef[] = headers.map(header => ({
            field: header.key,
            headerName: header.label,
            flex: header.flex, // Makes columns flexible
            renderCell: (params: GridRenderCellParams) => {

                // Log current header key and row data for debugging
                if (header.key === 'term' && system === 'alation') {
                    return (
                        <Link href={params.row.term_url} underline="hover" target="_blank" rel="noreferrer">
                            {he.decode(params.value)}
                        </Link>
                    );
                }

                if (header.key === 'name' && system === 'alation') {
                    return (
                        <Link href={params.row.url} underline="hover" target="_blank" rel="noreferrer">
                            {he.decode(params.value)}
                        </Link>
                    );
                }

                if (header.key === 'glossaries' && system === 'alation') {
                    return (
                        <>
                            {Array.isArray(params.value) ?
                                params.value.map((glossary: any) => (
                                    <p key={glossary.glossary_id}>
                                        <Link href={glossary.glossary_url} underline='hover' target='_blank'
                                              rel='noreferrer'>
                                            {glossary.glossary}
                                        </Link>
                                    </p>
                                )) : null
                            }
                        </>
                    );
                }

                if ((header.key === 'data' || header.key === 'schema' || header.key === 'table') && system === 'alation') {


                    const matchingValue = params.row.breadcrumbs.find((obj: {
                        type: string;
                    }) => obj.type === header.key)
                    return (
                        <>
                            {matchingValue != null ? header.key === 'data' ?
                                <Link href={matchingValue.url} underline='hover' target='_blank'
                                      rel='noreferrer'>
                                    {matchingValue.title}
                                </Link> : <Link href={matchingValue.url} underline='hover' target='_blank'
                                                rel='noreferrer'>
                                    {matchingValue.name}
                                </Link> : ""
                            }
                        </>
                    );
                }

                if (header.key === 'name' && system !== 'alation') {
                    return (
                        <Link href={params.row.objectUrl} underline="hover" target="_blank" rel="noreferrer">
                            {he.decode(params.value)}
                        </Link>
                    );
                }
                if (header.key === 'parentName' && system !== 'alation') {
                    return (
                        <Link href={params.row.parentUrl} underline="hover" target="_blank" rel="noreferrer">
                            {he.decode(params.value)}
                        </Link>
                    );
                }

                if (header.key === 'systemName' && system !== 'alation') {
                    return (
                        <Link href={params.row.systemUrl} underline="hover" target="_blank" rel="noreferrer">
                            {he.decode(params.value)}
                        </Link>
                    );
                }
                if (header.key === 'physicalFieldResource' && system !== 'alation') {
                    return (
                        <Link href={params.row.physicalFieldResourceUrl} underline="hover" target="_blank"
                              rel="noreferrer">
                            {he.decode(params.value)}
                        </Link>
                    );
                }

                if (header.key === 'physicalFieldTable' && system !== 'alation') {
                    return (
                        <Link href={params.row.physicalFieldTableUrl} underline="hover" target="_blank"
                              rel="noreferrer">
                            {he.decode(params.value.substring(params.value.lastIndexOf('/') + 1))}
                        </Link>
                    );
                }

                if (header.key === 'fieldResourceType' && system !== 'alation') {
                    return 'JDBC';
                }

                if (header.key === 'fieldType' && system !== 'alation') {
                    return 'Column';
                }

                if (system == 'addressbook' && header.key === 'stndrdz_prmry_name') {
                    return (
                        <Link href="#" onClick={() => handleFirmClick(params.row.rowid_object)}>
                            {he.decode(params.value)}
                        </Link>
                    );
                }

                return params.value != null ? he.decode(String(params.value).replace(/<\/?[^>]+(>|$)/g, "")) : "";
            },
            valueFormatter: (value: any, row: any) => {
                if (header.key === 'glossaries' && system === 'alation') {
                    let glossaryString: string = '';

                    if (Array.isArray(value)) {
                        let count = 0;
                        value.forEach((glossary: any) => {
                                glossaryString = glossaryString + glossary.glossary
                                if (value.length - 1 > count) {
                                    glossaryString = glossaryString + '\r\n'
                                }
                                count++
                            }
                        )
                    }
                    return glossaryString;
                }

                if ((header.key === 'data' || header.key === 'schema' || header.key === 'table') && system === 'alation') {

                    const matchingValue = row.breadcrumbs.find((obj: {
                        type: string;
                    }) => obj.type === header.key)

                    if (matchingValue != null) {
                        if (header.key === 'data') {
                            return matchingValue.title;
                        }
                        return matchingValue.name
                    }
                    return "";
                }

                if (header.key === 'physicalFieldTable' && system !== 'alation') {
                    return he.decode(value.substring(value.lastIndexOf('/') + 1));
                }

                if (header.key === 'fieldResourceType' && system !== 'alation') {
                    return 'JDBC';
                }

                if (header.key === 'fieldType' && system !== 'alation') {
                    return 'Column';
                }

                return value != null ? he.decode(String(value).replace(/<\/?[^>]+(>|$)/g, "")) : "";
            }
        }
    ))


    return (
        <Box>
            <DataGrid getRowHeight={() => 'auto'} getEstimatedRowHeight={() => 200}
                      slots={{toolbar: CustomToolbar}}
                      initialState={{pagination: {paginationModel: {pageSize: 10}}}}
                      pageSizeOptions={rowsPerPageOptions}
                      rows={dataWithIDs}
                      columns={columns}
                      rowCount={data.length}
                      getRowId={(row:any) => row._id}
                      disableRowSelectionOnClick
                      hideFooterPagination
            />
            <FirmDetailsPage open={drawerOpen} onClose={handleDrawerClose} firmRowId={firmRowId}></FirmDetailsPage>
        </Box>

    );
}


export default PaginatedTable;