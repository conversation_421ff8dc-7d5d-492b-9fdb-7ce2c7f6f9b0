import React, {useEffect, useState} from 'react';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import Typography from "@mui/material/Typography";

function SecurityWarning() {
    const [open, setOpen] = useState(false); // Initialize to false

    useEffect(() => {
        const checkAcceptance = () => {
            const storedAcceptanceDate = localStorage.getItem('securityWarningAcceptedDate');

            if (storedAcceptanceDate) {
                const now = Date.now();
                const diffInDays = Math.floor((now - +(storedAcceptanceDate)) / (1000 * 60 * 60 * 24));
                if (diffInDays >= 1) {
                    setOpen(true); // Show the dialog if it's been a day or more
                } else {
                    setOpen(false);  // Keep the dialog closed
                }
            } else {
                setOpen(true); // Show the dialog if there's no stored acceptance date
            }
        };

        checkAcceptance(); // Check on initial load

    }, []); // Empty dependency array:  runs only on mount (and the cleanup function on unmount)

    const handleClose = () => {
        // Do nothing on close (prevent closing without accepting)
    };

    const handleAccept = () => {
        localStorage.setItem('securityWarningAcceptedDate', String(Date.now().valueOf())); // Store the date of acceptance
        setOpen(false);
    };

    if (open) {  // Only render the dialog if 'open' is true
        return (
            <Dialog
                open={open}
                onClose={handleClose}
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
                maxWidth="md"
                sx={{
                    '& .MuiBackdrop-root': {
                        backgroundColor: '#303030' // Try to remove this to see the result
                    }
                }}

            >
                <DialogTitle id="alert-dialog-title">
                    <Typography variant='h4' fontWeight='bold'> Security Warning</Typography>
                    <Typography variant='h5' fontWeight='bold'>Welcome to FDA Data Concierge</Typography>
                </DialogTitle>
                <DialogContent sx={{color: 'black', fontFamily: 'Roboto', fontSize: '0.95rem', lineHeight: 'normal'}}>
                    <ul>
                        <li>This warning banner provides privacy and security notices consistent with applicable
                            federal laws, directives, and other federal guidance for accessing this Government
                            system, which includes
                            (1) this computer network, (2) all computers connected to this network, and (3) all
                            devices
                            and storage media attached to this network or to a computer on this network.
                        </li>
                        <li>
                            This system is provided for Government-authorized use only.
                        </li>
                        <li>
                            Unauthorized or improper use of this system is prohibited and may result in disciplinary
                            action and/or civil and criminal penalties.
                        </li>
                        <li>
                            Personal use of social media and networking sites on this system is limited as to not
                            interfere with official work duties and is subject to monitoring.
                        </li>
                        <li>By using this system, you understand and consent to the following:
                            <ul>
                                <li>The Government may monitor, record, and audit your system usage, including usage
                                    of personal devices and email systems for official duties or to conduct HHS
                                    business.
                                    Therefore, you have no reasonable expectation of privacy regarding any
                                    communication or data
                                    transiting or stored on this system. At any time, and for any lawful Government
                                    purpose, the government may
                                    monitor, intercept, and search and seize any communication or data transiting or
                                    stored on this system.
                                </li>
                                <li>Any communication or data transiting or stored on this system may be disclosed
                                    or used for any lawful Government purpose.
                                </li>
                            </ul>

                        </li>

                    </ul>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleAccept} color="primary" variant="contained">
                        I AGREE
                    </Button>
                </DialogActions>
            </Dialog>
        );
    }
    return null; // Return null when the dialog is not supposed to be displayed
}

export default SecurityWarning;