// components/InfoCard.tsx
import React, {ReactNode} from 'react';
import {makeStyles} from "@mui/styles";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import {Breadcrumbs, Divider, Link, Link as NextLink} from "@mui/material";

interface InfoCardProps {
    icon: ReactNode;
    header: string;
    description: string;
    breadcrumbs?: BreadcrumbItem[];
    HeaderFunctions?: React.ReactNode
}

interface BreadcrumbItem {
    label: string;
    href: string;
}

const useStyles = makeStyles({
    cardBox: {
        padding: 8,
        marginBottom: 10
    },
    textContent: {
        fontFamily: 'Roboto',
        fontSize: '1rem',
    },
    outerContainer: {
        display: "flex",
        flexDirection: "row",
        alignItems: "center",
        marginLeft: '-7px'
    }
});

const InfoCard: React.FC<InfoCardProps> = ({icon, header, description, breadcrumbs, HeaderFunctions}) => {
    const classes = useStyles();
    return (
        <div className={classes.cardBox}>
            <Box sx={{padding: 0.5}}>
                {breadcrumbs && (

                    <Breadcrumbs aria-label="breadcrumb" separator="›">
                        {breadcrumbs.map((item: { href: any; label: any; }, index: React.Key | null | undefined) => (
                            item.href !== "" ? <Link
                                component={NextLink}
                                key={index}
                                href={process.env.NEXT_PUBLIC_BASE_PATH + item.href}
                                underline="hover"
                            >
                                {item.label}
                            </Link> : <Typography key={index} sx={{
                                color: 'text.primary',
                                fontSize: '0.85rem'
                            }}>{item.label}</Typography>
                        ))}
                    </Breadcrumbs>
                )}
            </Box>

            <Box className={classes.outerContainer}>
                <Box
                    component="img"
                    src={typeof icon === 'string' ? icon : ''}
                    sx={{
                        width: 60,
                        height: 60,
                        objectFit: 'contain',
                        padding: 1
                    }}/>
                <Typography variant="h5" sx={{
                    marginTop: 1,
                    marginBottom: 1, color: '#222C67', fontWeight: 'bold'
                }}>{header}{HeaderFunctions}</Typography>

            </Box>
            <Divider/>
            <Typography sx={{
                marginTop: 1,
                marginBottom: 1.5,
                textAlign: 'left',
                width: '90%'
            }} className={classes.textContent}>
                {description}

            </Typography>
        </div>
    )

};

export default InfoCard;