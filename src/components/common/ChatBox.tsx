import React, {useEffect, useRef, useState} from 'react';
import {
    <PERSON>,
    Button,
    Collapse,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    IconButton,
    List,
    TextField,
    Typography,
} from '@mui/material';
import SendIcon from '@mui/icons-material/Send';
import CloseIcon from '@mui/icons-material/Close';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import {makeStyles} from '@mui/styles';
import moment from 'moment';


// Define a type for the messages
type Message = {
    text: string;
    sender: string;
    timestamp: number;
};

// makeStyles for styling
const useStyles = makeStyles((theme: any) => ({
    userMessageBubble: {
        alignItems: 'flex-end',
        backgroundColor: 'rgb(34 44 103)',
        color: theme.palette.primary.contrastText,
        borderRadius: '8px 8px 8px 8px',
        padding: theme.spacing(1, 2),
        maxWidth: '75%',
        alignSelf: 'flex-end',
        position: 'relative',
    },
    botMessageBubble: {
        alignItems: 'flex-start',
        backgroundColor: theme.palette.grey[300],
        color: theme.palette.text.primary,
        borderRadius: '8px 8px 8px 8px',
        padding: theme.spacing(1, 2),
        maxWidth: '75%',
        alignSelf: 'flex-start',
        position: 'relative',
    },
    messageMeta: {
        fontSize: '0.8rem',
        color: theme.palette.text.secondary,
    },
    userMeta: {
        textAlign: 'right',
    },
    botMeta: {
        textAlign: 'left',
    },
    messageContainer: {
        display: 'flex',
        flexDirection: 'column',
        width: '100%',
        margin: '5px auto'
    },
}));

function ChatBox() {
    const classes = useStyles();
    const [open, setOpen] = useState(false);
    const [input, setInput] = useState('');
    const [messages, setMessages] = useState<Message[]>([
        {text: "Welcome to FDA Data Concierge! How can I assist you today?", sender: 'bot', timestamp: Date.now()},
    ]);
    const [expanded, setExpanded] = useState(true);
    const messagesEndRef = useRef<HTMLDivElement>(null);

    const handleOpen = () => {
        setOpen(true);
        if (messagesEndRef.current) {
            messagesEndRef.current.scrollIntoView({behavior: 'smooth'});
        }
    };

    const handleClose = (event: any, reason: string) => {
        if (reason !== 'backdropClick') {
            setOpen(false)
            resetValues()
        }
    };

    const handleInputChange = (event: { target: { value: React.SetStateAction<string>; }; }) => {
        setInput(event.target.value);
    };

    const handleSendMessage = async () => {
        if (input.trim() === '') return;

        const userMessage: Message = {text: input, sender: 'user', timestamp: Date.now()};
        setMessages([...messages, userMessage]);
        setInput('');

        try {
            const response = await simulateApiResponse(input);
            const botMessage: Message = {text: response, sender: 'bot', timestamp: Date.now()};
            setMessages((prevMessages) => [...prevMessages, botMessage]);

        } catch (error) {
            console.error("Error fetching response:", error);
            const errorMessage: Message = {
                text: "Sorry, I couldn't process your request.",
                sender: 'bot',
                timestamp: Date.now()
            };
            setMessages(prevMessages => [...prevMessages, errorMessage]);
        }
    };

    const simulateApiResponse = (userQuery: string) => {
        return new Promise<string>((resolve) => {
            setTimeout(() => {
                const lowerCaseQuery = userQuery.toLowerCase();
                if (lowerCaseQuery.includes("hello")) {
                    resolve("Hi there! How can I help you today?");
                } else if (lowerCaseQuery.includes("how are you")) {
                    resolve("I'm doing well, thank you!  How about you?");
                } else if (lowerCaseQuery.includes("help")) {
                    resolve("I can help with a variety of things.  Ask me a question!");
                } else if (lowerCaseQuery.includes("goodbye")) {
                    resolve("See You");
                } else {
                    resolve("I'm still learning.  Could you rephrase your question?");
                }
            }, 500);
        });
    };

    const handleKeyDown = (event: { key: string; shiftKey: any; preventDefault: () => void; }) => {
        if (event.key === 'Enter' && !event.shiftKey) {
            event.preventDefault();
            handleSendMessage();
        }
    };

    const toggleExpanded = () => {
        setExpanded(!expanded);
    };

    useEffect(() => {
        if (messagesEndRef.current) {
            messagesEndRef.current.scrollIntoView({behavior: 'smooth'});
        }
    }, [messages]);

    function resetValues(){
        setInput('');
        setMessages([
            {text: "Welcome to FDA Data Concierge! How can I assist you today?", sender: 'bot', timestamp: Date.now()},
        ]);
        setInput('');
        setExpanded(true);
    }

    return (
        <>
            <Button sx={{
                padding: "5px 5px",
                borderRadius: "5px",
                cursor: "pointer",
                border: '2px solid #007CBA',
                color: '#000000',
                lineHeight: '24px',
            }} onClick={handleOpen}>
                <Box component="img" src={process.env.NEXT_PUBLIC_BASE_PATH + "/img/ai-chat.png"}
                     sx={{
                         width: 15,
                         height: 15,
                         marginRight: '10px',
                         objectFit: 'cover',
                     }}/>
                {'How can I help you?'}
            </Button>

            <Dialog
                open={open}
                onClose={handleClose}
                slotProps={{
                    paper: {
                        sx: {
                            position: "fixed",
                            bottom: 30,
                            right: 30,
                            width: {xs: '90%', sm: 350},
                            maxWidth: 400, // Maximum width
                            height: expanded ? '60%' : 'auto',
                            maxHeight: '60%',
                            m: 0,
                            borderRadius: '12px',
                            overflow: 'hidden',
                        }
                    }
                }}
            >
                <DialogTitle
                    sx={{display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '5px'}}>
                    <Box component="img" src={process.env.NEXT_PUBLIC_BASE_PATH + "/img/ai-chat.png"}
                         sx={{
                             width: 15,
                             height: 15,
                             marginRight: '10px',
                             objectFit: 'cover',
                         }}/>How Can I Help You?
                    <IconButton onClick={()=>handleClose(event,'')}><CloseIcon/></IconButton>
                </DialogTitle>

                <DialogContent dividers sx={{padding: 1, overflowY: 'hidden'}}>
                    <Collapse in={expanded} timeout="auto" unmountOnExit>
                        <List sx={{
                            overflowY: 'auto',
                            maxHeight: 450,
                            paddingTop: 0,
                            paddingBottom: 0,
                            display: 'flex',
                            flexDirection: 'column',
                            marginTop: 1
                        }}>
                            {messages.map((message, index) => (
                                // Use the container class here
                                <Box key={index}
                                     className={classes.messageContainer}>
                                    {message.sender === 'user' ? (
                                        <Box className={classes.userMessageBubble}>
                                            <Typography sx={{fontSize: '0.85rem'}}>{message.text}</Typography>
                                        </Box>
                                    ) : (
                                        <Box className={classes.botMessageBubble}>
                                            <Typography sx={{fontSize: '0.85rem'}}>{message.text}</Typography>
                                        </Box>
                                    )}
                                    {/* Meta data is now outside the bubble */}
                                    <Typography
                                        className={`${classes.messageMeta} ${message.sender === 'user' ? classes.userMeta : classes.botMeta}`}>
                                        {message.sender === 'user' ? 'You' : 'Bot'}, {moment(message.timestamp).format('MMM D, h:mm A')}
                                    </Typography>
                                </Box>
                            ))}
                            <div ref={messagesEndRef}/>
                        </List>
                    </Collapse>
                </DialogContent>

                <DialogActions sx={{padding: 1}}>
                    <IconButton onClick={toggleExpanded} sx={{marginRight: 'auto'}}>
                        {expanded ? <ExpandMoreIcon/> : <ExpandLessIcon/>}
                    </IconButton>
                    <TextField fullWidth variant="outlined" placeholder="Type your message..." value={input}
                               onChange={handleInputChange} onKeyDown={handleKeyDown} sx={{marginRight: 1}}
                               size="small"/>
                    <IconButton color="primary" onClick={handleSendMessage}><SendIcon/></IconButton>
                </DialogActions>
            </Dialog>
        </>
    );
}

export default ChatBox;