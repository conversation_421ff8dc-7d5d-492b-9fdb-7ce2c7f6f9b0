'use client';

import React, {useState} from 'react';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import Button from '@mui/material/Button';
import Rating from '@mui/material/Rating';
import TextField from '@mui/material/TextField';
import Box from '@mui/material/Box';
import IconButton from '@mui/material/IconButton';
import Tooltip from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
import HelpIcon from '@mui/icons-material/Help';
import CloseIcon from '@mui/icons-material/Close';
import FormControlLabel from '@mui/material/FormControlLabel';
import {Divider, Link, List, ListItem, ListItemButton, ListItemIcon, ListItemText, Switch} from "@mui/material";
import LiveHelpIcon from '@mui/icons-material/LiveHelp';
import ArticleIcon from '@mui/icons-material/Article';
import ContactSupportIcon from '@mui/icons-material/ContactSupport';
import FeedbackIcon from '@mui/icons-material/Feedback';
import {FDA_SERVICENOW_SUPPORT_URL} from "@/constants/constants";
import appService from "@/services/app.service";


export default function HelpAndFeedback() {
    const [open, setOpen] = useState(false);
    const [buttonVisible, setButtonVisible] = useState(true);
    const [showFeedbackForm, setShowFeedbackForm] = useState(false);
    const [rating, setRating] = useState(null);
    const [feedbackText, setFeedbackText] = useState('');
    const [submitted, setSubmitted] = useState(false);

    const handleClickOpen = () => {
        setOpen(true);
        setShowFeedbackForm(false);
        setSubmitted(false);
    };

    const handleCloseDialog = () => {
        setOpen(false);
    };

    const handleCloseButton = (event: any) => {
        event.stopPropagation();
        setButtonVisible(false);
    };

    const handleToggleFeedbackForm = (event: any) => {
        setShowFeedbackForm(event.target.checked);
        setSubmitted(false); // Hide thank you message if toggling feedback off/on again
    };

    const handleRatingChange = (event: any, newValue: any) => {
        setRating(newValue);
    };

    const handleFeedbackTextChange = (event: any) => {
        setFeedbackText(event.target.value);
    };

    const handleSubmitFeedback = async () => {
        const feedback = {
            feedbackText: feedbackText,
            rating: rating
        }
        await appService.saveFeedBack(feedback).then(() => {
            setSubmitted(true);

            setRating(null);
            setFeedbackText('');

            setTimeout(() => {
                handleCloseDialog();
            }, 2500);
        })


    };

    if (!buttonVisible) {
        return null;
    }

    return (
        <>
            <Tooltip title="Help and Feedback" placement="bottom-end">
                <Button variant="contained" size="small" sx={{
                    backgroundColor: 'transparent', "&:hover": {
                        backgroundColor: 'rgb(67 75 126 / 83%)',
                    }
                }}
                        aria-label="feedback"
                        onClick={handleClickOpen}
                        startIcon={<HelpIcon/>}> Support
                </Button>
            </Tooltip>
            <Dialog
                open={open}
                onClose={handleCloseDialog}
                aria-labelledby="help-feedback-dialog-title"
                maxWidth="sm"
                fullWidth
            >
                <DialogTitle sx={{color: '#222222'}} id="help-feedback-dialog-title">
                    Need Assistance?
                    <IconButton
                        aria-label="close dialog"
                        onClick={handleCloseDialog}
                        sx={{
                            position: 'absolute', right: 8, top: 8,
                            color: (theme) => theme.palette.grey[500],
                        }}
                    >
                        <CloseIcon/>
                    </IconButton>
                </DialogTitle>

                <DialogContent dividers>
                    <Typography variant="body2" sx={{mb: 2, fontWeight: 'bold'}}>
                        Find answers or get in touch with our support team.
                    </Typography>
                    <List dense disablePadding>
                        <ListItem disablePadding>
                            <ListItemButton component={Link} href="/faq" target="_blank" rel="noopener noreferrer">
                                <ListItemIcon sx={{minWidth: '40px'}}>
                                    <LiveHelpIcon/>
                                </ListItemIcon>
                                <ListItemText primary="Frequently Asked Questions"/>
                            </ListItemButton>
                        </ListItem>
                        <ListItem disablePadding>
                            <ListItemButton component={Link} href="/docs" target="_blank" rel="noopener noreferrer">
                                <ListItemIcon sx={{minWidth: '40px'}}>
                                    <ArticleIcon/>
                                </ListItemIcon>
                                <ListItemText primary="Documentation"/>
                            </ListItemButton>
                        </ListItem>
                        <ListItem disablePadding>
                            <ListItemButton component={Link} target="_blank" rel="noopener noreferrer"
                                            href={FDA_SERVICENOW_SUPPORT_URL}>

                                <ListItemIcon sx={{minWidth: '40px'}}>
                                    <ContactSupportIcon/>
                                </ListItemIcon>
                                <ListItemText primary="Contact Support"/>
                            </ListItemButton>
                        </ListItem>
                    </List>

                    <Divider sx={{my: 3}}/>
                    <FormControlLabel
                        control={
                            <Switch
                                checked={showFeedbackForm}
                                onChange={handleToggleFeedbackForm}
                                name="toggleFeedback"
                            />
                        }
                        label="Would you like to provide feedback on Data Concierge?"
                        sx={{mb: 1}}
                    />

                    {showFeedbackForm && !submitted && (
                        <Box sx={{
                            mt: 2,
                            border: theme => `1px solid ${theme.palette.divider}`,
                            borderRadius: 1,
                            p: 2
                        }}>
                            <Typography variant="subtitle1" gutterBottom>
                                How would you rate your experience so far?
                            </Typography>
                            <Box sx={{display: 'flex', alignItems: 'center', mb: 3}}>
                                <Rating
                                    name="experience-rating"
                                    value={rating}
                                    onChange={handleRatingChange}
                                    size="large"
                                />
                                {rating !== null && (
                                    <Box sx={{ml: 2}}>{rating} Star{rating !== 1 ? 's' : ''}</Box>
                                )}
                                {rating === null && (
                                    <Box sx={{ml: 2, color: 'text.secondary'}}>Optional</Box>
                                )}
                            </Box>

                            <TextField
                                margin="dense"
                                id="feedback-details"
                                label="How's your experience?"
                                helperText="Tell us what you liked and how we can improve"
                                type="text"
                                fullWidth
                                variant="outlined"
                                multiline
                                rows={3}
                                value={feedbackText}
                                onChange={handleFeedbackTextChange}
                            />
                        </Box>
                    )}

                    {showFeedbackForm && submitted && (
                        <Box sx={{mt: 2, textAlign: 'center', p: 3}}>
                            <Typography variant="h6" gutterBottom color="primary">
                                Thank you for your feedback!
                            </Typography>
                            <Typography variant="body2">
                                We appreciate you helping us improve the Data Concierge application.
                            </Typography>
                        </Box>
                    )}

                </DialogContent>

                <DialogActions sx={{p: '16px 24px'}}>
                    <Button onClick={handleCloseDialog} color="inherit">
                        Close
                    </Button>
                    {showFeedbackForm && !submitted && (
                        <Button
                            onClick={handleSubmitFeedback}
                            variant="contained"
                            disabled={rating === null && feedbackText.trim() === ''}
                            startIcon={<FeedbackIcon/>}
                        >
                            Submit Feedback
                        </Button>
                    )}
                </DialogActions>
            </Dialog>
        </>
    );
}