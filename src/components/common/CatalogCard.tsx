import React from 'react';
import {
    <PERSON>,
    <PERSON><PERSON>,
    Card,
    CardContent,
    CardMedia,
    Dialog,
    DialogActions,
    DialogContent,
    DialogContentText,
    DialogTitle,
    Divider,
    Link
} from '@mui/material';
import Typography from "@mui/material/Typography";
import {makeStyles} from "@mui/styles"; // Ensure theme is configured if using theme values

export interface CatalogCardProps {
    key?: React.Key;
    name: string;
    subtext?: string;
    path: string;
    iconPath: string;
    description?: string;
    external_url?: boolean;
    show_options?: boolean;
    options?: OptionsProps[];
    layout?: 'default' | 'horizontal';
}

export interface OptionsProps {
    name: string;
    path: string;
    external_url?: boolean;
    description?: string;
}

const useStyles = makeStyles(() => ({
    cardHeading: {
        fontWeight: '500',
        fontSize: '1rem',
        fontFamily: 'Roboto',
        color: '#222C67',
        padding: '0',
        lineHeight: '1.5',
    },
    dialogItemHeading: {
        fontWeight: '500',
        fontSize: '1rem',
        fontFamily: 'Roboto',
        lineHeight: '1.5',
        color: '#0071BC',
        padding: '5px 0 0 0',
    },
    cardDescription: {
        fontWeight: '400',
        fontFamily: 'Roboto',
        fontSize: '0.95rem',
        color: '#000000',
        lineHeight: '1.5',
    },
    cardContainer: {
        display: 'flex',
        flexDirection: 'column',
        backgroundColor: '#F2F8FC',
        border: '2px solid #007CBA',
        borderRadius: '10px',
        color: '#222C67',
    },
    linkStyle: {
        textDecoration: 'none',
        color: 'inherit',
        display: 'block',
        height: '100%',
        padding: 0,
    }
}));

const CatalogCard: React.FC<CatalogCardProps> = ({
                                                     name,
                                                     path,
                                                     subtext,
                                                     external_url,
                                                     iconPath,
                                                     description,
                                                     show_options,
                                                     options,
                                                     layout = 'default'
                                                 }) => {

    const [open, setOpen] = React.useState(false);

    const handleClickOpen = () => {
        if (show_options) {
            setOpen(true);
        }
    };

    const handleClose = () => {
        setOpen(false);
    };

    const classes = useStyles();

    // Define the inner content structure based on layout
    const CardInnerContent = (
        <CardContent
            sx={{
                '&:last-child': {
                    paddingBottom: 1.5,
                },

                padding: 1.5,
                display: 'flex',
                flexDirection: 'column',
                flexGrow: 1,
                overflow: 'hidden',
                justifyContent: layout === 'default' ? undefined : 'flex-start',
                alignItems: layout === 'default' ? 'center' : 'stretch',
            }}
        >
            {layout === 'horizontal' ? (
                <Box sx={{display: 'flex', alignItems: 'flex-start', width: '100%'}}>
                    <CardMedia
                        component="img"
                        image={iconPath}
                        sx={{
                            width: '35px',
                            height: '35px',
                            objectFit: 'contain',
                            mr: 1.5,
                            mt: '2px',
                            flexShrink: 0,
                        }}
                    />
                    <Box sx={{display: 'flex', flexDirection: 'column', overflow: 'hidden', flexGrow: 1}}>
                        <Typography
                            variant="h6"
                            className={classes.cardHeading}
                            sx={{
                                // Layout-specific style
                                textAlign: 'left',
                                overflowWrap: 'break-word',
                            }}
                        >
                            {name}<br/>{subtext}
                        </Typography>

                        {description && (
                            <Typography
                                variant="body2"
                                className={classes.cardDescription}
                                sx={{
                                    textAlign: 'left',
                                    mt: 0.5,
                                }}
                            >
                                {description}
                            </Typography>
                        )}
                    </Box>
                </Box>
            ) : (
                <>
                    <CardMedia
                        component="img"
                        image={iconPath}
                        sx={{
                            width: '35px',
                            height: '35px',
                            objectFit: 'contain',
                            mb: 1,
                        }}
                    />
                    <Typography
                        variant="h6"
                        className={classes.cardHeading}
                        sx={{

                            textAlign: 'center',
                            padding: '5px 0 0 0',
                            lineHeight: 'normal'
                        }}
                    >
                        {name}<br/>{subtext}
                    </Typography>
                    {description && (
                        <Typography
                            variant="body2"
                            className={classes.cardDescription}
                            sx={{
                                // Layout-specific style
                                textAlign: 'center',
                                mt: subtext ? 0.25 : 0.5,
                            }}
                        >
                            {description}
                        </Typography>
                    )}
                </>
            )}
        </CardContent>
    );

    const CardWrapper = ({children}: { children: React.ReactNode }) => {
        if (show_options) {
            return (
                <Card className={classes.cardContainer} onClick={handleClickOpen} sx={{
                    cursor: 'pointer',
                    height: layout === 'default' ? '210px' : undefined,
                    width: layout === 'default' ? '210px' : undefined
                }}>
                    {children}
                </Card>
            );
        } else if (external_url) {
            return (
                <Link href={path} className={classes.linkStyle} target="_blank" rel="noreferrer">
                    <Card className={classes.cardContainer} sx={{
                        height: layout === 'default' ? '210px' : undefined,
                        width: layout === 'default' ? '210px' : undefined
                    }}>{children}</Card>
                </Link>
            );
        } else {
            return (
                <Link href={(process.env.NEXT_PUBLIC_BASE_PATH || '') + path} className={classes.linkStyle}>
                    <Card className={classes.cardContainer} sx={{
                        height: layout === 'default' ? '210px' : undefined,
                        width: layout === 'default' ? '210px' : undefined
                    }}>{children}</Card>
                </Link>
            );
        }
    };

    return (
        <>
            <CardWrapper>
                {CardInnerContent}
            </CardWrapper>

            {/* Dialog remains unchanged */}
            <Dialog fullWidth open={open} onClose={handleClose} aria-labelledby="alert-dialog-title"
                    aria-describedby="alert-dialog-description">
                <DialogTitle id="alert-dialog-title">
                    <Typography variant="h6" sx={{fontWeight: 500, color: '#222C67', textAlign: 'center'}}>
                        {name}
                    </Typography>
                    <Divider sx={{mt: 1}}/>
                </DialogTitle>
                <DialogContent>
                    <DialogContentText component="div" id="alert-dialog-description">
                        {options?.map((item, index) => (
                            <Box key={index}>
                                <Link
                                    style={{textDecoration: "none"}}
                                    href={!item.external_url ? (process.env.NEXT_PUBLIC_BASE_PATH || '') + item.path : item.path}
                                    target={item.external_url ? "_blank" : "_self"}
                                    rel={item.external_url ? "noreferrer" : undefined}
                                >
                                    <Typography variant="h6" className={classes.dialogItemHeading}>
                                        {item.name}
                                    </Typography>
                                </Link>
                                {item.description && (
                                    <Typography variant="body2" sx={{color: 'rgb(85, 85, 85)'}}>
                                        {item.description}
                                    </Typography>
                                )}
                                <Divider sx={{mt: 1}}/>
                            </Box>
                        ))}
                    </DialogContentText>
                </DialogContent>
                <DialogActions>
                    <Button type="button" size="medium" sx={{mr: 2}} variant="contained"
                            onClick={handleClose}>Close</Button>
                </DialogActions>
            </Dialog>
        </>
    );
};

export default CatalogCard;