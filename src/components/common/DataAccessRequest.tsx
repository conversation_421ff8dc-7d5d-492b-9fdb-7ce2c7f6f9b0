'use client'
import {makeStyles} from "@mui/styles";
import theme from "@/theme/theme";
import {
    <PERSON>ert,
    Box, Button, Container,
    FormControl, IconButton, InputLabel, MenuItem, Paper, Select, SelectChangeEvent,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow, TextField,
    Typography
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import DeleteOutlineIcon from "@mui/icons-material/DeleteOutline";
import React, {useState} from "react";
import {User} from "@/lib/types";
import {useAuthStore} from "@/store/authStore";

const useStyles = makeStyles({
        container: {
            margin: "auto",
            minWidth: "75vw",
        },
        section: {
            marginBottom: theme.spacing(2),
            padding: theme.spacing(1),
            border: `1px solid ${theme.palette.grey[300]}`,
            borderRadius: theme.shape.borderRadius, // 4px
            backgroundColor: theme.palette.background.paper,
        },
        sectionTitle: {
            marginBottom: theme.spacing(2) + '!important',
            fontWeight: 'bold !important',
            color: theme.palette.text.primary,
            fontSize: '1rem',
        },
        infoGrid: {
            marginBottom: theme.spacing(2),
        },
        infoLabel: {
            fontWeight: 500,
            color: theme.palette.text.secondary,
            fontSize: '0.875rem',
            marginBottom: theme.spacing(0.5),
        },
        infoValue: {
            padding: theme.spacing(1), // 8px
            border: `1px solid ${theme.palette.grey[400]}`,
            backgroundColor: theme.palette.grey[100],
            borderRadius: theme.shape.borderRadius,
            minHeight: 38,
            display: 'flex',
            alignItems: 'center',
            wordBreak: 'break-word',
        },
        currentAccessTable: {
            '& th, & td': {
                padding: theme.spacing(1, 2),
                borderBottom: `1px solid ${theme.palette.divider}`,
            },
            '& th': {
                textAlign: 'left',
                fontWeight: 'bold',
                backgroundColor: theme.palette.grey[100],
            }
        },
        selectContainer: {
            display: 'flex',
            alignItems: 'center',
            gap: theme.spacing(1),
            marginBottom: theme.spacing(2),
        },
        formControl: {
            minWidth: 250,
            flexGrow: 1,
        },
        justificationLabel: {
            marginBottom: theme.spacing(1) + '!important',
            display: 'block',
            fontWeight: 500,
            color: theme.palette.text.primary,
        },
        justificationHelperText: {
            marginBottom: theme.spacing(1),
        },
        dataTableContainer: {
            marginTop: theme.spacing(2),
        },
        dataTableHeader: {
            backgroundColor: theme.palette.grey[100] + '!important',
            '& th': {
                fontWeight: 'bold',
            }
        },
        actionCell: {
            width: '50px !important',
            textAlign: 'center',
            padding: theme.spacing(0.5, 1) + '!important',
        },
        tableCell: {
            padding: theme.spacing(1, 2),
            borderBottom: `1px solid ${theme.palette.divider}`,
        },
        submitButtonContainer: {
            display: 'flex',
            justifyContent: 'flex-end',
            marginTop: theme.spacing(3),
        },
        requiredAsterisk: {
            color: theme.palette.error.main,
            marginLeft: theme.spacing(0.5),
        },
        disabledLookButton: {
            backgroundColor: theme.palette.action.disabledBackground + '!important',
            color: theme.palette.action.disabled + '!important',
            cursor: 'default',
            pointerEvents: 'none',
        }
    }
);

interface SelectOption {
    value: string;
    label: string;
    description: string;
}


const personaOptions: SelectOption[] = [
    {value: 'eMDM Data Steward', label: 'eMDM Data Steward', description: 'eMDM Data Steward Role Description...'},
    {value: 'eMDM Viewer', label: 'eMDM Viewer', description: 'Read-only access to eMDM...'},
    {value: 'eMDM Approver', label: 'eMDM Approver', description: 'Can approve eMDM requests...'},
];

const additionalAccessOptions: SelectOption[] = [
    {value: 'Tableau Viewer', label: 'Tableau Viewer', description: 'Allow users to view dashboards in Tableau'},
    {value: 'Reporting Suite', label: 'Reporting Suite', description: 'Access to generate standard reports'},
    {
        value: 'Admin Console',
        label: 'Admin Console',
        description: 'Administrative privileges (requires high justification)'
    },
];

export default function DataAccessRequest() {
    const user = useAuthStore.getState().user;
    const classes = useStyles();
    const [selectedPersonaValue, setSelectedPersonaValue] = useState<string>('');
    const [displayedPersona, setDisplayedPersona] = useState<any | null>(null);
    const [businessJustification, setBusinessJustification] = useState<string>('');
    const [selectedAdditionalAccessValue, setSelectedAdditionalAccessValue] = useState<string>('');
    const [displayedAdditionalAccess, setDisplayedAdditionalAccess] = useState<any[]>([
        {value: 'Tableau Viewer', label: 'Tableau Viewer', description: 'Allow users to view dashboards in Tableau'}
    ]);

    const handleSelectPersona = () => {
        const persona = personaOptions.find(p => p.value === selectedPersonaValue);
        if (persona) setDisplayedPersona(persona);
    };
    const handleDeletePersona = () => {
        setDisplayedPersona(null);
        setSelectedPersonaValue('');
    };
    const handleSelectAdditionalAccess = () => {
        const access = additionalAccessOptions.find(a => a.value === selectedAdditionalAccessValue);
        if (access && !displayedAdditionalAccess.some(da => da.value === access.value)) {
            setDisplayedAdditionalAccess(prev => [...prev, access]);
            setSelectedAdditionalAccessValue('');
        }
    };
    const handleDeleteAdditionalAccess = (valueToDelete: string) => {
        setDisplayedAdditionalAccess(prev => prev.filter(a => a.value !== valueToDelete));
    };
    const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
        event.preventDefault();
        if (!displayedPersona) {
            alert('Please select a Persona.');
            return;
        }
        if (!businessJustification.trim()) {
            alert('Please provide a Business Justification.');
            return;
        }
        console.log('Submitting Request:', {
            persona: displayedPersona,
            justification: businessJustification,
            additionalAccess: displayedAdditionalAccess
        });
        alert('Request Submitted (check console)');
    };

    const RequiredAsterisk = () => (<Box component="span" className={classes.requiredAsterisk}>*</Box>);

    return (<><Container className={classes.container}>

        <Box component="form" onSubmit={handleSubmit} noValidate>
            <Box className={classes.section}>
                <Typography variant="h6" className={classes.sectionTitle}>REQUESTOR INFORMATION</Typography>
                <Grid container spacing={2} className={classes.infoGrid}>
                    <Grid size={{xs: 12, sm: 4}}>
                        <Typography variant="body2" className={classes.infoLabel}>Requestor Name</Typography>
                        <Box className={classes.infoValue}>{user?.displayName}</Box>
                    </Grid>
                    <Grid size={{xs: 12, sm: 4}}>
                        <Typography variant="body2" className={classes.infoLabel}>Requestor Email</Typography>
                        <Box className={classes.infoValue}>{user?.email}</Box>
                    </Grid>
                    <Grid size={{xs: 12, sm: 4}}>
                        <Typography variant="body2" className={classes.infoLabel}>Requestor Office</Typography>
                        <Box className={classes.infoValue}>{user?.officeName}</Box>
                    </Grid>
                </Grid>

            </Box>
            <Box className={classes.section}>
                <Typography variant="h6" className={classes.sectionTitle}>CURRENT ACCESS</Typography>
                <TableContainer>
                    <Table size="small" className={classes.currentAccessTable}
                           aria-label="current access table">
                        <TableHead>
                            <TableRow>
                                <TableCell
                                    sx={{width: '30%'}}>Type</TableCell>
                                <TableCell>Name</TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            <TableRow>
                                <TableCell>Persona</TableCell>
                                <TableCell>Executive</TableCell>
                            </TableRow>
                            <TableRow>
                                <TableCell>Application Access</TableCell>
                                <TableCell>Tableau Viewer</TableCell>
                            </TableRow>
                        </TableBody>
                    </Table>
                </TableContainer>
            </Box>
            {/* Introductory Text (Always Visible) */}
            <Alert severity="info" sx={{mb: 1}}>To request access to eMDM Applications, choose your Persona from the
                dropdown and click on
                select. Once a Persona is selected, please fill out your business justification/need and click
                on Submit.</Alert>

            {/* Choose Persona Section (Always Visible) */}
            <Box className={classes.section}>
                <Box className={classes.selectContainer}>
                    <FormControl variant="outlined" size="small" className={classes.formControl}>
                        <InputLabel id="choose-persona-label">Choose Persona <RequiredAsterisk/></InputLabel>
                        <Select
                            labelId="choose-persona-label"
                            id="choose-persona"
                            value={selectedPersonaValue}
                            onChange={(e: SelectChangeEvent<string>) => setSelectedPersonaValue(e.target.value)}
                            label={<>Choose Persona <RequiredAsterisk/></>}
                        >
                            <MenuItem value=""><em>--Select--</em></MenuItem>
                            {personaOptions.map((option) => (
                                <MenuItem key={option.value} value={option.value}>{option.label}</MenuItem>))}
                        </Select>
                    </FormControl>
                    <Button variant="contained" color="primary" onClick={handleSelectPersona}
                            disabled={!selectedPersonaValue}>Select</Button>
                </Box>

                {/* Displayed Persona Table (Conditionally visible based on selection action) */}
                {displayedPersona && (
                    <TableContainer component={Paper} variant="outlined" className={classes.dataTableContainer}>
                        <Table size="small" aria-label="selected persona">
                            <TableHead>
                                <TableRow className={classes.dataTableHeader}>
                                    <TableCell className={classes.tableCell}>Persona</TableCell>
                                    <TableCell className={classes.tableCell}>Description</TableCell>
                                    <TableCell align="center"
                                               className={`${classes.tableCell} ${classes.actionCell}`}></TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                <TableRow>
                                    <TableCell
                                        className={classes.tableCell}>{displayedPersona.label}</TableCell>
                                    <TableCell
                                        className={classes.tableCell}>{displayedPersona.description}</TableCell>
                                    <TableCell align="center"
                                               className={`${classes.tableCell} ${classes.actionCell}`}>
                                        <IconButton size="small" onClick={handleDeletePersona}
                                                    aria-label="Delete Persona"><DeleteOutlineIcon
                                            fontSize="small"/></IconButton>
                                    </TableCell>
                                </TableRow>
                            </TableBody>
                        </Table>
                    </TableContainer>
                )}
            </Box>

            {/* --- Conditionally Rendered Sections START --- */}
            {displayedPersona && (
                <> {/* Use Fragment to group sections */}
                    {/* Business Justification Section (Only shows if displayedPersona is not null) */}
                    <Box className={classes.section}>
                        <InputLabel htmlFor="business-justification" className={classes.justificationLabel}
                                    required>
                            Provide Business Justification
                        </InputLabel>
                        <Typography variant="caption" display="block"
                                    className={classes.justificationHelperText}>
                            Please provide business justification of why you need this level of access.
                        </Typography>
                        <TextField
                            id="business-justification"
                            multiline
                            rows={4}
                            fullWidth
                            variant="outlined"
                            value={businessJustification}
                            onChange={(e) => setBusinessJustification(e.target.value)}
                            placeholder="Enter justification here..."
                            required
                        />
                    </Box>

                    {/* Choose Additional Access Section (Only shows if displayedPersona is not null) */}
                    <Box className={classes.section}>
                        <Box className={classes.selectContainer}>
                            <FormControl variant="outlined" className={classes.formControl} size="small">
                                <InputLabel id="choose-additional-access-label">Choose Additional
                                    Access</InputLabel>
                                <Select
                                    labelId="choose-additional-access-label"
                                    id="choose-additional-access"
                                    value={selectedAdditionalAccessValue}
                                    onChange={(e: SelectChangeEvent) => setSelectedAdditionalAccessValue(e.target.value)}
                                    label="Choose Additional Access"
                                >
                                    <MenuItem value=""><em>--Select--</em></MenuItem>
                                    {additionalAccessOptions.map((option) => (
                                        <MenuItem key={option.value} value={option.value}
                                                  disabled={displayedAdditionalAccess.some(da => da.value === option.value)}>
                                            {option.label}
                                        </MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                            <Button
                                variant="contained"
                                color="primary"
                                onClick={handleSelectAdditionalAccess}
                                className={`${!selectedAdditionalAccessValue ? classes.disabledLookButton : ''}`}
                            >
                                Select
                            </Button>
                        </Box>

                        {/* Displayed Additional Access Table */}
                        {displayedAdditionalAccess.length > 0 && (
                            <TableContainer component={Paper} variant="outlined"
                                            className={classes.dataTableContainer}>
                                <Table size="small" aria-label="selected additional access">
                                    <TableHead>
                                        <TableRow className={classes.dataTableHeader}>
                                            <TableCell className={classes.tableCell}>Additional
                                                Access</TableCell>
                                            <TableCell className={classes.tableCell}>Description</TableCell>
                                            <TableCell align="center"
                                                       className={`${classes.tableCell} ${classes.actionCell}`}></TableCell>
                                        </TableRow>
                                    </TableHead>
                                    <TableBody>
                                        {displayedAdditionalAccess.map((access) => (
                                            <TableRow key={access.value}>
                                                <TableCell
                                                    className={classes.tableCell}>{access.label}</TableCell>
                                                <TableCell
                                                    className={classes.tableCell}>{access.description}</TableCell>
                                                <TableCell align="center"
                                                           className={`${classes.tableCell} ${classes.actionCell}`}>
                                                    <IconButton size="small"
                                                                onClick={() => handleDeleteAdditionalAccess(access.value)}
                                                                aria-label={`Delete ${access.label}`}>
                                                        <DeleteOutlineIcon fontSize="small"/>
                                                    </IconButton>
                                                </TableCell>
                                            </TableRow>
                                        ))}
                                    </TableBody>
                                </Table>
                            </TableContainer>
                        )}
                    </Box>

                    {/* Submit Button (Only shows if displayedPersona is not null) */}
                    <Box className={classes.submitButtonContainer}>
                        <Button
                            type="submit"
                            variant="contained"
                            color="primary"
                            className={(!displayedPersona || !businessJustification.trim()) ? classes.disabledLookButton : ''}
                            disabled={!displayedPersona || !businessJustification.trim()}
                        >
                            Submit
                        </Button>
                    </Box>
                </>
            )}

        </Box></Container></>);
}