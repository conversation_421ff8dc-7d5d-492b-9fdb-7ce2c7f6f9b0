import React from "react";
import Box from "@mui/material/Box";
import {
    CircularProgress,
    Container,
    Divider,
    Tab,
    Link,
    Paper,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableRow,
    Tooltip
} from "@mui/material";
import Grid from "@mui/material/Grid2"
import Typography from "@mui/material/Typography";
import FirmDetails from "@/components/address-book/FirmDetails";
import GeoWeb from "@/components/address-book/GeoWeb";
import TabList from "@mui/lab/TabList";
import TabPanel from "@mui/lab/TabPanel";
import TabContext from "@mui/lab/TabContext";

interface FirmDetailsParentProps {
    isDrawer: boolean;
    loading: boolean;
    pathname: string;
    rowId: string;
    primaryAddress: any;
    doctoredAddress: any;
    mailingAddress: any;
    dunsDetails: any;
    aliases: any;
    resultData: any;
    registrationDetails: any;
}

const FirmDetailsParent: React.FC<FirmDetailsParentProps> = ({
                                                                 rowId,
                                                                 loading,
                                                                 primaryAddress,
                                                                 doctoredAddress,
                                                                 mailingAddress,
                                                                 dunsDetails,
                                                                 aliases,
                                                                 resultData,
                                                                 registrationDetails
                                                             }) => {


    const [tabValue, setTabValue] = React.useState('1');

    const handleTabChange = (event: React.SyntheticEvent, newValue: string) => {
        setTabValue(newValue);
    };

    if (loading) {
        return (

            <Container sx={{display: 'flex', justifyContent: 'center', alignItems: 'center'}}>
                <CircularProgress/>
            </Container>
        );
    }
    if (!resultData) {
        return (
            <Container>
                <Typography variant="h6" align="center" color="error">
                    Unable to fetch results.
                </Typography>
            </Container>
        );
    }
    return (
        <>
            <Grid container spacing={2} alignItems="stretch" className="firmDetails">
                <Grid size={{sm: 5, xs: 12}}>
                    <Paper elevation={3} style={{height: '100%'}}>
                        <Box component="section" sx={{p: 2, height: '100%'}}>
                            <div style={{color: "#222C67", fontWeight: 'bold'}}>eMDM ID</div>
                            <TableContainer component={Paper}>
                                <Table size="small">
                                    <TableBody>
                                        {resultData?.emdmIds &&
                                            Array.isArray(resultData.emdmIds) &&
                                            resultData.emdmIds
                                                .map((id: any) => {
                                                    const [prefix, suffix] = id.split('-');
                                                    return {prefix, suffix, original: id};
                                                })
                                                .sort((a: any, b: any) => b.suffix.localeCompare(a.suffix)) // Sort by suffix in descending order
                                                .map(({prefix, suffix}: any, index: any) => (
                                                    <TableRow key={index}>
                                                        <TableCell style={{textAlign: 'left'}}>{prefix}</TableCell>
                                                        <TableCell style={{
                                                            fontWeight: 'bold',
                                                            textAlign: 'left'
                                                        }}>{suffix}</TableCell>
                                                    </TableRow>
                                                ))}
                                    </TableBody>
                                </Table>
                            </TableContainer>
                        </Box>
                    </Paper>
                </Grid>
                <Grid size={{xs: 12, sm: 4}}>
                    <Paper elevation={3} style={{height: '100%'}}>
                        <Box component="section" sx={{p: 2, height: '100%'}}>
                            <div style={{color: "#222C67", fontWeight: 'bold'}}>Firm Name & Address

                            </div>
                            <div>
                                <div style={{fontSize: '0.9rem'}}>
                                    {resultData?.srcPrimaryName}

                                </div>
                            </div>
                            <Tooltip
                                title="Click the address link to find firm, nearby spots, and coordinates on Google Maps."
                                placement="bottom-start">
                                <Link
                                    href={`https://www.google.com/maps/search/?api=1&query=${doctoredAddress?.addrfulltxt}`}
                                    target="_blank">
                                    {primaryAddress?.addressLine1 && <span>{primaryAddress?.addressLine1}, </span>}
                                    {primaryAddress?.city && <span>{primaryAddress?.city}, </span>}
                                    {mailingAddress?.stateProvinceName &&
                                        <span>{mailingAddress?.stateProvinceName}, </span>}
                                    {primaryAddress?.countryCode && <span>{primaryAddress?.countryCode}, </span>}
                                    {primaryAddress?.postalCode && primaryAddress?.postalCode != "null" &&
                                        <span>{primaryAddress?.postalCode}</span>}
                                </Link>
                            </Tooltip>


                        </Box>
                    </Paper>
                </Grid>
                {resultData?.dunsDetails && <Grid size={{xs: 6, sm: 2}}>
                    <Paper elevation={3} style={{height: '100%'}}>
                        <Box component="section" sx={{p: 2, height: '100%'}}>
                            <div style={{color: "#222C67", fontWeight: 'bold'}}> DUNS Number</div>
                            <div>
                                {dunsDetails?.dunsNumber}
                            </div>
                        </Box>
                    </Paper>
                </Grid>
                }
                {
                    resultData?.feiNumber && <Grid size={{xs: 6, sm: 2}}>
                        <Paper elevation={3} style={{height: '100%'}}>
                            <Box component="section" sx={{p: 2, height: '100%'}}>
                                <div style={{color: "#222C67", fontWeight: 'bold'}}> FEI Number
                                </div>
                                <Tooltip title="View details on OSAR Website" placement="bottom-start">
                                    <Link
                                        href={`https://osar.fda.gov/oraosar/firm360/index.html?fei=${resultData?.feiNumber}`}
                                        target="_blank">
                                        {resultData?.feiNumber}
                                    </Link>
                                </Tooltip>
                            </Box>
                        </Paper>
                    </Grid>
                }
            </Grid>

            <Grid container spacing={2} sx={{mt: 0, mb: 0}}>
                <Grid size={{xs: 0, sm: 4}}>
                </Grid>

                <Grid size={{xs: 12}}>

                </Grid>

                <Grid size={{xs: 0, sm: 4}}>
                </Grid>
            </Grid>

            <TabContext value={tabValue}>
                <Box sx={{borderBottom: 1, borderColor: 'divider'}}>
                    <TabList onChange={handleTabChange} aria-label="Firm Details Tabs">
                        <Tab label="Firm Details" value="1"/>
                        <Tab label="Map View" value="2"/>
                    </TabList>
                </Box>
                <TabPanel value="1" tabIndex={1}>
                    <Typography variant="h5" sx={{color: '#222C67', fontWeight: 'bold'}}>Firm Details</Typography>
                    <Divider color="#222C67"/>
                    <FirmDetails
                        primaryAddress={primaryAddress}
                        mailingAddress={mailingAddress}
                        dunsDetails={dunsDetails}
                        aliases={aliases}
                        resultData={resultData}
                        doctoredAddress={doctoredAddress}
                        registrationDetails={registrationDetails}/>
                </TabPanel>
                <TabPanel value="2" tabIndex={2}>
                    <GeoWeb data={primaryAddress} firmName={resultData?.srcPrimaryName} rowId={rowId}/>
                </TabPanel>
            </TabContext>


        </>
    )
}

export default FirmDetailsParent