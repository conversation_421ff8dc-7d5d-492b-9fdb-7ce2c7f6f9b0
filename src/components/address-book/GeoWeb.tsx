import {EMDM_GEOWEB_MAP_URL} from "@/constants/constants";

const GeoWeb = ({data, rowId}: any) => {

    return (
        <div style={{width: '100%', marginTop: 20}}>
            {
                data != null ? <iframe
                        src={`${EMDM_GEOWEB_MAP_URL}&marker=${data?.Longitude}; ${data?.Lattitude};;${data?.FormattedAddress};;&level=15`}/>
                    : <iframe src={`${EMDM_GEOWEB_MAP_URL}&id=${rowId}&level=16`}/>
            }
        </div>
    )
}

export default GeoWeb;