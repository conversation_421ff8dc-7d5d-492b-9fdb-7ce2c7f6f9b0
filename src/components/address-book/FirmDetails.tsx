import React from "react";
import Box from "@mui/material/Box";
import {CardContent, Divider, Paper} from "@mui/material";
import Grid from "@mui/material/Grid2"
import Typography from "@mui/material/Typography";

interface FirmDetailsProps {
    primaryAddress: any;
    doctoredAddress: any;
    mailingAddress: any;
    dunsDetails: any;
    aliases: any;
    resultData: any;
    registrationDetails: any;
}

const FirmDetails: React.FC<FirmDetailsProps> = ({
                                                     primaryAddress,
                                                     mailingAddress,
                                                     dunsDetails,
                                                     aliases,
                                                     resultData,
                                                     registrationDetails
                                                 }) => {

    return (
        <CardContent sx={{mb: 3}}>
            <Grid container spacing={2} alignItems="stretch" className="firmDetails">
                {primaryAddress && <Grid size={{sm: 4, xs: 12}}>
                    <Paper elevation={3} style={{height: '100%'}}>
                        <Box component="section" sx={{p: 2, height: '100%'}}>
                            <div style={{color: '#222C67', fontWeight: 'bold'}}>Physical Address</div>
                            <div>
                                {primaryAddress?.addressLine1 && <div>Address: {primaryAddress?.addressLine1}</div>}
                                {primaryAddress?.addressLine2 && <div>Address: {primaryAddress?.addressLine2}</div>}
                                {/*{primaryAddress?.addressType && <div>Address Type: {primaryAddress?.addressType}</div>}*/}
                                {primaryAddress?.city && <div>City: {primaryAddress?.city}</div>}
                                {primaryAddress?.countryCode && <div>Country Code: {primaryAddress?.countryCode}</div>}
                                {primaryAddress?.stateProvinceName &&
                                    <div> State: {primaryAddress?.stateProvinceName}</div>}
                                {primaryAddress?.postalCode && <div>Postal Code: {primaryAddress?.postalCode}</div>}
                                {/* Hiding source address from UI as per UAT Feedback{primaryAddress?.srcAddressLine1 &&*/}
                                {/*    <div>Source Address: {primaryAddress?.srcAddressLine1}</div>}*/}
                                {/*{primaryAddress?.sourceCity && <div>Source City: {primaryAddress?.sourceCity}</div>}*/}
                                {/*{primaryAddress?.sourceCountryCode &&*/}
                                {/*    <div>Source Country Code: {primaryAddress?.sourceCountryCode}</div>}*/}
                                {primaryAddress?.longitude && <div>Longitude: {primaryAddress?.longitude}</div>}
                                {primaryAddress?.latitude && <div>Latitude: {primaryAddress?.latitude}</div>}
                            </div>
                        </Box>
                    </Paper>
                </Grid>}

                {mailingAddress &&
                    <Grid size={{sm: 4, xs: 12}}>
                        <Paper elevation={3} style={{height: '100%'}}>
                            <Box component="section" sx={{p: 2, height: '100%'}}>
                                <div style={{color: '#222C67', fontWeight: 'bold'}}>Mailing Address</div>
                                {mailingAddress?.addressLine1 && <div>Address: {mailingAddress?.addressLine1}</div>}
                                {mailingAddress?.addressLine2 && <div>Address: {mailingAddress?.addressLine2}</div>}
                                {/*{mailingAddress?.addressType && <div>Address Type: {mailingAddress?.addressType}</div>}*/}
                                {mailingAddress?.city && <div>City: {mailingAddress?.city}</div>}
                                {mailingAddress?.countryCode && <div>Country Code: {mailingAddress?.countryCode}</div>}
                                {mailingAddress?.stateProvinceName &&
                                    <div> State: {mailingAddress?.stateProvinceName}</div>}
                                {mailingAddress?.postalCode && <div>Postal Code: {mailingAddress?.postalCode}</div>}
                                {mailingAddress?.srcAddressLine1 &&
                                    <div>Source Address: {mailingAddress?.srcAddressLine1}</div>}
                                {mailingAddress?.sourceCity && <div>Source City: {mailingAddress?.sourceCity}</div>}
                                {mailingAddress?.sourceCountryCode &&
                                    <div>Source Country Code: {mailingAddress?.sourceCountryCode}</div>}
                                {mailingAddress?.longitude && <div>Longitude: {mailingAddress?.longitude}</div>}
                                {mailingAddress?.latitude && <div>Latitude: {mailingAddress?.latitude}</div>}
                            </Box>
                        </Paper>
                    </Grid>
                }

                {
                    dunsDetails &&
                    <Grid size={{sm: 4, xs: 12}}>
                        <Paper elevation={3} style={{height: '100%'}}>
                            <Box component="section" sx={{p: 2, height: '100%'}}>
                                <div style={{color: '#222C67', fontWeight: 'bold'}}>DUNS Details</div>
                                {dunsDetails &&
                                    <div>
                                        {dunsDetails?.dunsNumber && <div>Duns Number: {dunsDetails?.dunsNumber}</div>}
                                        {dunsDetails?.dnbBusinessName &&
                                            <div>Business Name: {dunsDetails?.dnbBusinessName}</div>}
                                        {dunsDetails?.dnbAddress && <div>Address: {dunsDetails?.dnbAddress}</div>}
                                        {dunsDetails?.dnbCity && <div>City: {dunsDetails?.dnbCity}</div>}
                                        {dunsDetails?.dnbStateAbbr && <div>State: {dunsDetails?.dnbStateAbbr}</div>}
                                        {dunsDetails?.dnbISOCountryCode &&
                                            <div>Country Code: {dunsDetails?.dnbISOCountryCode}</div>}
                                        {dunsDetails?.dnbPhone && <div>Phone Number: {dunsDetails?.dnbPhone}</div>}
                                        {dunsDetails?.dunsActivityStsCode &&
                                            <div>Act. Code: {dunsDetails?.dunsActivityStsCode}</div>}
                                        {dunsDetails?.dunsMatchGrade && <div>Match: {dunsDetails?.dunsMatchGrade}</div>}
                                        {dunsDetails?.dunsMatchConfScore &&
                                            <div>Match Number: {dunsDetails?.dunsMatchConfScore}</div>}
                                    </div>
                                }
                            </Box>
                        </Paper>
                    </Grid>
                }

                {
                    aliases &&
                    <Grid size={{sm: 4, xs: 12}}>
                        <Paper elevation={3} style={{height: '100%'}}>
                            <Box component="section" sx={{p: 2, height: '100%'}}>
                                <div style={{color: '#222C67', fontWeight: 'bold'}}>Alias Details</div>
                                {
                                    aliases &&
                                    <div>
                                        {aliases?.aliasName && <div>Alias Name: {aliases?.aliasName}</div>}
                                        {aliases?.aliasType && <div>Alias Type: {aliases?.aliasType}</div>}
                                    </div>
                                }
                            </Box>
                        </Paper>
                    </Grid>
                }

                {
                    registrationDetails &&
                    <Grid size={{sm: 4, xs: 12}}>
                        <Paper elevation={3} style={{height: '100%'}}>
                            <Box component="section" sx={{p: 2, height: '100%'}}>
                                <div style={{color: '#222C67', fontWeight: 'bold'}}>Registration Details</div>
                                {
                                    registrationDetails &&
                                    <div>
                                        {registrationDetails?.registrationNumber &&
                                            <div>Registration Number: {registrationDetails?.registrationNumber}</div>}
                                        {registrationDetails?.registrationStatusCode && <div>Registration Status
                                            Code: {registrationDetails?.registrationStatusCode}</div>}
                                        {registrationDetails?.registrationTypeCode && <div>Registration Type
                                            Code: {registrationDetails?.registrationTypeCode}</div>}
                                    </div>
                                }
                            </Box>
                        </Paper>
                    </Grid>
                }
                <Divider/>
            </Grid>
            {
                (resultData?.contactDetails || resultData?.businessActivityDetails) &&
                <Typography variant="h5" sx={{color: '#222C67', fontWeight: 'bold', margin: '10px 0 0 0'}}>Contacts and
                    Activities

                    <Divider color='#222C67' sx={{marginBottom: 1}}/></Typography>

            }
            <br/>
            <Grid container spacing={2} alignItems="stretch"  className="firmDetails">
                {
                    resultData?.contactDetails?.map((e: {
                        name: string | number | bigint | boolean | React.ReactElement<unknown, string | React.JSXElementConstructor<any>> | Iterable<React.ReactNode> | React.ReactPortal | Promise<string | number | bigint | boolean | React.ReactPortal | React.ReactElement<unknown, string | React.JSXElementConstructor<any>> | Iterable<React.ReactNode> | null | undefined> | null | undefined;
                        email: string | number | bigint | boolean | React.ReactElement<unknown, string | React.JSXElementConstructor<any>> | Iterable<React.ReactNode> | React.ReactPortal | Promise<string | number | bigint | boolean | React.ReactPortal | React.ReactElement<unknown, string | React.JSXElementConstructor<any>> | Iterable<React.ReactNode> | null | undefined> | null | undefined;
                        mobilePhone: string | number | bigint | boolean | React.ReactElement<unknown, string | React.JSXElementConstructor<any>> | Iterable<React.ReactNode> | React.ReactPortal | Promise<string | number | bigint | boolean | React.ReactPortal | React.ReactElement<unknown, string | React.JSXElementConstructor<any>> | Iterable<React.ReactNode> | null | undefined> | null | undefined;
                        workPhone: string | number | bigint | boolean | React.ReactElement<unknown, string | React.JSXElementConstructor<any>> | Iterable<React.ReactNode> | React.ReactPortal | Promise<string | number | bigint | boolean | React.ReactPortal | React.ReactElement<unknown, string | React.JSXElementConstructor<any>> | Iterable<React.ReactNode> | null | undefined> | null | undefined;
                        companyName: string | number | bigint | boolean | React.ReactElement<unknown, string | React.JSXElementConstructor<any>> | Iterable<React.ReactNode> | React.ReactPortal | Promise<string | number | bigint | boolean | React.ReactPortal | React.ReactElement<unknown, string | React.JSXElementConstructor<any>> | Iterable<React.ReactNode> | null | undefined> | null | undefined;
                        contactType: string | number | bigint | boolean | React.ReactElement<unknown, string | React.JSXElementConstructor<any>> | Iterable<React.ReactNode> | React.ReactPortal | Promise<string | number | bigint | boolean | React.ReactPortal | React.ReactElement<unknown, string | React.JSXElementConstructor<any>> | Iterable<React.ReactNode> | null | undefined> | null | undefined;
                    }, key: number) => (
                        <Grid size={{sm: 4, xs: 12}} key={key}>
                            <Paper elevation={3} style={{height: '100%'}}>
                                <Box component="section" sx={{p: 2, height: '100%'}}>
                                    <div style={{color: '#222C67', fontWeight: 'bold'}}>CONTACT {key + 1}</div>
                                    {e?.name && <div>Name: {e?.name}</div>}
                                    {e?.email && <div>email: {e?.email}</div>}
                                    {e?.mobilePhone && <div>Mobile Phone: {e?.mobilePhone}</div>}
                                    {e?.workPhone && <div>Work Phone: {e?.workPhone}</div>}
                                    {e?.companyName && <div>company Name: {e?.companyName}</div>}
                                    {e?.contactType && <div>Contact Type: {e?.contactType}</div>}
                                </Box>
                            </Paper>
                        </Grid>
                    ))}
                {
                    resultData?.businessActivityDetails && <Grid size={{sm: 4, xs: 12}}>
                        <Paper elevation={3} style={{height: '100%'}}>
                            <Box component="section" sx={{p: 4, height: '100%'}}>
                                <div style={{color: '#222C67', fontWeight: 'bold'}}>Business Activity List</div>
                                {resultData?.businessActivityDetails?.map((e: {
                                    businessActivityCode: string | number | bigint | boolean | React.ReactElement<unknown, string | React.JSXElementConstructor<any>> | Iterable<React.ReactNode> | React.ReactPortal | Promise<string | number | bigint | boolean | React.ReactPortal | React.ReactElement<unknown, string | React.JSXElementConstructor<any>> | Iterable<React.ReactNode> | null | undefined> | null | undefined;
                                }, key: React.Key | null | undefined) => (e?.businessActivityCode &&
                                    <div key={key}> {e?.businessActivityCode}</div>))}
                            </Box>
                        </Paper>
                    </Grid>
                }
            </Grid>
        </CardContent>
    )
}

export default FirmDetails