import {APIProvider, <PERSON>, Marker, useMapsLibrary} from '@vis.gl/react-google-maps';
import {useState, useMemo, useEffect} from 'react';

export function GoogleMap(address: any, coordinates?: any) {
    const geocodingLib = useMapsLibrary('geocoding');
    const geocoder = useMemo(() => geocodingLib && new geocodingLib.Geocoder(), [geocodingLib]);
    const [markerPosition, setMarkerPosition] = useState<any>(null);

    useEffect(() => {
        if (!geocoder) return;

        geocoder.geocode({address}, (results, status) => {
            if (status === 'OK' && results && results.length > 0) {
                const location = results[0].geometry.location;
                console.log("location", location);
                //setMarkerPosition({lat: location.lat(), lng: location.lng()});
                setMarkerPosition({lat: coordinates.lat, lng: coordinates.lng});

            } else {
                console.error('Geocode was not successful for the following reason: ' + status);
            }
        });
    }, [address, coordinates, geocoder]);

    return (
        <APIProvider apiKey={process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || ''}>
            <Map style={{width: '100vw', height: '100vh'}}
                 defaultCenter={{lat: 0, lng: 0}}
                 zoom={10}
            >
                {markerPosition && (
                    <Marker position={markerPosition}/>
                )}
            </Map>
        </APIProvider>
    );
}