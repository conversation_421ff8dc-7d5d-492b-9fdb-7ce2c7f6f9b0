'use client'
import React, {useState} from 'react'
import {Autocomplete, Button, CircularProgress, TextField} from '@mui/material';
import Grid from '@mui/material/Grid2';
import {USA_STATES as us_states} from '@/constants/usa-states';
import {geocode_schema} from '@/constants/validations'
import {useFormik} from 'formik';
import GeoWeb from '../../address-book/GeoWeb';
import GeoCodeTable from "@/components/toolkit/geocode/GeoCodeTable";
import appService from "@/services/app.service";
import CountryAutoComplete from "@/components/common/CountryAutoComplete";
import Box from "@mui/material/Box";

const GeoCodePage = () => {

    const [loading, setLoading] = useState(false);
    const [data, setData] = useState<any>(null);

    const formik = useFormik({
        initialValues: {countryCode: "", state: "", addressLine1: "", city: "", zip_code: ""},
        validationSchema: geocode_schema,
        onSubmit: (values) => {
            fetchData(values)
        },
    });

    const fetchData = async (searchParams: any) => {
        try {
            setLoading(true)
            await appService.fetchGeoCodeAddressDetails(searchParams).then((data) => {
                setData(data?.addressDetails);
                setLoading(false)
            })
        } catch (error) {
            setLoading(false)
            setData(null);
            console.error(error);
        }
    };

    function resetValues() {
        setLoading(false);
        setData(null);
    }


    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid container spacing={1}>
                <Grid size={{md: 2, xs: 4}}>
                    <label htmlFor={'address'}>Street Address</label> <span style={{color: "red"}}>*</span>
                    <TextField size="small"
                               fullWidth
                               id="addressLine1"
                               name="addressLine1"
                               className="street-address"
                               value={formik.values.addressLine1}
                               onChange={formik.handleChange}
                               onBlur={formik.handleBlur}
                               error={formik.touched.addressLine1 && Boolean(formik.errors.addressLine1)}
                               helperText={formik.touched.addressLine1 && formik.errors.addressLine1}
                    />
                </Grid>
                <Grid size={{md: 2, xs: 4}}>
                    <label htmlFor={'country'}>Country</label> <span style={{color: "red"}}>*</span>
                    <CountryAutoComplete formik={formik}/>
                </Grid>
                {
                    formik.values.countryCode != 'US' &&
                    <Grid size={{md: 2, xs: 4}}>
                        <label htmlFor={'state'}>State/Province</label>
                        <TextField size="small"
                                   fullWidth
                                   id="state"
                                   name="state"
                                   className="state-name"
                                   value={formik.values.state}
                                   onChange={formik.handleChange}
                                   onBlur={formik.handleBlur}
                                   error={formik.touched.state && Boolean(formik.errors.state)}
                                   helperText={formik.touched.state && formik.errors.state}
                        />
                    </Grid>
                }
                {
                    formik.values.countryCode == 'US' &&
                    <Grid size={{md: 2, xs: 4}}>
                        <label htmlFor={'state'}>State</label>
                        <Autocomplete size="small"
                                      disablePortal
                                      id="state"
                                      options={us_states}
                                      getOptionLabel={(option) => option?.label}
                            //getOptionSelected={(option: any, value: any) => option?.code === value?.code}
                                      value={us_states.find((option) => option?.code === formik.values.state)}
                                      onChange={(_, newValue) => formik.setFieldValue("state", newValue ? newValue?.code : "")}
                                      onBlur={() => formik.setFieldTouched("state")}
                                      renderInput={(params) =>
                                          <TextField
                                              {...params}
                                              error={formik.touched.state && Boolean(formik.errors.state)}
                                              helperText={formik.touched.state && formik.errors.state}
                                          />}
                        />
                    </Grid>
                }
                <Grid size={{md: 2, xs: 4}}>
                    <label htmlFor={'city'}>City</label> <span style={{color: "red"}}>*</span>
                    <TextField size="small"
                               fullWidth
                               id="city"
                               name="city"
                               className="city-name"
                               required={true}
                               value={formik.values.city}
                               onChange={formik.handleChange}
                               onBlur={formik.handleBlur}
                               error={formik.touched.city && Boolean(formik.errors.city)}
                               helperText={formik.touched.city && formik.errors.city}
                    />
                </Grid>
                <Grid size={{md: 2, xs: 4}}>
                    <label htmlFor={'zip_code'}>Zip Code</label>
                    <TextField size="small"
                               fullWidth
                               id="zip_code"
                               name="zip_code"
                               className="zip-code"
                               value={formik.values.zip_code}
                               onChange={formik.handleChange}
                               onBlur={formik.handleBlur}
                               error={formik.touched.zip_code && Boolean(formik.errors.zip_code)}
                               helperText={formik.touched.zip_code && formik.errors.zip_code}
                    /> </Grid>
                <Grid size={{md: 6}}>
                    <Button size="medium" variant="contained" type="submit"
                            style={{marginRight: 25}}> search </Button>
                    <Button size="medium" sx={{mr: 2}} variant="contained"
                            onClick={() => {
                                formik.resetForm();
                                resetValues();
                                setLoading(false);
                            }}>Clear</Button>
                </Grid>
                {
                    loading ? <Box sx={{width: '100%', mt: 1}}>
                        <CircularProgress/>
                    </Box> : (data != null) &&
                        <div style={{marginTop: 5}}>
                            <GeoCodeTable row={data}/>
                            <GeoWeb data={data}/>
                        </div>
                }
                <>

                </>
            </Grid>
        </form>
    )
}

export default GeoCodePage;