import * as React from 'react';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';

const GeoCodeTable = ({row}: any) => {


    return (
        <>
            <Table sx={{width: '80vw', marginTop: 5}} aria-label="simple table">
                <TableHead style={{background: "#222C67", border: '1px solid black', whiteSpace: 'nowrap', height: 10}}>
                    <TableRow>
                        <TableCell align="left" className="table_head">Type</TableCell>
                        <TableCell align="left" className="table_head">Address</TableCell>
                        <TableCell align="left" className="table_head">City</TableCell>
                        <TableCell align="left" className="table_head">State</TableCell>
                        <TableCell align="left" className="table_head">Country</TableCell>
                        <TableCell align="left" className="table_head">Postal Code</TableCell>
                        <TableCell align="left" className="table_head">Lattitude</TableCell>
                        <TableCell align="left" className="table_head">Longitude</TableCell>
                        <TableCell align="left" className="table_head">Formated Address</TableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    <TableRow
                        key={row?.dnbPhoneNumber}
                    >
                        <TableCell align="left" component="th" scope="row">{row?.addressType}</TableCell>
                        <TableCell align="left">{row?.AddressLine1}</TableCell>
                        <TableCell align="left">{row?.City}</TableCell>
                        <TableCell align="left">{row?.StateOrProvince}</TableCell>
                        <TableCell align="left">{row?.Country}</TableCell>
                        <TableCell align="left">{row?.ZipCode}</TableCell>
                        <TableCell align="left">{row?.Lattitude}</TableCell>
                        <TableCell align="left">{row?.Longitude}</TableCell>
                        <TableCell align="left">{row?.FormattedAddress}</TableCell>

                    </TableRow>
                </TableBody>
            </Table></>
    )
}

export default GeoCodeTable;