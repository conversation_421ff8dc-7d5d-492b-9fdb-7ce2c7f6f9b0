import React, {useState} from 'react'
import {Button, CircularProgress, TextField} from '@mui/material';
import Grid from '@mui/material/Grid2'
import FeiTable from './FEITable';
import {useFormik} from 'formik';
import {fei_schema} from "@/constants/validations";
import appService from "@/services/app.service";
import Box from "@mui/material/Box";

const FEILookup = () => {

    const [data, setData] = useState<any>(null);

    const [loading, setLoading] = useState(false);

    const formik = useFormik({
        initialValues: {feiNumber: ""},
        validationSchema: fei_schema,
        onSubmit: (values) => {
            fetchData(values)
        },
    });

    function resetValues() {
        setLoading(false);
        setData(null);
    }

    const fetchData = async (searchParams: any) => {
        try {
            setLoading(true);
            await appService.fetchFEIDetails(searchParams).then((data) => setData(data?.feiMatchDetails.firm[0]))
            setLoading(false);
        } catch (error) {
            setLoading(false)
            console.error('Error fetching data', error);
            setData(null);
        }
    };


    return (
        <form onSubmit={formik.handleSubmit}>

            <Grid container spacing={1}>
                <Grid size={{md: 5, xs: 4}}>
                    <label htmlFor={'fei_number'}>FEI Number</label> <span style={{color: "red"}}>*</span>
                    <TextField size="small"
                               fullWidth
                               id="feiNumber"
                               name="feiNumber"
                               value={formik.values.feiNumber}
                               onChange={formik.handleChange}
                               onBlur={formik.handleBlur}
                               error={formik.touched.feiNumber && Boolean(formik.errors.feiNumber)}
                               helperText={formik.touched.feiNumber && formik.errors.feiNumber}
                    />
                </Grid>
                <Grid size={{md: 12}}>
                    <Button variant="contained" type='submit'
                            style={{marginRight: 25}}> Search </Button>
                    <Button size="medium" sx={{mr: 2}} variant="contained"
                            onClick={() => {
                                formik.resetForm();
                                resetValues();
                                setLoading(false);
                            }}>Clear</Button>
                </Grid>
                {
                    loading ? <Box sx={{width: '100%', mt: 1}}>
                        <CircularProgress/>
                    </Box> : (data != null) && <FeiTable rows={data}/>
                }
            </Grid>
        </form>
    )
}

export default FEILookup;