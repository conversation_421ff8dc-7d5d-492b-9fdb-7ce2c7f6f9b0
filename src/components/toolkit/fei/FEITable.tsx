import * as React from 'react';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';

const FEITable = ({rows}: any) => {


    return (
        <>
            <Table sx={{minWidth: 650, marginTop: 5}} aria-label="simple table">
                <TableHead style={{background: "#222C67", border: '1px solid black', whiteSpace: 'nowrap', height: 10}}>
                    <TableRow>
                        <TableCell align="left" className="table_head">FEI Number</TableCell>
                        <TableCell align="left" className="table_head">Business Name</TableCell>
                        <TableCell align="left" className="table_head">Address</TableCell>
                        <TableCell align="left" className="table_head">City</TableCell>
                        <TableCell align="left" className="table_head">Country</TableCell>
                        <TableCell align="left" className="table_head">State</TableCell>
                        <TableCell align="left" className="table_head">Postal Code</TableCell>
                        <TableCell align="left" className="table_head">Firm Comment</TableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    <TableRow
                        key={rows?.dnbPhoneNumber}
                    >
                        <TableCell align="left" component="th" scope="row">{rows?.feiNumber}</TableCell>
                        <TableCell align="left">{rows?.legalName}</TableCell>
                        <TableCell align="left">{rows?.firmAddress[0].addressLine1}</TableCell>
                        <TableCell align="left">{rows?.firmAddress[0].cityName}</TableCell>
                        <TableCell align="left">{rows?.firmAddress[0].isoCountryCode}</TableCell>
                        <TableCell align="left">{rows?.firmAddress[0].state}</TableCell>
                        <TableCell align="left">{rows?.firmAddress[0].zipCode}</TableCell>
                        <TableCell align="left">{rows?.firmComment}</TableCell>
                    </TableRow>
                </TableBody>
            </Table></>
    )
}

export default FEITable;