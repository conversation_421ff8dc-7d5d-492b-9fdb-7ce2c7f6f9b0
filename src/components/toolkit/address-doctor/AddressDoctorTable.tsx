import * as React from 'react';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import {CustomHelp} from "@/components/toolkit/address-doctor/CustomHelp";

const AddressTable = ({row}:any) => {


    return (
        <>
            <Table sx={{minWidth: 650, marginTop: 5}} aria-label="simple table">
                <TableHead style={{background: "#222C67", border: '1px solid black', whiteSpace: 'nowrap', height: 10}}>
                    <TableRow>
                        <TableCell align="left" className="table_head">Full Address</TableCell>
                        <TableCell align="left" className="table_head">Address</TableCell>
                        <TableCell align="left" className="table_head">City</TableCell>
                        <TableCell align="left" className="table_head">State</TableCell>
                        <TableCell align="left" className="table_head">Country</TableCell>
                        <TableCell align="left" className="table_head">Postal Code</TableCell>
                        <TableCell align="left" className="table_head">Lattitude</TableCell>
                        <TableCell align="left" className="table_head">Longitude</TableCell>
                        <TableCell align="left" className="table_head">Geocode Num.</TableCell>
                        <TableCell align="left" className="table_head">Score</TableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    <TableRow
                        key={row?.dnbPhoneNumber}
                    >
                        <TableCell align="left" component="th" scope="row">{row?.addrfulltxt}</TableCell>
                        <TableCell align="left">{row?.addrline1TXT}</TableCell>
                        <TableCell align="left">{row?.addrcitytxt}</TableCell>
                        <TableCell align="left">{row?.addrstatecd}</TableCell>
                        <TableCell align="left">{row?.addrcntrycd}</TableCell>
                        <TableCell align="left">{row?.addrpstlcd}</TableCell>
                        <TableCell align="left">{row?.addrlattddeg}</TableCell>
                        <TableCell align="left">{row?.addrlngtddeg}</TableCell>
                        <TableCell align="left">{row?.addrgeocdprcsnnum}<CustomHelp size='small'
                                                                                    value={row?.addrgeocdprcsnnum}/></TableCell>
                        <TableCell align="left"
                                   sx={{fontWeight: 600}}>{row?.addrvalmatchcd + " - " + row?.addrmailabilityscore}</TableCell>
                    </TableRow>
                </TableBody>
            </Table></>
    )
}

export default AddressTable;