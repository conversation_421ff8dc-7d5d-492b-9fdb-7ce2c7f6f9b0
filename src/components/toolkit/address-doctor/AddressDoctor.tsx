import React, {useState} from 'react'
import {Autocomplete, Button, CircularProgress, TextField} from '@mui/material'
import Grid from '@mui/material/Grid2';
import {USA_STATES as us_states} from '@/constants/usa-states';
import AddressTable from './AddressDoctorTable';
import {useFormik} from 'formik';
import CountryAutoComplete from "@/components/common/CountryAutoComplete";
import {address_doctor_schema} from "@/constants/validations";
import appService from "@/services/app.service";
import Box from "@mui/material/Box";

const AddressDoctor = () => {


    const [loading, setLoading] = useState(false);
    const [data, setData] = useState(null);

    const formik = useFormik({
        initialValues: {countryCode: "", stateCode: "", addressLine1: "", city: "", postalCode: ""},
        validationSchema: address_doctor_schema,
        onSubmit: (values) => {
            fetchData(values)
        },
    });


    const fetchData = async (searchParams: any) => {

        try {
            setLoading(true);
            await appService.getAddressDoctorAddressDetails(searchParams).then((data) => {
                setData(data?.addressDetails);
                setLoading(false)
            })
        } catch (error) {
            setLoading(false)
            setData(null);
            console.error(error);
        }
    };

    function resetValues() {
        setLoading(false);
        setData(null);
    }


    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid container spacing={1}>
                <Grid size={{md: 2, xs: 4}}>
                    <label htmlFor={'address'}>Street Address</label> <span style={{color: "red"}}>*</span>
                    <TextField size="small"
                               fullWidth
                               id="addressLine1"
                               name="addressLine1"
                               className="street-address"
                               value={formik.values.addressLine1}
                               onChange={formik.handleChange}
                               onBlur={formik.handleBlur}
                               error={formik.touched.addressLine1 && Boolean(formik.errors.addressLine1)}
                               helperText={formik.touched.addressLine1 && formik.errors.addressLine1}
                    />
                </Grid>
                <Grid size={{md: 2, xs: 4}}>
                    <label htmlFor={'country'}>Country</label> <span style={{color: "red"}}>*</span>
                    <CountryAutoComplete formik={formik}/>
                </Grid>
                {
                    formik.values.countryCode != 'US' &&
                    <Grid size={{md: 2, xs: 4}}>
                        <label htmlFor={'state'}>State/Province</label>
                        <TextField size="small"
                                   fullWidth
                                   id="stateCode"
                                   name="stateCode"
                                   className="state-name"
                                   value={formik.values.stateCode}
                                   onChange={formik.handleChange}
                                   onBlur={formik.handleBlur}
                                   error={formik.touched.stateCode && Boolean(formik.errors.stateCode)}
                                   helperText={formik.touched.stateCode && formik.errors.stateCode}
                        />
                    </Grid>
                }
                {
                    formik.values.countryCode == 'US' &&
                    <Grid size={{md: 2, xs: 4}}>
                        <label htmlFor={'state'}>State</label>
                        <Autocomplete size="small"
                                      disablePortal
                                      id="state"
                                      options={us_states}
                                      getOptionLabel={(option) => option?.label}
                            //getOptionSelected={(option: any, value: any) => option?.code === value?.code}
                                      value={us_states.find((option) => option?.code === formik.values.stateCode)}
                                      onChange={(_, newValue) => formik.setFieldValue("state", newValue ? newValue?.code : "")}
                                      onBlur={() => formik.setFieldTouched("state")}
                                      renderInput={(params) =>
                                          <TextField
                                              {...params}
                                              error={formik.touched.stateCode && Boolean(formik.errors.stateCode)}
                                              helperText={formik.touched.stateCode && formik.errors.stateCode}
                                          />}
                        />
                    </Grid>
                }
                <Grid size={{md: 2, xs: 4}}>
                    <label htmlFor={'city'}>City</label> <span style={{color: "red"}}>*</span>
                    <TextField size="small"
                               fullWidth
                               id="city"
                               name="city"
                               className="city-name"
                               required={true}
                               value={formik.values.city}
                               onChange={formik.handleChange}
                               onBlur={formik.handleBlur}
                               error={formik.touched.city && Boolean(formik.errors.city)}
                               helperText={formik.touched.city && formik.errors.city}
                    />
                </Grid>
                <Grid size={{md: 2, xs: 4}}>
                    <label htmlFor={'zip_code'}>Zip Code</label>
                    <TextField size="small"
                               fullWidth
                               id="postalCode"
                               name="postalCode"
                               className="postalCode"
                               value={formik.values.postalCode}
                               onChange={formik.handleChange}
                               onBlur={formik.handleBlur}
                               error={formik.touched.postalCode && Boolean(formik.errors.postalCode)}
                               helperText={formik.touched.postalCode && formik.errors.postalCode}
                    /> </Grid>
                <Grid size={{md: 6}}>
                    <Button variant="contained" type="submit"
                            style={{marginRight: 25}}> search </Button>
                    <Button size="medium" sx={{mr: 2}} variant="contained"
                            onClick={() => {
                                formik.resetForm();
                                resetValues();
                                setLoading(false);
                            }}>Clear</Button>
                </Grid>

                {
                    loading ? <Box sx={{width: '100%', mt: 1}}>
                        <CircularProgress/>
                    </Box> : (data != null) && <AddressTable row={data}/>
                }
            </Grid>
        </form>
    )
}

export default AddressDoctor;