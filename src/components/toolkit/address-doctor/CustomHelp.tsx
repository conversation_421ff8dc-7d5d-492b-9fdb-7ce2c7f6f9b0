import HelpIcon from '@mui/icons-material/Help';
import {Tooltip} from '@mui/material';


export const CustomHelp = ({value, size}: any) => {

    return (
        <Tooltip sx={{marginBottom: 2}} title={messageProcessor({value: value})} placement="right-start" tabIndex={0}>
            <HelpIcon fontSize={size}/>
        </Tooltip>
    );
};

function messageProcessor({value}: any) {
    switch (value) {
        case 'EGCN':
            return "Address Verification cannot find the geocoding database.";
        case 'EGCU':
            return "The geocoding database is not unlocked.";
        case 'EGCC':
            return "The geocoding database is corrupt.";
        case 'EGC0':
            return "Address Verification could not append geocoordinates to the input address because no geocoordinates are available for the address."
        case 'EGC4':
            return "Geocoordinates are only partially accurate to the postal code level. For example, 795xx."
        case 'EGC5':
            return "Geocoordinates are accurate to the postal code level."
        case 'EGC6':
            return "Geocoordinates are accurate to the locality level."
        case 'EGC7':
            return "Geocoordinates are accurate to the street level."
        case 'EGC8':
            return "Geocoordinates are accurate to the house number level. (Estimated location of the parcel of land with street-side offset.)"
        case 'EGC9':
            return "High-precision arrival point geocoordinates. (Measured entryway to the parcel of land.)"
        case 'EGCA':
            return "High-precision parcel centroid geocoordinates. (Measured center of the parcel of land.)"
        case 'EGCB':
            return "High-precision rooftop geocoordinates."
    }
}
