import InfoCard from "@/components/common/InfoCard";
import React from "react";
import RecentActivityTracker from "@/constants/recentActivityTracker";
import SearchAppBar from "@/components/common/SearchAppBar";

export default function Home() {
    RecentActivityTracker('Home');
    return (
        <div>
            <InfoCard header="Welcome to the FDA Data Concierge"
                      description="The Data Concierge is a data management portal that provides access to Data Management and Data Governance products and services across FDA Centers. Use the menu on the left or search box below to navigate to the Data Products & Services, Data Governance, and Data Marketplace."
                      icon={`${process.env.NEXT_PUBLIC_BASE_PATH}/img/FDA-data-concierge.svg`}/>
        </div>
    )
}

Home.getLayout = (page: React.ReactElement) => page;

