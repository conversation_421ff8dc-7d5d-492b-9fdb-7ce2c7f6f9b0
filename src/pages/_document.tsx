import React from 'react';
import Document, { Html, Head, Main, NextScript } from 'next/document';
import { ServerStyleSheets } from '@mui/styles';

class CustomDocument extends Document {
    static async getInitialProps(ctx: any) {
        // Create an instance of ServerStyleSheets
        const sheets = new ServerStyleSheets();
        const originalRenderPage = ctx.renderPage;

        ctx.renderPage = () =>
            originalRenderPage({
                enhanceApp: (App: any) => (props: any) => sheets.collect(<App {...props} />),
            });

        const initialProps = await Document.getInitialProps(ctx);

        return {
            ...initialProps,
            styles: [
                ...React.Children.toArray(initialProps.styles),
                sheets.getStyleElement(),
            ],
        };
    }

    render() {
        return (
            <Html lang="en">
                <Head>
                    <link rel='preconnect' href='https://fonts.googleapis.com'/>
                    <link rel='preconnect' href='https://fonts.gstatic.com'/>

                    <link
                        rel='stylesheet'
                        href='https://fonts.googleapis.com/css2?family=Roboto+Condensed:wght@300;400;500;600;700&display=swap'
                    />
                    <link
                        rel='stylesheet'
                        href='https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;600;700&display=swap'
                    />
                    <link rel="icon" href={process.env.NEXT_PUBLIC_BASE_PATH + '/img/FDA-data-concierge.svg'}
                          sizes="any"/>
                </Head>
                <body>
                <Main/>
                <NextScript />
                </body>
            </Html>
        );
    }
}

export default CustomDocument;