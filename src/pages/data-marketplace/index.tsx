import InfoCard from "@/components/common/InfoCard";
import React from "react";
import RecentActivityTracker from "@/constants/recentActivityTracker";
import {Box} from "@mui/material";
import CatalogCard from "@/components/common/CatalogCard";
import {catalogStyles} from "@/styles/catalogStyles";
import {CATALOG_TILES} from "@/constants/catalogs";

const breadcrumbs = [
    {label: 'Data Concierge', href: '/'},
    {label: 'Data Marketplace', href: ''}
]

const DataMarketplace = () => {
    RecentActivityTracker('Data Marketplace');
    const classes = catalogStyles();
    const dataMarketplaceCatalog = CATALOG_TILES.marketplace.items;

    return (
        <>
            <div>
                <InfoCard breadcrumbs={breadcrumbs} header="Data Marketplace Catalog"
                          description="Welcome to the Data Marketplace page. Navigate to the Data Marketplace functionalities by clicking the cards below."
                          icon={`${process.env.NEXT_PUBLIC_BASE_PATH}/img/ai-service-dark.svg`}/>
            </div>
            <Box className={classes.cardContentBox}>
                {dataMarketplaceCatalog.map((item, index) => (
                    <CatalogCard key={index} name={item.name} path={item.path} iconPath={item.image}
                                 external_url={item.external_url}
                                 subtext={item.subtext}
                                 description={item.description}/>

                ))}
            </Box></>
    )
}

DataMarketplace.getLayout = (page: React.ReactElement) => page;

export default DataMarketplace