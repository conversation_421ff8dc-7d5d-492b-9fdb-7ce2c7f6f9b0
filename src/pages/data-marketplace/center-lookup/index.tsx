import React from "react";
import InfoCard from "@/components/common/InfoCard";
import Box from "@mui/material/Box";
import TabList from "@mui/lab/TabList";
import {Tab} from "@mui/material";
import TabContext from "@mui/lab/TabContext";
import RecentActivityTracker from "@/constants/recentActivityTracker";
import {CENTER_LOOKUP_URLS} from "@/constants/constants";
import TabPanel from "@mui/lab/TabPanel";

const CenterResources = () => {
    RecentActivityTracker('Center Resources');
    const breadcrumbs = [
        {label: 'Data Concierge', href: '/'},
        {label: 'Data Marketplace', href: '/data-marketplace'},
        {label: 'Center Resources', href: ''},
    ]
    const [tabValue, setTabValue] = React.useState(0);


    const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
        setTabValue(newValue);
    };

    return (<div>
        <InfoCard breadcrumbs={breadcrumbs} header="Center Resources"
                  description="Use the tabs below to access center-specific sites for center-specific dashboards."
                  icon={`${process.env.NEXT_PUBLIC_BASE_PATH}/img/center-look-up.svg`}/>
        <TabContext value={tabValue}>
            <Box sx={{borderBottom: 1, borderColor: 'divider'}}>
                <TabList onChange={handleTabChange} aria-label="Center Lookup Tabs">
                    {CENTER_LOOKUP_URLS.map((tab, index) => (
                        <Tab label={tab.center} value={index} key={index}/>
                    ))}
                </TabList>
            </Box>
            {CENTER_LOOKUP_URLS.map((tab, index) => (
                <TabPanel value={index} key={index}>
                    <iframe height="100%" width="100%"
                            src={tab.url}
                            title={tab.center}></iframe>
                </TabPanel>
            ))}
        </TabContext>
    </div>)
}

export default CenterResources;

CenterResources.getLayout = (page: React.ReactElement) => page;