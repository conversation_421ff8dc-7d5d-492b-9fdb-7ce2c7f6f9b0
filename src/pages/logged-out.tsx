import React from 'react';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import Box from '@mui/material/Box';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';

export default function LoggedOutPage() {

    const basePath = process.env.NEXT_PUBLIC_BASE_PATH || '';
    const loginHref = process.env.NODE_ENV === 'development' ? '/api/auth/dev-login' : '/api/auth/saml/login';
    const fullLoginHref = `${basePath}${loginHref}`;

    return (
        <Container maxWidth="sm" sx={{mt: 8, textAlign: 'center'}}>
            <Box sx={{mb: 3}}>
                <CheckCircleOutlineIcon color="success" sx={{fontSize: 60}}/>
            </Box>
            <Typography variant="h6" component="h1" gutterBottom>
                GoodBye! You have been successfully logged out.
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{mb: 4}}>
                Your session has ended. Thank you for using the Data Concierge application.
            </Typography>
            <Button
                variant="contained"
                color="primary"
                size="large"
                component="a"
                href={fullLoginHref}
            >
                Log In Again
            </Button>
        </Container>
    );
}