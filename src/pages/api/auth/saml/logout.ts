import type {NextApiRequest, NextApiResponse} from 'next';
import {getIronSession, IronSession, IronSessionData} from 'iron-session';
import {sessionOptions} from '@/lib/session';
import {User} from "@/lib/types";

export default async function logoutRoute(req: NextApiRequest, res: NextApiResponse) {
    const session: IronSession<IronSessionData> = await getIronSession(req, res, sessionOptions);
    const userEmail = session.user?.email || 'User';
    const basePath = process.env.NEXT_PUBLIC_BASE_PATH || '';

    try {
        session.destroy();
        console.log(`API Route: /api/auth/saml/logout - ${userEmail} logged out (session destroyed)`);
        // Prevent caching of this redirect response
        res.setHeader('Cache-Control', 'no-store, max-age=0');
        res.setHeader('Pragma', 'no-cache');
        res.setHeader('Expires', '0');
        res.redirect(302, basePath + '/logged-out');

    } catch (error) {
        console.error("Error during session destruction", error);
        // Still try to redirect even if destroy fails somehow
        res.redirect(302, basePath+'/logged-out?error=session_destroy_failed');
    }
}