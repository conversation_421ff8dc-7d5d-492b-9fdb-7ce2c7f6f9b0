import type {NextApiRequest, NextApiResponse} from 'next';
import {samlStrategy} from '@/lib/passportSamlStrategy'; // Import strategy instance
import fs from 'fs';

export default function samlMetadata(req: NextApiRequest, res: NextApiResponse) {
    console.log('API Route: /api/auth/saml/metadata START');
    if (req.method !== 'GET') {
        res.setHeader('Allow', 'GET');
        return res.status(405).end('Method Not Allowed');
    }

    try {
        // Read the SP's public certificate if path defined (for metadata signature/encryption fields)
        const spPublicCertContent = process.env.SP_CERT_PATH
            ? fs.readFileSync(process.env.SP_CERT_PATH, 'utf-8')
            : null;

        // Generate metadata using the configured strategy instance
        // Pass cert content for decryption and signing elements in metadata
        const metadata = samlStrategy.generateServiceProviderMetadata(
            spPublicCertContent,
            spPublicCertContent
        );

        console.log("SP Metadata Generated Successfully");
        res.setHeader('Content-Type', 'application/xml');
        res.status(200).send(metadata);

    } catch (error: any) {
        console.error("Error Generating SP Metadata", error);
        if (error.code === 'ENOENT' && process.env.SP_CERT_PATH) {
            res.status(500).send(`Error generating metadata: Certificate file not found at path specified in SP_CERT_PATH: ${process.env.SP_CERT_PATH}.`);
        } else {
            res.status(500).send(`An unexpected error occurred while generating Service Provider metadata: ${error.message}`);
        }
    }
}