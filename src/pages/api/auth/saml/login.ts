import type {NextApiRequest, NextApiResponse} from 'next';
import {passport} from '@/lib/passportSamlStrategy';


export default async function samlLoginRoute(req: NextApiRequest, res: NextApiResponse) {

    const returnTo = req.query.returnTo as string | undefined; // Get from query param
    let relayStateValue: string | undefined = undefined;

    if (returnTo) {
        if (returnTo.startsWith('/') && !returnTo.startsWith('//') && !returnTo.includes('..')) {
            relayStateValue = returnTo;
        }
    }


    const authOptions: any = {
        session: false,
        additionalParams:{
            RelayState: relayStateValue,// Pass the validated path here
        }
    };

    passport.authenticate('saml', authOptions, (err: any) => {
        if (err) {
            console.error('Passport SAML Initiation Error', err);
            res.status(500).send(`Error during SAML login initiation: ${err.message || 'Unknown error'}`);
            return;
        }
        // If passport doesn't redirect or error, something is wrong.
        if (!res.writableEnded) {
            console.error("ERROR: Passport authenticate ('saml', login) finished without sending a response. Check strategy config (entryPoint, issuer).");
            res.status(500).send('SAML login initiation failed to redirect.');
        }
    })(req, res);
}