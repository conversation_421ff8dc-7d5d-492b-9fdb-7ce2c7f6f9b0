import type {NextApiRequest, NextApiResponse} from 'next';
import {getIronSession, IronSession, IronSessionData} from 'iron-session';
import {passport} from '@/lib/passportSamlStrategy';
import {sessionOptions} from '@/lib/session';
import type {User} from '@/lib/types';


export default async function samlCallbackRoute(req: NextApiRequest, res: NextApiResponse) {
    const session: IronSession<IronSessionData> = await getIronSession(req, res, sessionOptions);
    const relayState = req.body?.RelayState as string | undefined;

    passport.authenticate('saml', {session: false}, async (err: any, user: User | false | null, info: any) => {
        const basePath = process.env.NEXT_PUBLIC_BASE_PATH || '';
        if (err || !user) {
            const errorMessage = err?.message || info?.message || 'SAML authentication failed.';
            console.error('SAML Authentication Failed', err || info);
            // Redirect to  error page
            const errorRedirectUrl = new URL(`${basePath}/saml-error`, process.env.APP_BASE_URL || `https://${req.headers.host}`);
            errorRedirectUrl.searchParams.set('message', errorMessage);
            errorRedirectUrl.searchParams.set('details', JSON.stringify(err || info || {})); // Optional details
            return res.redirect(errorRedirectUrl.toString());
        }

        try {

            let redirectTo = '/'; // Default redirect target
            if (relayState && relayState.startsWith('/') && !relayState.startsWith('//') && !relayState.includes('..')) {
                redirectTo = relayState;
            }

            // Save user data into the iron-session
            session.user = user;
            await session.save();

            redirectTo = `${basePath}${redirectTo}`;
            if (redirectTo !== '/' && redirectTo.endsWith('/'))
                redirectTo = redirectTo.slice(0, -1);
            res.redirect(302, redirectTo);

        } catch (saveError: any) {

            console.error("Error saving session", saveError);
            const errorRedirectUrl = new URL(`${basePath}/saml-error`, process.env.APP_BASE_URL || `http://${req.headers.host}`);
            errorRedirectUrl.searchParams.set('message', 'Failed to save user session after login.');
            return res.redirect(302, errorRedirectUrl.toString());
        }

    })(req, res);
}