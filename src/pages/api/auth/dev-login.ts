import type {NextApiRequest, NextApiResponse} from 'next';
import {getIronSession, IronSession, IronSessionData} from 'iron-session';
import {sessionOptions} from '@/lib/session';

export default async function devLoginRoute(req: NextApiRequest, res: NextApiResponse) {

    if (process.env.NODE_ENV !== 'development') {
        res.status(403).send('Forbidden in production');
        return;
    }

    const session: IronSession<IronSessionData> = await getIronSession(req, res, sessionOptions);
    const basePath = process.env.NEXT_PUBLIC_BASE_PATH || '';
    const returnTo = req.query.returnTo as string | undefined; // Get from query param

    if (returnTo) {
        if (returnTo.startsWith('/') && !returnTo.startsWith('//') && !returnTo.includes('..')) {
            session.returnTo = returnTo;
        } else {
            if (session.returnTo) delete session.returnTo;
        }
    } else {
        if (session.returnTo) delete session.returnTo;
    }

    console.log('[DEV LOGIN API] Simulating successful login');

    // Create mock user data
    session.user = {
        displayName: process.env.USERNAME || 'Odt, Dev User',
        username: process.env.USERNAME || 'odt.dev.user',
        email: '<EMAIL>',
        firstName: 'Odt',
        lastName: 'Dev User',
        officeName: 'OC/ODT'
    };

    await session.save();
    let redirectTo = `${basePath}${session.returnTo || '/'}`;

    if (redirectTo !== '/' && redirectTo.endsWith('/'))
        redirectTo = redirectTo.slice(0, -1);

    res.redirect(302, redirectTo);
}