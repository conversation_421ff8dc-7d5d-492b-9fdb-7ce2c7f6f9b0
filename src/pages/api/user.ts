import type {NextApiRequest, NextApiResponse} from 'next';
import {getIronSession, IronSession, IronSessionData} from 'iron-session';
import {sessionOptions} from '@/lib/session';


export default async function userRoute(req: NextApiRequest, res: NextApiResponse) {
    res.setHeader('Cache-Control', 'no-store, max-age=0');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
    const session: IronSession<IronSessionData> = await getIronSession(req, res, sessionOptions);

    if (session.user) {
        const {email, firstName, lastName, displayName, username, officeName} = session.user;
        res.status(200).json({user: {email, firstName, lastName, displayName, username, officeName}});
    } else {
        console.debug(`[/api/user] No valid session found. Sending 401.`);
        res.status(401).json({user: null});
    }
}