import React, {useEffect, useState} from 'react';
import {Alert, CircularProgress} from '@mui/material';
import InfoCard from "@/components/common/InfoCard";
import PaginatedTable from "@/components/common/PaginatedTable";
import Box from "@mui/material/Box"; // or any other HTTP client
import appService from "@/services/app.service";
import RecentActivityTracker from "@/constants/recentActivityTracker";


const Policy = () => {
    RecentActivityTracker('Policy');

    const [policyData, setPolicyData] = useState([]);
    const [initialLoad, setInitialLoad] = useState(true);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');

    const breadcrumbs = [
        {label: 'Data Concierge', href: '/'},
        {label: 'Data Governance', href: '/data-governance'},
        {label: 'Policy', href: ''},
    ]

    useEffect(() => {
        (async () => {
            try {
                await appService.getPolicyResults().then(data => {
                    if (data.AXON != null){
                        let policyDataResponse = JSON.parse(data.AXON).searchList;
                        if(policyDataResponse && policyDataResponse.length > 0){
                            policyDataResponse = policyDataResponse.sort((a:any, b:any)=> Number(a.id)-Number(b.id));
                        }
                        setPolicyData(policyDataResponse);
                    }
                });
            } catch (error) {
                setError(String(error));
                console.error('Error fetching data:', error);
            } finally {
                setLoading(false);
                setInitialLoad(false);
            }
        })();
    }, []);

    const axonHeaders = [
        {label: 'Ref.', key: 'id', flex: 0.3,type:'number'},
        {label: 'Name', key: 'name', flex: 1},
        {label: 'Parent Name', key: 'parentName', flex: 1},
        {label: 'Definition', key: 'description', flex: 2},
        {label: 'Lifecycle', key: 'lifecycle', flex: 0.3},


    ]

    return (
        <div>
            <InfoCard breadcrumbs={breadcrumbs} header="Policy"
                      description="The Policy Page provides data governance policy information from Axon. Search for policy information from Axon using search box below. "
                      icon={`${process.env.NEXT_PUBLIC_BASE_PATH}/img/policy.svg`}/>

            {/* Table to display results */}
            {loading && <Box sx={{width: '100%', mt: 1}}>
                <CircularProgress/>
            </Box>} {/* Display loading message while fetching data */}

            {!initialLoad && !loading && error === '' && policyData.length === 0 &&
                <Alert sx={{margin: '10px 0 0 0', width: 'fit-content'}} severity="error">No results
                    found</Alert>}


            <Box sx={{width: '100%', typography: 'body1', mt: 2}}>
                <PaginatedTable system='axon' data={policyData} headers={axonHeaders}/>
            </Box>


        </div>
    );
}
export default Policy;

Policy.getLayout = (page: React.ReactElement) => page;
