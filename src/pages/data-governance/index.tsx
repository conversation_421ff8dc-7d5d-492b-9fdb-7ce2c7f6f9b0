import {Box} from "@mui/material";
import CatalogCard from "@/components/common/CatalogCard";
import InfoCard from "@/components/common/InfoCard";
import React from "react";
import RecentActivityTracker from "@/constants/recentActivityTracker";
import {catalogStyles} from "@/styles/catalogStyles";
import {CATALOG_TILES} from "@/constants/catalogs";

const breadcrumbs = [
    {label: 'Data Concierge', href: '/'},
    {label: 'Data Governance', href: ''}
]

export default function DataGovernance() {
    RecentActivityTracker('Data Governance');
    const classes = catalogStyles();
    const dataGovernance = CATALOG_TILES.governance.items;
    return (
        <div>
            <InfoCard breadcrumbs={breadcrumbs} header="Data Governance Catalog"
                      description="Welcome to the Data Governance page. Navigate to the data governance components by clicking on the cards below."
                      icon={`${process.env.NEXT_PUBLIC_BASE_PATH}/img/data-governance-dark.svg`}/>

            <Box className={classes.cardContentBox}>
                {dataGovernance.map((item, index) => (
                    <CatalogCard key={index} name={item.name} path={item.path} iconPath={item.image}
                                 subtext={item.subtext} show_options={item.show_options} options={item.options}
                                 description={item.description} external_url={item.external_url}/>
                ))}
            </Box>
        </div>
    )
}


DataGovernance.getLayout = (page: React.ReactElement) => page;
