import React from 'react';
import InfoCard from "@/components/common/InfoCard";
import RecentActivityTracker from "@/constants/recentActivityTracker";
import {MOU_BUILDER_URL} from "@/constants/constants";
import Button from "@mui/material/Button";
import {Link} from "@mui/material";
import {OpenInNew} from "@mui/icons-material";


const DocumentBuilder = () => {
    RecentActivityTracker('Document Builder');
    const breadcrumbs = [
        {label: 'Data Concierge', href: '/'},
        {label: 'Data Governance', href: '/data-governance'},
        {label: 'Document Builder', href: ''},
    ]

    return (
        <div>
            <InfoCard breadcrumbs={breadcrumbs} header="Data Governance Document Builder"
                      description="Generate Data Governance Documents"
                      HeaderFunctions={OpenInNewTabButton()}
                      icon={`${process.env.NEXT_PUBLIC_BASE_PATH}/img/mou_builder.jfif`}/>


            <iframe height="100%" width="100%"
                    src="https://cderonegpt-dev.preprod.fda.gov/SemossWeb/packages/client/dist/#/s/6f83638c-e55b-4803-9e1e-ee48940e64fb"
                    title='Data Governance Document Builder'></iframe>


        </div>
    );
}

const OpenInNewTabButton = () => {
    return (<>
        <Button sx={{
            position: 'absolute',
            right: 10
        }} component={Link} target="_blank" rel="noopener noreferrer"
                href={MOU_BUILDER_URL}
                variant="contained" size='small' color="primary">
            Open in New Tab&nbsp;<OpenInNew/></Button>
    </>)
}

export default DocumentBuilder;

DocumentBuilder.getLayout = (page: React.ReactElement) => page;
