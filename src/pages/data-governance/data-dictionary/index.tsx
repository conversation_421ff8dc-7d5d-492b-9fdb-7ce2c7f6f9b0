import React, {useState} from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON>, CircularProgress, Tab, TextField} from '@mui/material';
import Grid from '@mui/material/Grid2'
import InfoCard from "@/components/common/InfoCard";
import PaginatedTable from "@/components/common/PaginatedTable";
import TabContext from '@mui/lab/TabContext';
import TabList from '@mui/lab/TabList';
import TabPanel from '@mui/lab/TabPanel';
import Box from "@mui/material/Box";
import {useFormik} from "formik";
import {data_dictionary_validation_schema} from "@/constants/validations"; // or any other HTTP client
import appService from "@/services/app.service";
import RecentActivityTracker from "@/constants/recentActivityTracker";


const DataDictionary = () => {
    RecentActivityTracker('Data Dictionary');

    const formik = useFormik({
        initialValues: {
            searchTerm: '',
            center: 'All'
        },
        validationSchema: data_dictionary_validation_schema,
        onSubmit: (values) => {
            fetchData(values)
        },
    });


    const [alationData, setAlationData] = useState([]);
    const [axonFieldData, setAxonFieldData] = useState([]);
    const [axonDatasetData, setAxonDatasetData] = useState([]);
    const [initialLoad, setInitialLoad] = useState(true);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');

    const breadcrumbs = [
        {label: 'Data Concierge', href: '/'},
        {label: 'Data Governance', href: '/data-governance'},
        {label: 'Data Dictionary', href: ''},
    ]

    const centerSearchOptions = ['All', 'CDER', 'CBER', 'CTP', 'HFP'];

    // Function to handle API call
    const fetchData = async (searchParams: any) => {
        resetValues();
        setLoading(true);
        try {

            await appService.getDataDictionaryResults(searchParams).then(data => {
                if (data?.AXON != null) {
                    setAxonFieldData(JSON.parse(data?.AXON).dataDictionaryList[0].searchList);
                    setAxonDatasetData(JSON.parse(data?.AXON).dataDictionaryList[1].searchList);
                    setTabValue('2');
                }
                if (data?.ALATION != null) {
                    setAlationData(JSON.parse(data?.ALATION));
                    setTabValue('1');
                }
            })

        } catch (err) {
            setError(String(err));
            console.error("Error fetching data:", err);
        } finally {
            setLoading(false);
            setInitialLoad(false);
        }
    };

    function resetValues() {
        setLoading(false);
        setError('');
        setAlationData([]);
        setAxonFieldData([]);
        setAxonDatasetData([]);
        setInitialLoad(true);

    }

    const alationHeaders = [
        {label: 'Name', key: 'name', flex: 1},
        {label: 'Title', key: 'title', flex: 1},
        {label: 'Type', key: 'type', flex: 0.5},
        {label: 'Data Source', key: 'data', flex: 1},
        {label: 'Schema', key: 'schema', flex: 1},
        {label: 'Table', key: 'table', flex: 1.5},
    ]

    const axonFieldHeaders = [
        {label: 'Name', key: 'name', flex: 1},
        {label: 'Type', key: 'fieldType', flex: 0.5},
        {label: 'Parent', key: 'physicalFieldTable', flex: 0.5},
        {label: 'Resource', key: 'physicalFieldResource', flex: 1},
        {label: 'Resource Type', key: 'fieldResourceType', flex: 0.3},
        {label: 'Path', key: 'physicalFieldPath', flex: 2}
    ]


    const axonDataSetHeaders = [
        {label: 'Name', key: 'name', flex: 1},
        {label: 'Definition', key: 'definition', flex: 2.5},
        {label: 'Lifecycle', key: 'lifecycle', flex: 0.5},
        {label: 'System Name', key: 'systemName', flex: 1},
    ]

    const [tabValue, setTabValue] = React.useState('1');
    const [axonTabValue, setAxonTabValue] = React.useState('21');

    const handleChange = (event: React.SyntheticEvent, newValue: string) => {
        setTabValue(newValue);
    };

    const handleAxonChange = (event: React.SyntheticEvent, newValue: string) => {
        setAxonTabValue(newValue);
    };


    return (
        <div>
            <InfoCard breadcrumbs={breadcrumbs} header="Data Dictionary"
                      description="The Data Dictionary allows users to search for data attributes across FDA Centers. Search for data terms to see their definitions using the search box below."
                      icon={`${process.env.NEXT_PUBLIC_BASE_PATH}/img/data-dictionary.svg`}></InfoCard>

            {/* Input Fields */}
            <form onSubmit={formik.handleSubmit}>
                <Grid container spacing={2}>
                    <Grid size={{md: 6, xs: 12}}>
                        <TextField fullWidth
                                   id="searchTerm"
                                   name="searchTerm"
                                   label="Search Term"
                                   value={formik.values.searchTerm}
                                   onChange={formik.handleChange}
                                   onBlur={formik.handleBlur}
                                   error={formik.touched.searchTerm && Boolean(formik.errors.searchTerm)}
                                   helperText={formik.touched.searchTerm && formik.errors.searchTerm}
                                   variant="outlined"
                                   size="small"

                        />
                    </Grid>
                    {/*<Grid size={{xs: 1.5}}>*/}
                    {/*    <FormControl fullWidth>*/}
                    {/*        <InputLabel id="center-select-label">Center</InputLabel>*/}
                    {/*        <Select*/}
                    {/*            id="center"*/}
                    {/*            name="center"*/}
                    {/*            label="Center"*/}
                    {/*            value={formik.values.center}*/}
                    {/*            onChange={formik.handleChange}*/}
                    {/*            onBlur={formik.handleBlur}*/}
                    {/*            error={formik.touched.center && Boolean(formik.errors.center)}*/}
                    {/*            variant="outlined"*/}
                    {/*            size="small"*/}
                    {/*            sx={{mr: 2, minWidth: 120}} // Add some margin to the right and set a minimum width*/}
                    {/*        >*/}
                    {/*            {centerSearchOptions.map((option) => (*/}
                    {/*                <MenuItem key={option} value={option}>*/}
                    {/*                    {option}*/}
                    {/*                </MenuItem>*/}
                    {/*            ))}*/}
                    {/*        </Select>*/}
                    {/*    </FormControl>*/}
                    {/*</Grid>*/}
                    <Grid size={{md: 4}}>
                        <Button type="submit" size="medium" sx={{mr: 2}} variant="contained">Search</Button>
                        <Button size="medium" sx={{mr: 2}} variant="contained"
                                onClick={() => {
                                    formik.resetForm();
                                    resetValues();
                                    setLoading(false);
                                }}>Clear</Button>
                    </Grid>
                </Grid>
            </form>

            {/* Table to display results */}
            {loading && <Box sx={{width: '100%', mt: 1}}>
                <CircularProgress/>
            </Box>} {/* Display loading message while fetching data */}
            {(!initialLoad && !loading && error == '' && (alationData.length === 0 && axonDatasetData.length === 0 && axonFieldData.length === 0)) &&
                <Alert sx={{margin: '10px 0 0 0', width: 'fit-content'}} severity="error">No results
                    found</Alert>}


            {(alationData.length > 0 || axonFieldData.length > 0 || axonDatasetData.length > 0) &&
                <Box sx={{width: '100%', typography: 'body1', mt: 2}}>
                    <Alert sx={{paddingTop: 0, paddingBottom: 0, marginBottom: 1}} severity="info">Data Dictionary
                        information originates in various source systems. They are automatically scanned and collected by
                        platforms like Alation, EDC, and Axon, and then presented here exactly as recorded in those
                        sources.</Alert>

                    <TabContext value={tabValue}>
                        <Box sx={{borderBottom: 1, borderColor: 'divider'}}>
                            <TabList onChange={handleChange} aria-label="lab API tabs example">
                                <Tab label="Alation" value="1"/>
                                <Tab label="Axon" value="2"/>
                            </TabList>
                        </Box>
                        <TabPanel value="1">{alationData.length > 0 ?
                            <PaginatedTable system='alation' data={alationData}
                                            headers={alationHeaders}></PaginatedTable> :
                            <Alert sx={{margin: '10px 0 0 0', width: 'fit-content'}} severity="error">No results
                                found</Alert>}</TabPanel>
                        <TabPanel value="2">{(axonFieldData.length > 0 || axonDatasetData.length > 0) ?
                            <TabContext value={axonTabValue}>
                                <Box sx={{borderBottom: 1, borderColor: 'divider'}}>
                                    <TabList onChange={handleAxonChange} aria-label="lab API tabs example">
                                        <Tab label="Field" value="21"/>
                                        <Tab label="Dataset" value="22"/>
                                    </TabList>
                                </Box>
                                <TabPanel value="21">{axonFieldData.length > 0 ?
                                    <PaginatedTable system='axon' data={axonFieldData}
                                                    headers={axonFieldHeaders}></PaginatedTable> :
                                    <Alert sx={{margin: '10px 0 0 0', width: 'fit-content'}} severity="error">No results
                                        found</Alert>}</TabPanel>
                                <TabPanel value="22">{axonDatasetData.length > 0 ?
                                    <PaginatedTable system='axon' data={axonDatasetData}
                                                    headers={axonDataSetHeaders}></PaginatedTable> :
                                    <Alert sx={{margin: '10px 0 0 0', width: 'fit-content'}}
                                           severity="error">No results
                                        found</Alert>}
                                </TabPanel>
                            </TabContext> :
                            <Alert sx={{margin: '10px 0 0 0', width: 'fit-content'}} severity="error">No results
                                found</Alert>}
                        </TabPanel>
                    </TabContext>
                </Box>}


        </div>
    );
}
export default DataDictionary;

DataDictionary.getLayout = (page: React.ReactElement) => page;
