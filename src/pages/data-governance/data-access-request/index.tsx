'use client'
import React from 'react';
import InfoCard from "@/components/common/InfoCard";
import RecentActivityTracker from "@/constants/recentActivityTracker";
import DataAccessRequest from "@/components/common/DataAccessRequest";


const AccessRequestPage: React.FC = () => {


    RecentActivityTracker('Data Access Request');

    const breadcrumbs = [
        {label: 'Data Concierge', href: '/'},
        {label: 'Data Governance', href: '/data-governance'},
        {label: 'Data Access Request', href: ''},
    ]


    return (
        <><InfoCard breadcrumbs={breadcrumbs} header="Data Access Request"
                    description="Submit the below form to process the Data Access Request using ABAC/RBAC."
                    icon={`${process.env.NEXT_PUBLIC_BASE_PATH}/img/access-request.svg`}></InfoCard>
            <DataAccessRequest/>
        </>
    )

};

export default AccessRequestPage;