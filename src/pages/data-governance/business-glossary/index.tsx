import React, {useState} from 'react';
import {
    Alert,
    Button,
    CircularProgress,
    FormControl,
    InputLabel,
    MenuItem,
    Select,
    Tab,
    TextField
} from '@mui/material';
import Grid from '@mui/material/Grid2'
import InfoCard from "@/components/common/InfoCard";
import PaginatedTable from "@/components/common/PaginatedTable";
import TabContext from '@mui/lab/TabContext';
import TabList from '@mui/lab/TabList';
import TabPanel from '@mui/lab/TabPanel';
import Box from "@mui/material/Box";
import appService from "@/services/app.service";
import {useFormik} from "formik";
import {business_glossary_validation_schema} from "@/constants/validations";
import RecentActivityTracker from "@/constants/recentActivityTracker";


const BusinessGlossary = () => {

    RecentActivityTracker('Business Glossary');

    const formik = useFormik({
        initialValues: {
            searchTerm: '',
            center: 'All'
        },
        validationSchema: business_glossary_validation_schema,
        onSubmit: (values) => {
            fetchData(values)
        },
    });


    const [alationData, setAlationData] = useState([]);
    const [axonData, setAxonData] = useState([]);
    const [initialLoad, setInitialLoad] = useState(true);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');

    const breadcrumbs = [
        {label: 'Data Concierge', href: '/'},
        {label: 'Data Governance', href: '/data-governance'},
        {label: 'Business Glossary', href: ''},
    ]

    const centerSearchOptions = ['All', 'CDER', 'CBER', 'CTP', 'HFP'];


    // Function to handle API call
    const fetchData = async (searchParams: any) => {
        resetValues();
        setLoading(true);
        try {
            await appService.getBusinessGlossaryResults(searchParams).then(data => {
                if (data?.AXON != null) {
                    if (JSON.parse(data?.AXON).searchList != null) {
                        setAxonData(JSON.parse(data?.AXON).searchList);
                    }
                    setTabTabValue('2');
                }
                if (data?.ALATION != null) {
                    setAlationData(JSON.parse(data?.ALATION));
                    setTabTabValue('1');
                }
            })


        } catch (err) {
            setError(String(err));
            console.error("Error fetching data:", err);
        } finally {
            setLoading(false);
            setInitialLoad(false);
        }
    };

    function resetValues() {
        setLoading(false);
        setError('');
        setAlationData([]);
        setAxonData([]);
        setInitialLoad(true);
    }

    const alationHeaders = [
        {label: 'Term', key: 'term', flex: 1},
        {label: 'Type', key: 'term_type', flex: 0.5},
        {label: 'Description', key: 'term_description', flex: 2},
        {label: 'Glossary', key: 'glossaries', flex: 1},
    ]

    const axonHeaders = [
        {label: 'Name', key: 'name', flex: 1},
        {label: 'Type', key: 'type', flex: 0.5},
        {label: 'Definition', key: 'description', flex: 2},
        {label: 'Parent Name', key: 'parentName', flex: 1},
        {label: 'Parent Type', key: 'parentType', flex: 0.5},
    ]


    const [tabValue, setTabTabValue] = React.useState('1');

    const handleTabChange = (event: React.SyntheticEvent, newValue: string) => {
        setTabTabValue(newValue);
    };

    return (
        <div>
            <InfoCard breadcrumbs={breadcrumbs} header="Business Glossary"
                      description="The Business Glossary allows users to search for data attributes across FDA Centers. Search for data terms to see their definitions using the search box below."
                      icon={`${process.env.NEXT_PUBLIC_BASE_PATH}/img/business-glossary.svg`}/>
            {/* Input Fields */}
            <form onSubmit={formik.handleSubmit}>
                <Grid container spacing={2}>
                    <Grid size={{md: 6, xs: 12}}>
                        <TextField fullWidth
                                   id="searchTerm"
                                   name="searchTerm"
                                   label="Search Term"
                                   value={formik.values.searchTerm}
                                   onChange={formik.handleChange}
                                   onBlur={formik.handleBlur}
                                   error={formik.touched.searchTerm && Boolean(formik.errors.searchTerm)}
                                   helperText={formik.touched.searchTerm && formik.errors.searchTerm}
                                   variant="outlined"
                                   size="small"
                        />
                    </Grid>
                    <Grid size={{xs: 1.5}}>
                        <FormControl fullWidth>
                            <InputLabel id="center-select-label">Center</InputLabel>
                            <Select
                                id="center"
                                name="center"
                                label="Center"
                                value={formik.values.center}
                                onChange={formik.handleChange}
                                onBlur={formik.handleBlur}
                                error={formik.touched.center && Boolean(formik.errors.center)}
                                variant="outlined"
                                size="small"
                                sx={{mr: 2, minWidth: 120}} // Add some margin to the right and set a minimum width
                            >
                                {centerSearchOptions.map((option) => (
                                    <MenuItem key={option} value={option}>
                                        {option}
                                    </MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                    </Grid>
                    <Grid size={{md: 4}}>
                        <Button type="submit" size="medium" sx={{mr: 2}} variant="contained">Search</Button>
                        <Button size="medium" sx={{mr: 2}} variant="contained"
                                onClick={() => {
                                    formik.resetForm();
                                    resetValues();
                                    setLoading(false);
                                }}>Clear</Button>
                    </Grid>
                </Grid>
            </form>


            {/* Table to display results */}
            {loading && <Box sx={{width: '100%', mt: 1}}>
                <CircularProgress/>
            </Box>

            }

            {(!initialLoad && !loading && error == '' && (alationData.length === 0 && axonData.length === 0)) &&
                <Alert sx={{margin: '10px 0 0 0', width: 'fit-content'}} severity="error">No results found</Alert>}


            {(alationData.length > 0 || axonData.length > 0) && <Box sx={{width: '100%', typography: 'body1', mt: 2}}>
                <Alert sx={{paddingTop: 0, paddingBottom: 0, marginBottom: 1}} severity="info">Business Glossary
                    definitions originate in various source systems. They are automatically scanned and collected by
                    platforms like Alation, EDC, and Axon, and then presented here exactly as recorded in those
                    sources.</Alert>

                <TabContext value={tabValue}>
                    <Box sx={{borderBottom: 1, borderColor: 'divider'}}>
                        <TabList onChange={handleTabChange} aria-label="lab API tabs example">
                            <Tab label="Alation" value="1"/>
                            <Tab label="Axon" value="2"/>
                        </TabList>
                    </Box>
                    <TabPanel value="1">{alationData.length > 0 ?
                        <PaginatedTable system='alation' data={alationData}
                                        headers={alationHeaders}></PaginatedTable> :
                        <Alert sx={{margin: '10px 0 0 0', width: 'fit-content'}} severity="error">No results
                            found</Alert>}</TabPanel>
                    <TabPanel value="2">{axonData.length > 0 ?
                        <PaginatedTable system='axon' data={axonData}
                                        headers={axonHeaders}></PaginatedTable> :
                        <Alert sx={{margin: '10px 0 0 0', width: 'fit-content'}} severity="error">No results
                            found</Alert>}</TabPanel>
                </TabContext>
            </Box>
            }


        </div>
    );
}
export default BusinessGlossary;

BusinessGlossary.getLayout = (page: React.ReactElement) => page;
