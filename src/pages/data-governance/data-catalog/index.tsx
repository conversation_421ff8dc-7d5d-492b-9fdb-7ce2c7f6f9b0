import {Box, Button, Link} from "@mui/material";
import CatalogCard from "@/components/common/CatalogCard";
import InfoCard from "@/components/common/InfoCard";
import React from "react";
import RecentActivityTracker from "@/constants/recentActivityTracker";
import {catalogStyles} from "@/styles/catalogStyles";
import {CATALOG_TILES} from "@/constants/catalogs";
import ArticleIcon from '@mui/icons-material/Article';

const breadcrumbs = [
    {label: 'Data Concierge', href: '/'},
    {label: 'Data Governance', href: '/data-governance'},
    {label: 'FDA Data Catalog', href: ''},
]


export default function DataCatalog() {
    RecentActivityTracker('FDA Data Catalog');
    const classes = catalogStyles();

    const dataProductCatalog = CATALOG_TILES.data_catalog.items;

    return (
        <div>
            <InfoCard breadcrumbs={breadcrumbs} header="FDA Data Catalog"
                      description="Access FDA Data Assets (Metadata)"
                      icon={`${process.env.NEXT_PUBLIC_BASE_PATH}/img/data-asset.svg`}/>

            <Box className={classes.cardContentBox}>
                {dataProductCatalog.map((item, index) => (
                    <CatalogCard key={index} name={item.name} path={item.path} iconPath={item.image}
                                 show_options={item.show_options} options={item.options}
                                 description={item.description} external_url={item.external_url}/>
                ))}
            </Box>
        </div>
    )
}


DataCatalog.getLayout = (page: React.ReactElement) => page;
