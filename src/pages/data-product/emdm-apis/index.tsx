'use client'

import React, {SyntheticEvent} from "react";
import InfoCard from "@/components/common/InfoCard";
import Box from "@mui/material/Box";
import TabList from "@mui/lab/TabList";
import {FormControl, InputLabel, MenuItem, Select, SelectChangeEvent, Tab} from "@mui/material";
import TabPanel from "@mui/lab/TabPanel";
import TabContext from "@mui/lab/TabContext";
import RecentActivityTracker from "@/constants/recentActivityTracker";
import SwaggerUI from "swagger-ui-react"
import "swagger-ui-react/swagger-ui.css"

const CenterLookup = () => {
    RecentActivityTracker('eMDM APIs');
    const breadcrumbs = [
        {label: 'Data Concierge', href: '/'},
        {label: 'Data Products & Services', href: '/data-product'},
        {label: 'eMDM APIs', href: ''},
    ]

    const ALATION_OPEN_API_CONFIG = [
        {
            "API": "Alation Agent",
            "YAML": "agent.yaml"
        },
        {
            "API": "API Authentication",
            "YAML": "api_authentication.yaml"
        },
        {
            "API": "Articles",
            "YAML": "articles.yaml"
        },
        {
            "API": "Business Intelligence",
            "YAML": "gbmv2.yaml"
        },
        {
            "API": "Connectors (OCF)",
            "YAML": "connectors.yaml"
        },
        {
            "API": "Conversations v2",
            "YAML": "conversations.yaml"
        },
        {
            "API": "Data Health",
            "YAML": "data_quality.yaml"
        },
        {
            "API": "Data Sources (Native)",
            "YAML": "datasources.yaml"
        },
        {
            "API": "Data Sources (OCF)",
            "YAML": "ocf_datasources.yaml"
        },
        {
            "API": "Domains",
            "YAML": "domain.yaml"
        },
        {
            "API": "Groups",
            "YAML": "group.yaml"
        },
        {
            "API": "Lineage and Dataflow",
            "YAML": "lineage.yaml"
        },
        {
            "API": "oTypes",
            "YAML": "otypes.yaml"
        },
        {
            "API": "Policy and Policy Groups",
            "YAML": "policy.yaml"
        },
        {
            "API": "Search",
            "YAML": "search.yaml"
        },
        {
            "API": "RDBMS Integration",
            "YAML": "integration_apis.yaml"
        },
        {
            "API": "Terms",
            "YAML": "terms.yaml"
        },
        {
            "API": "Users",
            "YAML": "user.yaml"
        },
        {
            "API": "Users v2",
            "YAML": "userv2.yaml"
        },
        {
            "API": "Workflows",
            "YAML": "workflows.yaml"
        }
    ]

    const [tabValue, setTabValue] = React.useState('1');
    const [alationAPISelection, setAlationAPISelection] = React.useState('search.yaml');

    const handleTabChange = (event: SyntheticEvent<Element, Event>, newValue: string) => {
        setTabValue(newValue);
    };

    const handleAlationAPIChange = (event: SelectChangeEvent) => {
        setAlationAPISelection(event.target.value);
    }

    return (<div>
        <InfoCard breadcrumbs={breadcrumbs} header="eMDM APIs"
                  description="Use the tabs below to access eMDM, Alation, Axon and EDC API's."
                  icon={`${process.env.NEXT_PUBLIC_BASE_PATH}/img/center-look-up.svg`}/>
        <TabContext value={tabValue}>
            <Box sx={{borderBottom: 1, borderColor: 'divider'}}>
                <TabList onChange={handleTabChange} aria-label="D&B Tabs">
                    <Tab label="EMDM API" value="1"/>
                    <Tab label="Alation API" value="2"/>
                    <Tab label="Axon API" value="3"/>
                    <Tab label="EDC API" value="4"/>
                </TabList>
            </Box>
            <TabPanel value="1">
                <embed
                    style={{
                        width: '100%',
                        height: '100vh'
                    }}
                    type='application/pdf'
                    src={`${process.env.NEXT_PUBLIC_BASE_PATH}/docs/eMDM MuleSoft API handbook.pdf`}
                />
            </TabPanel>

            <TabPanel value="2">
                <FormControl sx={{mr: 2, minWidth: '50%'}}>
                    <InputLabel id="demo-simple-select-label">Alation API</InputLabel>
                    <Select variant="outlined" size="small" labelId="demo-simple-select-label"

                            label="Alation API"
                            id="demo-simple-select"
                            value={alationAPISelection}
                            onChange={handleAlationAPIChange}
                    >
                        {
                            ALATION_OPEN_API_CONFIG.map((apiItem) => {
                                return <MenuItem value={apiItem.YAML} key={apiItem.API}>{apiItem.API}</MenuItem>
                            })
                        }
                    </Select>
                </FormControl>
                <SwaggerUI url={process.env.NEXT_PUBLIC_BASE_PATH + '/alation-api/' + alationAPISelection}
                           tryItOutEnabled={false} supportedSubmitMethods={[]}/>

            </TabPanel>
            <TabPanel value="3">
                <embed
                    style={{
                        width: '100%',
                        height: '100vh'
                    }}
                    type='application/pdf'
                    src={`${process.env.NEXT_PUBLIC_BASE_PATH}/docs/AXON_54_en.pdf`}
                />
            </TabPanel>
            <TabPanel value="4">
                <embed
                    style={{
                        width: '100%',
                        height: '100vh'
                    }}
                    type='application/pdf'
                    src={`${process.env.NEXT_PUBLIC_BASE_PATH}/docs/EDC_105401_en.pdf`}
                />

            </TabPanel>

        </TabContext>
    </div>)
}

export default CenterLookup;

CenterLookup.getLayout = (page: React.ReactElement) => page;