import * as React from "react";
import {useEffect, useState} from "react";
import {useRouter} from "next/router";
import Drawer from "@mui/material/Drawer";
import FirmDetailsParent from "@/components/address-book/FirmDetailsParent";
import InfoCard from "@/components/common/InfoCard";
import Box from "@mui/material/Box";
import {Container, Divider, Link, Snackbar, SnackbarCloseReason} from "@mui/material";
import Grid from "@mui/material/Grid2"
import Typography from "@mui/material/Typography";
import Button from "@mui/material/Button";
import {OpenInNew, Share} from "@mui/icons-material";
import appService from "@/services/app.service";


const useFetchData = (rowId: string) => {
    const [resultData, setResultData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [mailingAddress, setMailingAddress] = useState(null);
    const [primaryAddress, setPrimaryAddress] = useState<any>(null);
    const [dunsDetails, setDunsDetails] = useState(null);
    const [registrationDetails, setRegistrationDetails] = useState(null);
    const [aliases, setAliases] = useState(null);
    const [doctoredAddress, setDoctoredAddress] = useState(null);

    const fetchData = async () => {
        if (!rowId) return;
        setLoading(true);
        try {
            await appService.getFirmDetails({
                rowid: rowId
            }).then(data => {
                const details = data?.masterEntityDetailsList[0]?.eMDMEntityDetails
                setResultData(details);
                if (details?.addressDetails) {
                    const primary = details.addressDetails.find((a: { addressType: string; }) => a.addressType === "P");
                    const mailing = details.addressDetails.find((a: { addressType: string; }) => a.addressType === "M");
                    setPrimaryAddress(primary || null);
                    setMailingAddress(mailing || null);
                }

                setDunsDetails(details?.dunsDetails ? details.dunsDetails[0] : null);
                setRegistrationDetails(details?.registrationDetails ? details.registrationDetails[0] : null);
                setAliases(details?.aliasDetails ? details.aliasDetails[0] : null);
            });


        } catch (error) {
            console.error('Error fetching data:', error);
            setResultData(null);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        if (rowId) {
            fetchData();
        }
    }, [rowId]);

    useEffect(() => {
        const fetchAddressDoctor = async () => {
            if (primaryAddress?.addressLine1) {
                try {
                    await appService.getAddressDoctorAddress(primaryAddress).then(data => setDoctoredAddress(data?.addressDetails));
                } catch (error) {
                    console.error('Error in fetchAddressDoctor:', error);
                    setDoctoredAddress(null);
                }
            }
        };

        if (primaryAddress) {
            fetchAddressDoctor();
        }
    }, [primaryAddress]);


    return {
        resultData,
        loading,
        primaryAddress,
        mailingAddress,
        dunsDetails,
        registrationDetails,
        aliases,
        doctoredAddress
    };
};

const breadcrumbs = [
    {label: 'Data Concierge', href: '/'},
    {label: 'Data Products & Services', href: '/data-product'},
    {label: 'FDAddress Book', href: '/data-product/address-book'},
    {label: 'Firm Profile', href: ''},
]

const FirmDetailsPage = ({open, onClose, firmRowId}: { open: any, onClose: any, firmRowId: any }) => {
    const router = useRouter();
    const {pathname, basePath} = router;
    const currentPath = basePath + pathname
    const {id} = router.query
    const rowId = firmRowId || id
    const {
        resultData,
        loading,
        primaryAddress,
        mailingAddress,
        dunsDetails,
        registrationDetails,
        aliases,
        doctoredAddress
    } = useFetchData(rowId);

    const [snackBarOpen, setSnackBarOpen] = React.useState(false);

    const showSnackBar = () => {
        setSnackBarOpen(true);
    };

    const snackBarClose = (
        event: React.SyntheticEvent | Event,
        reason?: SnackbarCloseReason,
    ) => {
        if (reason === 'clickaway') {
            return;
        }

        setSnackBarOpen(false);
    };


    if (firmRowId == null) {
        return (<><InfoCard breadcrumbs={breadcrumbs} header="Firm Profile" description={"Viewing details of the firm."}
                            icon={`${process.env.NEXT_PUBLIC_BASE_PATH}/img/FDA-address-book.svg`}/><Box
                sx={{fontFamily: 'Roboto Condensed', maxWidth: '95%'}}><FirmDetailsParent isDrawer={false}
                                                                                          loading={loading}
                                                                                          rowId={rowId}
                                                                                          pathname={currentPath}
                                                                                          primaryAddress={primaryAddress}
                                                                                          doctoredAddress={doctoredAddress}
                                                                                          mailingAddress={mailingAddress}
                                                                                          dunsDetails={dunsDetails}
                                                                                          aliases={aliases}
                                                                                          resultData={resultData}
                                                                                          registrationDetails={registrationDetails}/></Box></>
        )

    } else {
        return (<><Drawer anchor="right" open={open} PaperProps={{
                sx: {minWidth: "60%"},
            }}
                          onClose={onClose}>
                <Box sx={{
                    display: "flex",
                    flexDirection: "row",
                    alignItems: "center",
                }}>
                    <Box
                        component="img"
                        src={`${process.env.NEXT_PUBLIC_BASE_PATH}/img/FDA-address-book.svg`}
                        sx={{
                            width: 60,
                            height: 60,
                            objectFit: 'cover',
                            padding: 1
                        }}/>
                    <Typography variant="h5" sx={{
                        marginTop: 1,
                        marginBottom: 1, color: '#222C67', fontWeight: 'bold'
                    }}>Firm
                        Profile</Typography>
                    {!loading && <Grid container justifyContent="flex-end"
                                       sx={{right: '10px', left: '10px', position: 'absolute'}}>
                        <Button sx={{margin: '5px'}} component={Link} target="_blank"
                                href={currentPath + "/firm-details?id=" + rowId}
                                variant="contained" size='small' color="primary">
                            Open in New Tab&nbsp;<OpenInNew/></Button>
                        <Button sx={{margin: '5px'}} size='small' onClick={() => {
                            navigator.clipboard.writeText(window.location.origin + currentPath + "/firm-details?id=" + rowId);
                            showSnackBar();

                        }} variant="contained" color="primary">
                            Share Link&nbsp;<Share/></Button>
                    </Grid>}
                </Box>
                <Divider sx={{marginBottom: '32px'}}/>
                <Snackbar
                    anchorOrigin={{vertical: 'top', horizontal: 'right'}}
                    open={snackBarOpen}
                    autoHideDuration={3000}
                    message="Firm Profile URL Copied"
                    onClose={snackBarClose}
                />

                <Container><FirmDetailsParent isDrawer={true}
                                              loading={loading}
                                              rowId={rowId}
                                              pathname={currentPath}
                                              primaryAddress={primaryAddress}
                                              doctoredAddress={doctoredAddress}
                                              mailingAddress={mailingAddress}
                                              dunsDetails={dunsDetails}
                                              aliases={aliases}
                                              resultData={resultData}
                                              registrationDetails={registrationDetails}/></Container></Drawer></>
        )

    }

}


export default FirmDetailsPage;
FirmDetailsPage.getLayout = (page: React.ReactElement) => page;
