import React, {useState} from 'react';
import {<PERSON><PERSON>, Button, CircularProgress, TextField} from '@mui/material';
import Grid from '@mui/material/Grid2'
import InfoCard from "@/components/common/InfoCard";
import PaginatedTable from "@/components/common/PaginatedTable";
import Box from "@mui/material/Box";
import {useFormik} from "formik";
import {address_book_validation_schema} from "@/constants/validations";
import appService from "@/services/app.service";
import RecentActivityTracker from "@/constants/recentActivityTracker";
import CountryAutoComplete from "@/components/common/CountryAutoComplete";
import LocationOnIcon from '@mui/icons-material/LocationOn';


const AddressBook = () => {
    RecentActivityTracker('FDAddress Book');

    const formik = useFormik({
        initialValues: {
            primaryName: '',
            address: '',
            city: '',
            state: '',
            dunsNumber: '',
            feiNumber: '',
            countryCode: '',
            firmId: ''
        },
        validationSchema: address_book_validation_schema,
        onSubmit: (values) => {
            fetchData(values)
        },
    });

    const [searchData, setSearchData] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [initialLoad, setInitialLoad] = useState(true);


    const breadcrumbs = [
        {label: 'Data Concierge', href: '/'},
        {label: 'Data Products & Services', href: '/data-product'},
        {label: 'FDAddress Book', href: ''},
    ]

    function resetValues() {
        setLoading(false);
        setError('');
        setSearchData([]);
        setInitialLoad(true);
    }


    // Function to handle API call
    const fetchData = async (searchTerms: any) => {
        resetValues();
        setLoading(true);
        try {
            await appService.getAddressBookSearchResults(searchTerms).then(data => {
                setSearchData(data.data);
            })


        } catch (err) {
            setError(String(err));
            console.error("Error fetching data:", err);
        } finally {
            setLoading(false);
            setInitialLoad(false);
        }
    };

    const addressBookHeaders = [
        {label: 'Firm Name', key: 'stndrdz_prmry_name', flex: 1.25},
        {label: 'DNB Business Name', key: 'dnb_business_name', flex: 1.25},
        {label: 'Street Address', key: 'adr_line_1', flex: 2},
        {label: 'City', key: 'src_city', flex: 1},
        {label: 'State / Province', key: 'st_prvnc_name', flex: 1},
        {label: 'Country', key: 'iso2_cntry_cd', flex: 0.8},
        {label: 'Zip / Postal Code', key: 'pstl_cd', flex: 1},
        {label: 'Business Activity', key: 'bsns_actvty_cd', flex: 1.5},
        {label: 'Duns Number', key: 'duns_num', flex: 1},
        {label: 'FEI Number', key: 'fei_num', flex: 0.8},
        {label: 'System', key: 'last_rowid_system', flex: 0.8},
        {label: 'eMDM ID', key: 'enty_id', flex: 1.5}
    ]

    return (
        <div>
            <InfoCard breadcrumbs={breadcrumbs} header="FDAddress Book"
                      description="The Enterprise Master Data Management (eMDM) system pulls in data about firms across various Center/Office systems. Our Enterprise Search functionality maintains and returns the most accurate information about these firms. This is the most reliable information available about each firm."
                      HeaderFunctions={GeoWebLink()}
                      icon={`${process.env.NEXT_PUBLIC_BASE_PATH}/img/FDA-address-book.svg`}/>
            {/* Input Fields */}
            <form onSubmit={formik.handleSubmit}>
                <Grid container spacing={1}>
                    <Grid size={{md: 1.5, xs: 4}}>
                        <TextField fullWidth
                                   id="primaryName"
                                   name="primaryName"
                                   label="Firm Name"
                                   value={formik.values.primaryName}
                                   onChange={formik.handleChange}
                                   onBlur={formik.handleBlur}
                                   error={formik.touched.primaryName && Boolean(formik.errors.primaryName)}
                                   helperText={formik.touched.primaryName && formik.errors.primaryName}
                                   variant="outlined"
                                   size="small"
                        />
                    </Grid>
                    <Grid size={{md: 1.5, xs: 4}}>
                        <TextField fullWidth
                                   id="address"
                                   name="address"
                                   label="Street Address"
                                   value={formik.values.address}
                                   onChange={formik.handleChange}
                                   onBlur={formik.handleBlur}
                                   error={formik.touched.address && Boolean(formik.errors.address)}
                                   helperText={formik.touched.address && formik.errors.address}
                                   variant="outlined"
                                   size="small"

                        />
                    </Grid>
                    <Grid size={{xs: 4, md: 1.5}}>
                        <TextField fullWidth
                                   id="city"
                                   name="city"
                                   label="City"
                                   value={formik.values.city}
                                   onChange={formik.handleChange}
                                   onBlur={formik.handleBlur}
                                   error={formik.touched.city && Boolean(formik.errors.city)}
                                   helperText={formik.touched.city && formik.errors.city}
                                   variant="outlined"
                                   size="small"

                        />
                    </Grid>
                    <Grid size={{md: 1.5, xs: 4}}>
                        <TextField fullWidth
                                   id="state"
                                   name="state"
                                   label="State/Province"
                                   value={formik.values.state}
                                   onChange={formik.handleChange}
                                   onBlur={formik.handleBlur}
                                   error={formik.touched.state && Boolean(formik.errors.state)}
                                   helperText={formik.touched.state && formik.errors.state}
                                   variant="outlined"
                                   size="small"

                        />
                    </Grid>
                    <Grid size={{md: 2, xs: 4}}>
                        <CountryAutoComplete formik={formik}/>
                    </Grid>
                    <Grid size={{md: 1.5, xs: 4}}>
                        <TextField fullWidth
                                   id="dunsNumber"
                                   name="dunsNumber"
                                   label="DUNS Number"
                                   value={formik.values.dunsNumber}
                                   onChange={formik.handleChange}
                                   onBlur={formik.handleBlur}
                                   error={formik.touched.dunsNumber && Boolean(formik.errors.dunsNumber)}
                                   helperText={formik.touched.dunsNumber && formik.errors.dunsNumber}
                                   variant="outlined"
                                   size="small"

                        />
                    </Grid>
                    <Grid size={{md: 1.5, xs: 4}}>
                        <TextField fullWidth
                                   id="feiNumber"
                                   name="feiNumber"
                                   label="FEI Number"
                                   value={formik.values.feiNumber}
                                   onChange={formik.handleChange}
                                   onBlur={formik.handleBlur}
                                   error={formik.touched.feiNumber && Boolean(formik.errors.feiNumber)}
                                   helperText={formik.touched.feiNumber && formik.errors.feiNumber}
                                   variant="outlined"
                                   size="small"

                        />
                    </Grid>
                    <Grid size={{md: 1.5, xs: 4}}>
                        <TextField fullWidth
                                   id="firmId"
                                   name="firmId"
                                   label="eMDM Id"
                                   value={formik.values.firmId}
                                   onChange={formik.handleChange}
                                   onBlur={formik.handleBlur}
                                   error={formik.touched.firmId && Boolean(formik.errors.firmId)}
                                   helperText={formik.touched.firmId && formik.errors.firmId}
                                   variant="outlined"
                                   size="small"

                        />
                    </Grid>
                    <Grid size={{md: 6}}>
                        <Button type="submit" size="medium" sx={{mr: 2}} variant="contained">Search</Button>
                        <Button size="medium" sx={{mr: 2}} variant="contained"
                                onClick={() => {
                                    formik.resetForm();
                                    resetValues();
                                    setLoading(false);
                                }}>Clear</Button>

                    </Grid>
                </Grid>
            </form>


            {/* Table to display results */}
            {loading && <Box sx={{width: '100%', mt: 1}}>
                <CircularProgress/>
            </Box>

            }

            {!initialLoad && !loading && error === '' && searchData.length === 0 &&
                <Alert sx={{margin: '10px 0 0 0', width: 'fit-content'}} severity="error">No results
                    found</Alert>}


            {searchData.length != 0 && <Box sx={{width: '100%', typography: 'body1', mt: 2, height: 500}}>
                <Alert sx={{paddingTop: 0, paddingBottom: 0, marginBottom: 1}} severity="info">Search results include
                    exact and close matches to the search terms. Up to 100 of the most relevant results are shown first,
                    with close matches prioritized within that list.</Alert>

                <PaginatedTable system='addressbook' headers={addressBookHeaders}
                                data={searchData}/>
            </Box>
            }

        </div>
    );
}

const GeoWebLink = () => {
    return (
        <Button sx={{
            position: 'absolute',
            right: 10,
            alignItems: 'center'
        }} target="_blank" rel="noopener noreferrer" size={"small"}
                href="https://geoweb.fda.gov/portal/apps/instant/media/index.html?appid=933d4d92be9541e9b6c07b38f8a63760">
            <LocationOnIcon/>
            Map View of All Firms</Button>
    )
}

export default AddressBook;

AddressBook.getLayout = (page: React.ReactElement) => page;
