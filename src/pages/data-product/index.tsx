import {Box} from "@mui/material";
import CatalogCard from "@/components/common/CatalogCard";
import InfoCard from "@/components/common/InfoCard";
import React from "react";
import RecentActivityTracker from "@/constants/recentActivityTracker";
import {catalogStyles} from "@/styles/catalogStyles";
import {CATALOG_TILES} from "@/constants/catalogs";

const breadcrumbs = [
    {label: 'Data Concierge', href: '/'},
    {label: 'Data Products & Services', href: ''}
]


export default function DataProduct() {
    RecentActivityTracker('Data Products & Services');
    const classes = catalogStyles();

    const dataProductCatalog = CATALOG_TILES.products_services.items;

    return (
        <div>
            <InfoCard breadcrumbs={breadcrumbs} header="Data Products & Services Catalog"
                      description="Welcome to the Data Products & Services Page. Navigate to the data Products & Services components by clicking on the cards below."
                      icon={`${process.env.NEXT_PUBLIC_BASE_PATH}/img/data-product-dark.svg`}/>

            <Box className={classes.cardContentBox}>
                {dataProductCatalog.map((item, index) => (
                    <CatalogCard key={index} name={item.name} path={item.path} iconPath={item.image}
                                 description={item.description}/>
                ))}
            </Box>
        </div>
    )
}


DataProduct.getLayout = (page: React.ReactElement) => page;
