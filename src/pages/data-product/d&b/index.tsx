import InfoCard from "@/components/common/InfoCard";
import React from "react";
import Box from "@mui/material/Box";
import TabList from "@mui/lab/TabList";
import {Tab} from "@mui/material";
import TabPanel from "@mui/lab/TabPanel";
import TabContext from "@mui/lab/TabContext";
import DunsNameAddressLookup from "@/components/d&b/DunsNameAddressLookup";
import DunsLookup from "@/components/d&b/DunsLookup";
import DunsValidation from "@/components/d&b/DunsValidation";
import RecentActivityTracker from "@/constants/recentActivityTracker";


const DnB = () => {
    RecentActivityTracker('D&B');
    const breadcrumbs = [
        {label: 'Data Concierge', href: '/'},
        {label: 'Data Products & Services', href: '/data-product'},
        {label: 'D&B', href: ''},
    ]

    const [tabValue, setTabValue] = React.useState('1');

    const handleTabChange = (event: React.SyntheticEvent, newValue: string) => {
        setTabValue(newValue);
    };


    return (
        <div>
            <InfoCard breadcrumbs={breadcrumbs} header="D&B"
                      description="The Duns and Bradstreet (D&B) service provides firm lookup and validation.
                  Use the Name & Address tab to lookup firm names and addresses, the DUNS Lookup tab to lookup DUNS numbers,
                  and the Validate DUNS tab to validate DUNS Numbers for firms."
                      icon={`${process.env.NEXT_PUBLIC_BASE_PATH}/img/D&B.svg`}/>

            <TabContext value={tabValue}>
                <Box sx={{borderBottom: 1, borderColor: 'divider'}}>
                    <TabList onChange={handleTabChange} aria-label="D&B Tabs">
                        <Tab label="Name & Address Lookup" value="1"/>
                        <Tab label="DUNS Lookup" value="2"/>
                        <Tab label="Validate DUNS" value="3"/>
                    </TabList>
                </Box>
                <TabPanel value="1" tabIndex={1}>
                    <DunsNameAddressLookup/>
                </TabPanel>
                <TabPanel value="2" tabIndex={2}>
                    <DunsLookup/>
                </TabPanel>
                <TabPanel value="3" tabIndex={3}>
                    <DunsValidation/>
                </TabPanel>
            </TabContext>
        </div>
    )
}
export default DnB;

DnB.getLayout = (page: React.ReactElement) => page;
