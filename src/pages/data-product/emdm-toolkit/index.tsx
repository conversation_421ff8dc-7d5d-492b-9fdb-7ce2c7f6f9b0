import {Box, Button, Link} from "@mui/material";
import CatalogCard from "@/components/common/CatalogCard";
import InfoCard from "@/components/common/InfoCard";
import React from "react";
import RecentActivityTracker from "@/constants/recentActivityTracker";
import {catalogStyles} from "@/styles/catalogStyles";
import {CATALOG_TILES} from "@/constants/catalogs";
import ArticleIcon from '@mui/icons-material/Article';

const breadcrumbs = [
    {label: 'Data Concierge', href: '/'},
    {label: 'Data Products & Services', href: '/data-product'},
    {label: 'eMDM Toolkit', href: ''},
]


export default function DataProduct() {
    RecentActivityTracker('eMDM Toolkit');
    const classes = catalogStyles();

    const dataProductCatalog = CATALOG_TILES.emdm_toolkit.items;

    return (
        <div>
            <InfoCard breadcrumbs={breadcrumbs} header="eMDM ToolKit"
                      description="Explore the eMDM RESTFul API calls and its illustration referring the common use-cases. Access the API Documentation by clicking on the link above."
                      icon={`${process.env.NEXT_PUBLIC_BASE_PATH}/img/emdm_toolkit.svg`}
                      HeaderFunctions={APIDocumentationLink()}/>

            <Box className={classes.cardContentBox}>
                {dataProductCatalog.map((item, index) => (
                    <CatalogCard key={index} name={item.name} path={item.path} iconPath={item.image}
                                 description={item.description}/>
                ))}
            </Box>
        </div>
    )
}


const APIDocumentationLink = () => {
    return (
        <Button sx={{
            position: 'absolute',
            right: 10,
            alignItems: 'center'
        }} component={Link} size={"small"}
                href={process.env.NEXT_PUBLIC_BASE_PATH + "/docs/eMDM MuleSoft API handbook.pdf"} target="_blank"
                rel="noopener noreferrer">
            <ArticleIcon/>
            View the API Documentation</Button>
    )
}


DataProduct.getLayout = (page: React.ReactElement) => page;
