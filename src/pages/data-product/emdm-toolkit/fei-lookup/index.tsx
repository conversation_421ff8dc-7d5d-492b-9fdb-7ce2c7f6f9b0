import React from 'react';
import InfoCard from "@/components/common/InfoCard";
import RecentActivityTracker from "@/constants/recentActivityTracker";
import FEILookup from "@/components/toolkit/fei/FEILookup";


const FEILookupPage = () => {
    RecentActivityTracker('FEI Lookup Service');


    const breadcrumbs = [
        {label: 'Data Concierge', href: '/'},
        {label: 'Data Products & Services', href: '/data-product'},
        {label: 'eMDM Toolkit', href: '/data-product/emdm-toolkit'},
        {label: 'FEI Lookup Service', href: ''},
    ]

    return (
        <div>
            <InfoCard breadcrumbs={breadcrumbs} header="FEI Lookup Service"
                      description="Fetch FEI (Facility Establishment Identifier) details for a FEI based on FEI number as a input parameter"
                      icon={`${process.env.NEXT_PUBLIC_BASE_PATH}/img/center-look-up.svg`}/>
            <FEILookup/>
        </div>
    );
}

export default FEILookupPage;

FEILookupPage.getLayout = (page: React.ReactElement) => page;
