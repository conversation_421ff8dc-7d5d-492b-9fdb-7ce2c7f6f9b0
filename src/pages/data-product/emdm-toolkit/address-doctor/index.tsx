import React from 'react';
import InfoCard from "@/components/common/InfoCard";
import RecentActivityTracker from "@/constants/recentActivityTracker";
import FEILookup from "@/components/toolkit/fei/FEILookup";
import AddressDoctor from "@/components/toolkit/address-doctor/AddressDoctor";
import {Button} from "@mui/material";
import LocationOnIcon from "@mui/icons-material/LocationOn";


const FEILookupPage = () => {
    RecentActivityTracker('Address Lookup Service');


    const breadcrumbs = [
        {label: 'Data Concierge', href: '/'},
        {label: 'Data Products & Services', href: '/data-product'},
        {label: 'eMDM Toolkit', href: '/data-product/emdm-toolkit'},
        {label: 'Address Lookup Service', href: ''},
    ]

    return (
        <div>
            <InfoCard breadcrumbs={breadcrumbs} header="Address Lookup By Address Doctor Service"
                      description="Address Validation service used to validate the company/firm address based on specific address related input parameters"
                      icon={`${process.env.NEXT_PUBLIC_BASE_PATH}/img/third-party.svg`}/>
            <AddressDoctor/>
        </div>
    );

}

export default FEILookupPage;

FEILookupPage.getLayout = (page: React.ReactElement) => page;
