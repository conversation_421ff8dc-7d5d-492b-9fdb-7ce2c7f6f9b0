import React from 'react';
import InfoCard from "@/components/common/InfoCard";
import RecentActivityTracker from "@/constants/recentActivityTracker";
import GeoCode from '@/components/toolkit/geocode/GeoCode';


const GeoCodePage = () => {
    RecentActivityTracker('GeoCode Service');


    const breadcrumbs = [
        {label: 'Data Concierge', href: '/'},
        {label: 'Data Products & Services', href: '/data-product'},
        {label: 'eMDM Toolkit', href: '/data-product/emdm-toolkit'},
        {label: 'GeoCode Service', href: ''},
    ]

    return (
        <div>
            <InfoCard breadcrumbs={breadcrumbs} header="GeoWeb Service"
                      description="Geocode API service provides endpoints to obtain precise geocode details for a company/firm address based on specific address related input parameters"
                      icon={`${process.env.NEXT_PUBLIC_BASE_PATH}/img/geo_code.svg`}/>
            <GeoCode/>
        </div>
    );
}

export default GeoCodePage;

GeoCodePage.getLayout = (page: React.ReactElement) => page;
