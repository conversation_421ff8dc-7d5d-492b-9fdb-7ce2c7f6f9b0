import {ThemeProvider} from '@mui/material';
import theme from '../theme/theme';
import {ReactElement, ReactNode} from 'react';
import {NextPage} from 'next';
import '../app/globals.css';
import Layout from "@/layout/Layout";
import SecurityWarning from "@/components/common/SecurityWarning";
import {useAuth} from "@/hooks/userAuth";
import {InactivityTimeoutHandler} from "@/components/common/InactivityTimeoutHandler";
import {usePathname} from 'next/navigation';
import Head from 'next/head'

type NextPageWithLayout = NextPage & {
    getLayout?: (page: ReactElement) => ReactNode;
};

type AppProps = {
    Component: NextPageWithLayout;
    pageProps: any;
};

function GlobalAuthInitializer() {
    // This hook call triggers the SWR fetch and updates Zustand globally
    useAuth();
    return null;
}

function App({Component, pageProps}: AppProps) {
    const pathname = usePathname() || '';

    const getLayout = Component.getLayout || ((page) => page);
    return (

        <><Head>
            <title>Data Concierge</title>
        </Head><GlobalAuthInitializer/><InactivityTimeoutHandler/><SecurityWarning/><ThemeProvider theme={theme}>

            {pathname.includes('logged-out') ? <Component {...pageProps} /> : getLayout(
                <Layout><Component {...pageProps} /></Layout>)}

        </ThemeProvider></>

    )
}

export default App;
