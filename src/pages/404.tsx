// ** Next Import
import Link from 'next/link'

// ** MUI Components
import Button from '@mui/material/Button'
import { styled } from '@mui/material/styles'
import Typography from '@mui/material/Typography'
import Box, { BoxProps } from '@mui/material/Box'


// ** Styled Components
const BoxWrapper = styled(Box)<BoxProps>(({ theme }) => ({
    [theme.breakpoints.down('md')]: {
        width: '90vw'
    }
}))

const Error404 = () => {
    return (
        <Box className='content-center'>
            <Box sx={{ p: 5, display: 'flex', flexDirection: 'column', alignItems: 'center', textAlign: 'center' }}>
                <BoxWrapper>
                    <Typography variant='h1'>404</Typography>
                    <Typography variant='h5' sx={{ mb: 1, fontSize: '1.5rem !important' }}>
                        Page Not Found ⚠️
                    </Typography>
                    <Typography variant='body2'>We couldn&prime;t find the page you are looking for.</Typography>
                </BoxWrapper>
                <Button href='/' component={Link} variant='contained' sx={{ px: 5.5, margin: 10 }}>
                    Back to Home
                </Button>
            </Box>
        </Box>
    )
}


export default Error404;
