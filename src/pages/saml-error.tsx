import { useRouter } from 'next/router';
import Link from 'next/link';
import React from 'react';

export default function SamlErrorPage() {
    const router = useRouter();
    const { message, details } = router.query;

    return (
        <div style={{ padding: '2rem', textAlign: 'center', fontFamily: 'sans-serif' }}>
            <h1>Authentication Error</h1>
            <p style={{ color: 'red', fontWeight: 'bold' }}>
                An error occurred during the SAML sign-in process.
            </p>
            {message && (
                <p><strong>Message:</strong> {decodeURIComponent(message as string)}</p>
            )}
            {details && details !== '{}' && ( // Don't show empty details object string
                <div style={{ marginTop: '1rem', textAlign: 'left', background: '#f8f8f8', padding: '1rem', borderRadius: '5px', overflowX: 'auto' }}>
                    <strong>Details:</strong>
                    <pre><code>{decodeURIComponent(details as string)}</code></pre>
                </div>
            )}
            <p style={{ marginTop: '2rem' }}>
                Please try again or contact your administrator.
            </p>
            <Link href="/" passHref>
                <a style={{ display: 'inline-block', marginTop: '1rem', textDecoration: 'underline' }}>Go to Homepage (will retry login)</a>
            </Link>
        </div>
    );
}