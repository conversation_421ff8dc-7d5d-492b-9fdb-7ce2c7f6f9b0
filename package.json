{"name": "emdm", "version": "2.0", "private": true, "homepage": "/", "scripts": {"dev": "next dev", "build:dev": "cp .env.dev .env.production && next build && cp next-logger.config.js .next/standalone/ && cp -r config .next/standalone/ && cp -r public .next/standalone/ && cp -r .next/static .next/standalone/.next/", "build:preprod": "cp .env.preprod .env.production && next build && cp next-logger.config.js .next/standalone/ && cp -r config .next/standalone/ && cp -r public .next/standalone/ && cp -r .next/static .next/standalone/.next/", "build:prod": "cp .env.prod .env.production && next build && cp next-logger.config.js .next/standalone/ && cp -r config .next/standalone/ && cp -r public .next/standalone/ && cp -r .next/static .next/standalone/.next/", "build": "next build && cp next-logger.config.js .next/standalone/ && cp -r config .next/standalone/ && cp -r public .next/standalone/ && cp -r .next/static .next/standalone/.next/", "start": "next start", "export": "next export", "lint": "eslint --fix \"src/**/*.{js,jsx,ts,tsx}\"", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx}\""}, "dependencies": {"@emotion/react": "11.14.0", "@emotion/styled": "11.14.0", "@mui/icons-material": "^6.4.1", "@mui/lab": "6.0.0-beta.24", "@mui/material": "6.4.1", "@mui/styles": "^6.4.1", "@mui/system": "^6.4.1", "@mui/x-data-grid": "^7.24.1", "formik": "^2.4.6", "fuse.js": "^7.0.0", "next": "^15.3.1", "react": "^19.0.0", "react-dom": "^19.0.0", "yup": "^1.6.1", "zustand": "^5.0.3", "swagger-ui-react": "^5.18.3", "moment": "^2.30.1", "he": "^1.2.0", "passport": "^0.7.0", "@node-saml/passport-saml": "^5.0.1", "iron-session": "^8.0.4", "swr": "^2.3.3", "xml2js": "^0.6.2", "pino": "^9.6.0", "next-logger": "^5.0.1", "@vis.gl/react-google-maps": "^1.5.2"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.19.0", "@types/node": "^22.12.0", "@types/react": "^19.0.8", "@types/react-dom": "^19.1.2", "@types/swagger-ui-react": "^5.18.0", "@types/he": "^1.2.3", "@types/passport": "^1.0.17", "@types/xml2js": "^0.4.14", "eslint": "^9.19.0", "eslint-config-next": "^15.1.6", "eslint-plugin-unused-imports": "^4.1.4", "prettier": "^3.4.2", "typescript": "^5.7.3", "pino-pretty": "^13.0.0"}}