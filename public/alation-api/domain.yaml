info:
  description: PublicAPI for creating, assigning objects to domain in bulk
  title: Domain API
  version: 1.0.0
openapi: 3.0.0
paths:
  /domain/:
    delete:
      description: This API is used to delete domains in bulk. When a domain is requested
        to be deleted, all its subdomains also will be deleted.
      operationId: deleteDomains
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Bulk_Delete_Request_Payload'
        required: true
      responses:
        202:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JobID'
          description: The JobID of the underlying bulk operation running asynchronously.
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Deletes the requested domain(s) and their subdomains. Additionally,
        it un-assigns all the objects from these domains and subdomains.
      tags:
      - Domain
    get:
      description: This API is used to retrieve domains from the server, along with
        details including title, description, and parent id, if a parent exists. Add
        the parent_id parameter to only retrieve subdomains of a specific domain.
      operationId: getDomains
      parameters:
      - $ref: '#/components/parameters/ParentId'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Get_Domains_Response_Body'
          description: Requested domains
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Retrieve a list of domains.
      tags:
      - Domain
    post:
      description: This API is used to create domains in bulk.  Domains must include
        a title.  If the domain has a parent domain, it MUST be included - this cannot
        be changed after creation.
      operationId: postDomains
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Create_Domains_Request_Payload'
        required: true
      responses:
        201:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Create_Domains_Response_Body'
          description: Details for the created domains.
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Create domains in bulk.
      tags:
      - Domain
  /domain/membership/:
    post:
      description: This API lets you add multiple alation catalog objects to a domain,
        at once. If the count of requested objects is lesser than 1000, then it runs
        synchronously, otherwise asynchronously. The limit is configurable under property
        alation.domains.bulk_membership.sync_job_max_batch_size.
      operationId: postDomainMembership
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Bulk_Assign_Request_Payload'
        required: true
      responses:
        201:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Bulk_Assign_Response_Body'
          description: Confirmation that objects have been added to the domain.
        202:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JobID'
          description: The JobID of the underlying bulk operation running asynchronously.
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Adds the requested objects to a domain.
      tags:
      - Domain
  /domain/membership/view_rules/:
    post:
      description: This API lets you browse the domain membership rules.
      operationId: postViewDomainRules
      parameters:
      - $ref: '#/components/parameters/limit'
      - $ref: '#/components/parameters/skip'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/View_Rules_Request_Payload'
        required: true
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/View_Rules_Response_Payload'
          description: Returns the applied domain rules matching the filter conditions
            provided in request.
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: It allows user to browse the domain rules, with some filters.
      tags:
      - Domain
security:
- ApiKeyAuth: []
servers:
- url: '{protocol}://{base-url}/integration/v2'
  variables:
    base-url:
      default: localhost
      description: Django BASE_URL setting for this instance.
    protocol:
      default: http
      enum:
      - http
      - https
tags:
- description: A domain provides a way to group objects.
  name: Domain
- description: Api supporting a bulk operation.
  name: Bulk


components:
  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: TOKEN
      description: API Key for the user

  schemas:
    Domain:
      description: Properties of a Domain object
      type: object
      properties:
        id:
          type: integer
          readOnly: true
          description: The id of the Domain object
        title:
          type: string
          description: The title of the Domain
        description:
          type: string
          description: The description of the Domain
        parent_id:
          type: integer
          description: The id of the parent of the Domain
          nullable: true
      required:
        - title
        - description

    Get_Domains_Response_Body:
      type: object
      properties:
        id:
          type: integer
          description: Domain object id
          example: 1
        title:
          type: string
          description: Title of the Domain object
          example: Sales
        description:
          type: string
          description: Description of the Domain object
          example: Relevant data and articles for Sales Analytics
        parent_id:
          type: integer
          description: The id of the domain's parent, if it has one.
          example: 2

    Create_Domains_Request_Payload:
      type: array
      items:
        $ref: "#/components/schemas/Domain"

    Create_Domains_Response_Body:
      type: array
      items:
        $ref: "#/components/schemas/Domain"

    Bulk_Assign_Request_Payload:
      type: object
      properties:
        id:
          type: integer
          description: Domain id, to which objects will be added.
          example: 1
        exclude:
          type: boolean
          description: Should it unassign objects from the domain? (By default, it's false. i.e. it will assign the objects to the domain.)
        oid:
          type: array
          items:
            type: integer
          description: >
            List of object IDs to be assigned to the domain.
          example: [1,2,3,7,8]
        otype:
          type: string
          description: otype of the Catalog Objects
          example: Article
      required:
        - id
        - oid
        - otype

    View_Rules_Request_Payload:
      type: object
      properties:
        domain_id:
          type: array
          items:
            type: integer
          description: Get the rules applied on the requested domain ids.
          example: [1,2]
        exclude:
          type: boolean
          description: Get rules that Exclude objects from domain vs Include those into domain.
        recursive:
          type: boolean
          description: Get rules which are applied recursively on the objects (i.e. on object's descendents as well) vs  non-recursively. (i.e. only the object)
      required:
        - domain_id
        - exclude

    View_Rules_Response_Payload:
      type: object
      properties:
        domain_id:
          type: integer
          description: Denotes domain id, on which the rule is applied.
        exclude:
          type: boolean
          description: Denotes whether rule will Exclude objects from domain or Include.
        recursive:
          type: boolean
          description: Denotes whether rule is applied recursively on the object (i.e. on the object's descendents as well) or  non-recursively. (i.e. only on the object)
        otype:
          type: string
          description: Denotes the type of the catalog object on which the rule has been applied.
        oid:
          type: integer
          description: Denotes the id of the catalog object on which the rule has been applied.

    Bulk_Assign_Response_Body:
      type: object
      properties:
        id:
          type: integer
          description: Domain id, to which objects will be added.
          example: 1
        exclude:
          type: boolean
          description: Should it unassign objects from the domain? (By default, it's false. i.e. it will assign the objects to the domain.)
        oid:
          type: array
          items:
            type: integer
          description: >
            List of object IDs to be assigned to the domain.
          example: [1,2,3,7,8]
        otype:
          type: string
          description: otype of the Catalog Objects
          example: Article

    Bulk_Delete_Request_Payload:
      type: object
      properties:
        id:
          type: array
          items:
            type: integer
          description: List of domain ids to be deleted. (all their subdomains also will be deleted.)
          example: [ 22222, 33333 ]
      required:
        - id

    JobID:
      description: Properties of a Job Object
      type: object
      properties:
        job_id:
          type: integer
          description: GET /api/v1/bulk_metadata/job/?id={job_id} will return the status of the underlying POST job

    Error:
      type: object
      description: Properties of a Error Object
      properties:
        status:
          type: string
          description: The HTTP status code
        title:
          type: string
          description: The title of the error message
        details:
          type: string
          description: More information about the error
      required:
        - status
        - title
        - details

  parameters:
    ParentId:
      name: parent_id
      description: The id of the parent of the domain
      in: query
      required: false
      schema:
        type: integer
      examples:
        id:
          value: 123
          summary: A sample integer domain parent id
    limit:
      description: >
        Specifies the number of rules to be fetched in one paginated request. Response may
        have a `X-Next-Page` URL parameter in the header to fetch next set of rules or page
      in: query
      name: limit
      required: false
      schema:
        type: integer
        default: 100
    skip:
      description: >
        Specifies the number of rules to be skipped in one request. Used together with
        `Limit` parameter to fetch paginated requests. This is automatically set in `X-Next-Page`
        response header if present
      in: query
      name: skip
      required: false
      schema:
        type: integer
        default: 0

  responses:
    Standard_400_Error_Response:
      description: Malformed Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            status: 400
            title: "Malformed Request"
            detail: "The request sent by user is in the wrong format."

    Standard_401_Error_Response:
      description: Unauthorized bad/missing token
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            status: 401
            title: "Unauthorized"
            detail: "You are unauthorized to access this resource. Please obtain valid credentials
            and try again."
    Standard_403_Error_Response:
      description: Forbidden User cannot edit this resource
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            status: 403
            title: "Forbidden Action"
            detail: "This is forbidden to access. Make sure you access the right resource."
    Standard_404_Error_Response:
      description: The specified resource was not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            status: 404
            title: "Resource not found"
            detail: "This is not a valid resource. Please try something else."
    Standard_500_Error_Response:
      description: Internal Server Error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            status: 500
            title: "Internal Server Error"
            detail: "Something went wrong, Please try again later."
