info:
  description: PublicAPI for Retrieving Search Results
  title: Search API
  version: 1.0.0
openapi: 3.0.0
paths:
  /search/:
    get:
      description: API to fetch search results for given query params. This should
        match results retrieved on the Full Search page when searching using the Search
        UI.
      operationId: getSearchResults
      parameters:
      - $ref: '#/components/parameters/Query'
      - $ref: '#/components/parameters/Limit'
      - $ref: '#/components/parameters/Offset'
      - $ref: '#/components/parameters/Filters'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SearchResults'
          description: Requested Search Results
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: GET a response from the search API endpoint
      tags:
      - Search
security:
- ApiKeyAuth: []
servers:
- url: '{protocol}://{base-url}/integration/v1/search/'
  variables:
    base-url:
      default: <base-url>
      description: Alation BASE_URL setting. This is the host name for your Alation
        instance.
    protocol:
      default: http
      enum:
      - http
      - https
tags:
- description: Search API for all objects, returns search results
  name: Search


components:
  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: TOKEN
      description: API Key for the user

  schemas:
    SearchResults:
      description: Search Result object from Search API
      type: object
      properties:
        total:
          type: integer
          description: Total number of matching results found including ones that are beyond the ones returned
        limit:
          type: integer
          default: 20
          minimum: 0
          maximum: 10000
          description: The maximum number of results to be returned
        offset:
          type: integer
          default: 0
          minimum: 0
          description: Index of first search result to return from full result set
        full_search_url:
          type: string
          description: The url for the corresponding Full Search page
        results:
          type: array
          description: The list of results returned for the current Search query
          items:
            type: object
            properties:
              breadcrumbs:
                type: array
                description: >
                  array of objects that allows constructing breadcrumbs. If breadcrumbs are
                  not supported for this object type, returns None.
                nullable: true
                items:
                  type: object
                  description: breadcrumb object
                  properties:
                    id:
                      type: integer
                      description: id of the breadcrumb object
                    otype:
                      type: string
                      description: otype of the breadcrumb object
                    name:
                      type: string
                      description: name of the breadcrumb object
                    title:
                      type: string
                      description: title of the breadcrumb object
              flags:
                type: array
                description: >
                  Types of flags present on the result object.
                  If at least one flag of the type is present then the type is listed.
                items:
                  enum:
                    - Deprecation
                    - Endorsement
                    - Warning
                  type: string
                  description: Permitted flags are ['Deprecation', 'Endorsement', 'Warning']
              highlight:
                type: object
                description: >
                  Object that shows the exact location(s) of the search string
                  that matched the Search Query within the result object
                properties:
                  name:
                    type: object
                    description: matching text for 'name' field in result object
                    properties:
                      snippet:
                        type: string
                        description: string containing matching substring for search query
                      offsets:
                        type: array
                        description: start, end indices for matching substring within snippet for search query
                        items:
                          type: integer
                          description: start/end index within snippet string
                        minItems: 2
                        maxItems: 2
                  text:
                    type: object
                    description: matching text for 'text' field in result object
                    properties:
                      snippet:
                        type: string
                        description: string containing matching substring for search query
                      offsets:
                        type: array
                        description: start, end indices for matching substring within snippet for search query
                        items:
                          type: integer
                          description: start/end index within snippet string
                        minItems: 2
                        maxItems: 2
                  title:
                    type: object
                    description: matching text for 'title' field in result object
                    properties:
                      snippet:
                        type: string
                        description: string containing matching substring for search query
                      offsets:
                        type: array
                        description: start, end indices for matching substring within snippet for search query
                        items:
                          type: integer
                          description: start/end index within snippet string
                        minItems: 2
                        maxItems: 2
              id:
                type: integer
                description: >
                  The Numeric ID of the result object. otype + id can serve as a unique identifier for objects
              name:
                type: string
                description: The name of the result object
              otype:
                type: string
                description: >
                  The otype of the result object. otype + id can serve as a unique identifier for objects
              public_search_otype:
                type: string
                description: The otype of the result object that can be passed to Search API
              starred:
                type: boolean
                description: The "Starred" state of the result object as set by the requesting user
              text:
                type: string
                description: The text of the result object
              title:
                type: string
                description: The title of the result object
              url:
                type: string
                description: The url of the result object catalog page
              recent:
                type: boolean
                description: >
                  The "Recent" state of the result object, indicating whether the object was recently visited by the requesting user
              watching:
                type: boolean
                description: The "Watching" state of the result object as set by the requesting user
      required:
        - total
        - limit
        - offset
        - full_search_url
        - results

    Error:
      type: object
      description: Properties of an Error Object
      properties:
        code:
          type: string
          description: A six digit code that identifies the problem. Refer the error documentation for more information.
        title:
          type: string
          description: The title of the error message
        detail:
          type: string
          description: More information about the error
      required:
        - code
        - title
        - detail

  responses:
    Standard_400_Error_Response:
      description: Malformed Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "400XXX"
            title: "Malformed Request"
            detail: "The request sent by user is in the wrong format. (Refer the error documentation for specific details of the error)"

    Standard_401_Error_Response:
      description: Unauthorized bad/missing token
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "401XXX"
            title: "Unauthorized"
            detail: "You are unauthorized to access this resource. Please obtain valid credentials. (Refer the error documentation for specific details of the error)"
    Standard_403_Error_Response:
      description: Forbidden User cannot edit this resource
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "403XXX"
            title: "Forbidden Action"
            detail: "This is forbidden to access. Make sure you access the right resource. (Refer the error documentation for specific details of the error)"
    Standard_404_Error_Response:
      description: The specified resource was not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "404XXX"
            title: "Resource not found"
            detail: "This is not a valid resource. Please try something else. (Refer the error documentation for specific details of the error)"
    Standard_500_Error_Response:
      description: Internal Server Error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "500XXX"
            title: "Internal Server Error"
            detail: "Something went wrong, Please try again later. (Refer the error documentation for specific details of the error)"

  parameters:
    Query:
      name: q
      description: Query String to search for
      explode: false
      in: query
      required: false
      schema:
        type: string
      examples:
        q:
          value: 'SampleSearchQuery'
          summary: A sample valid query string

    Limit:
      name: limit
      description: Maximum number of results to return in request
      explode: false
      in: query
      required: false
      schema:
        type: integer
        default: 20
        minimum: 0
        maximum: 10000
      examples:
        limit:
          value: 50
          summary: A sample valid limit value

    Offset:
      name: offset
      description: >
        Index of first search result to return from full result set
      explode: false
      in: query
      required: false
      schema:
        type: integer
        default: 0
        minimum: 0
      examples:
        offset:
          value: 5
          summary: A sample valid offset value

    Filters:
      name: filters
      description: |
        Filters object used to restrict search results. These are the permitted properties:
          * starred:<br>
            type: boolean<br>
            description: Restrict results to objects marked "Starred" by the requesting user<br>
          * watching:<br>
            type: boolean<br>
            description: Restrict results to objects marked "Watching" by the requesting user<br>
          * recent:<br>
            type: boolean<br>
            description: Restrict results to objects marked "Recent", indicating whether the object was recently visited by the requesting user<br>
          * flags:<br>
            type: array<br>
            description: Restrict results to objects with specified flags present on the result object.
            * items:<br>
            type: string<br>
            description: Permitted flags are:
              * 'Deprecation'
              * 'Endorsement'
              * 'Warning'
          * otypes:<br>
            type: array<br>
            description: Restrict results to objects with specified otypes.
            * items:<br>
            type: string<br>
            description:
            Below are the permitted otypes as of 02/02/2021. Please consult Otypes API endpoint for current permitted otypes based on your Alation configuration:
              * api_resource
              * api_resource_field
              * api_resource_folder
              * article
              * bi_field
              * catalog_set
              * column
              * dataflow
              * datasource
              * doc_schema
              * docstore_collection
              * docstore_folder
              * domain
              * execution_result
              * file
              * filesystem
              * function
              * glossary
              * group
              * query_or_statement
              * report_collection
              * report_datasource
              * report_object
              * report_source
              * schema
              * table
              * tag
              * thread
              * user
              * value
          * domain_ids:<br>
            type: array<br>
            description: Restrict results to objects within the specified domain(s).
            * items:<br>
            type: integer<br>
      in: query
      required: false
      content:
        application/json:
          schema:
            type: object
            description: Filters object used to restrict search results
            default: {}
            properties:
              starred:
                type: boolean
                description: Restrict results to objects marked "Starred" by the requesting user
              watching:
                type: boolean
                description: Restrict results to objects marked "Watching" by the requesting user
              recent:
                type: boolean
                description: Restrict results to objects marked "Recent", indicating whether the object was recently visited by the requesting user
              flags:
                type: array
                description: Restrict results to objects with specified flags present on the result object.
                items:
                  enum:
                    - Deprecation
                    - Endorsement
                    - Warning
                  type: string
                  description: >
                    Permitted flags are:
                      * Deprecation
                      * Endorsement
                      * Warning
              otypes:
                type: array
                description: Restrict results to objects with specified otypes.
                items:
                  enum:
                    - api_resource
                    - api_resource_field
                    - api_resource_folder
                    - article
                    - bi_field
                    - catalog_set
                    - column
                    - dataflow
                    - datasource
                    - doc_schema
                    - docstore_collection
                    - docstore_folder
                    - domain
                    - execution_result
                    - file
                    - filesystem
                    - function
                    - glossary
                    - group
                    - query_or_statement
                    - report_collection
                    - report_datasource
                    - report_object
                    - report_source
                    - schema
                    - table
                    - tag
                    - thread
                    - user
                    - value
                  type: string
                  description: >
                    Below are the permitted otypes as of 02/02/2021. Please consult Otypes API endpoint for current permitted otypes based on your Alation configuration:
                      * api_resource
                      * api_resource_field
                      * api_resource_folder
                      * article
                      * bi_field
                      * catalog_set
                      * column
                      * dataflow
                      * datasource
                      * doc_schema
                      * docstore_collection
                      * docstore_folder
                      * domain
                      * execution_result
                      * file
                      * filesystem
                      * function
                      * glossary
                      * group
                      * query_or_statement
                      * report_collection
                      * report_datasource
                      * report_object
                      * report_source
                      * schema
                      * table
                      * tag
                      * thread
                      * user
                      * value
              domain_ids:
                type: array
                description: Restrict results to objects within the specified domain(s).
                items:
                  type: integer
          example:
            starred: true
            recent: false
            watching: true
            flags: ['Endorsement']
            otypes: ['schema', 'table']
            domain_ids: [5]

