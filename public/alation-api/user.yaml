info:
  description: Public API for getting user information and suspending duplicate user accounts
  title: User Public API
  version: 1.0.0
openapi: 3.0.0
paths:
  /user/:
    get:
      description: This API is used to list all Users.
      operationId: getUsers
      parameters:
        - $ref: '#/components/parameters/UserList_display_name'
        - $ref: '#/components/parameters/UserList_display_name__contains'
        - $ref: '#/components/parameters/UserList_display_name__icontains'
        - $ref: '#/components/parameters/UserList_email'
        - $ref: '#/components/parameters/UserList_email__contains'
        - $ref: '#/components/parameters/UserList_email__icontains'
        - $ref: '#/components/parameters/UserList_id'
        - $ref: ./common/parameters.yaml#/components/parameters/Limit
        - $ref: '#/components/parameters/UserList_profile_id'
        - $ref: ./common/parameters.yaml#/components/parameters/Offset
        - $ref: '#/components/parameters/UserList_order_by'
        - $ref: ./common/parameters.yaml#/components/parameters/Values
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserList'
          description: Requested Users
        400:
          $ref: ./common/responses.yaml#/components/responses/Standard_400_Error_Response
        401:
          $ref: ./common/responses.yaml#/components/responses/Standard_401_Error_Response
        403:
          $ref: ./common/responses.yaml#/components/responses/Standard_403_Error_Response
        404:
          $ref: ./common/responses.yaml#/components/responses/Standard_404_Error_Response
        500:
          $ref: ./common/responses.yaml#/components/responses/Standard_500_Error_Response
      summary: GET multiple Users
      tags:
        - User
  /user/{id}/:
    get:
      description: This API fetches an individual User.
      operationId: getUserById
      parameters:
        - $ref: '#/components/parameters/User_id'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: Confirmation that a User has been fetched.
        400:
          $ref: ./common/responses.yaml#/components/responses/Standard_400_Error_Response
        401:
          $ref: ./common/responses.yaml#/components/responses/Standard_401_Error_Response
        403:
          $ref: ./common/responses.yaml#/components/responses/Standard_403_Error_Response
        404:
          $ref: ./common/responses.yaml#/components/responses/Standard_404_Error_Response
        500:
          $ref: ./common/responses.yaml#/components/responses/Standard_500_Error_Response
      summary: GET a User
      tags:
        - User
  /userinfo/:
    get:
      description: This API is used to fetch the details of the authenticated user.
      operationId: getAuthenticatedUserDetails
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserInfo'
          description: Confirms that user details are fetched.
        400:
          $ref: ./common/responses.yaml#/components/responses/Standard_400_Error_Response
        401:
          $ref: ./common/responses.yaml#/components/responses/Standard_401_Error_Response
        403:
          $ref: ./common/responses.yaml#/components/responses/Standard_403_Error_Response
        404:
          $ref: ./common/responses.yaml#/components/responses/Standard_404_Error_Response
        500:
          $ref: ./common/responses.yaml#/components/responses/Standard_500_Error_Response
      summary: GET authenticated User details
      security:
        - AlationUserAPIKeyAuth: []
        - AlationBearerTokenAuth: []
      tags:
        - User
  /generate_dup_users_accts_csv_file/:
    get:
      description: |-
        This API is used to generate a CSV file containing a list of duplicate user accounts.
        Refer to [De-duplicate Mixed Case Usernames](https://docs2.alationdata.com/en/latest/installconfig/Update/DeduplicateMixedCaseUsernames.html "Docs") for details.
      operationId: getDuplicateUserAccountsInFile
      responses:
        200:
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - $ref: '#/components/schemas/FileResponse'
          description: |-
            Confirms that a CSV file containing duplicate user accounts was fetched, or returns a Success response indicating that no duplicate user accounts were found.
        400:
          $ref: ./common/responses.yaml#/components/responses/Standard_400_Error_Response
        401:
          $ref: ./common/responses.yaml#/components/responses/Standard_401_Error_Response
        403:
          $ref: ./common/responses.yaml#/components/responses/Standard_403_Error_Response
        404:
          $ref: ./common/responses.yaml#/components/responses/Standard_404_Error_Response
        500:
          $ref: ./common/responses.yaml#/components/responses/Standard_500_Error_Response
      summary: Get duplicate user accounts.
      security:
        - AlationAdminAPIKeyAuth: []
      tags:
        - Suspend Duplicate User Accounts
  /remove_dup_users_accts/:
    post:
      description: |-
        This API is used to suspend duplicate user accounts by parsing a CSV file. You must first generate a CSV file using the **generate_dup_users_accts_csv_file** endpoint.
        Refer to [De-duplicate Mixed Case Usernames - Edit the CSV File](https://docs2.alationdata.com/en/latest/installconfig/Update/DeduplicateMixedCaseUsernames.html#step-2-edit-the-csv-file "Docs") for details on how to edit the CSV file.
      operationId: suspendDuplicateUserAccountsFromFile
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                csv_file:
                  type: string
                  format: binary
              required:
                - csv_file
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
          description: |-
            Confirms that duplicate user accounts fetched as CSV file format or Success Response will be returned to indicate no duplicate users accounts found.
        400:
          $ref: ./common/responses.yaml#/components/responses/Standard_400_Error_Response
        401:
          $ref: ./common/responses.yaml#/components/responses/Standard_401_Error_Response
        403:
          $ref: ./common/responses.yaml#/components/responses/Standard_403_Error_Response
        404:
          $ref: ./common/responses.yaml#/components/responses/Standard_404_Error_Response
        500:
          $ref: ./common/responses.yaml#/components/responses/Standard_500_Error_Response
      summary: Suspend duplicate user accounts
      security:
        - AlationAdminAPIKeyAuth: []
      tags:
        - Suspend Duplicate User Accounts
security:
  - AlationAdminAPIKeyAuth: []
servers:
  - url: '{protocol}://{base-url}/integration/v1'
    variables:
      base-url:
        default: localhost
        description: Alation BASE_URL setting for this instance.
      protocol:
        default: http
        enum:
          - http
          - https
tags:
  - description: The User object. Provides access to reading Users.
    name: User
  - description: Provides the ability to generate a CSV containing a list of duplicate users and to remove duplicate users by uploading an edited CSV file.
    name: Suspend Duplicate User Accounts


components:
  schemas:
    display_name:
      description: The User's display name.
      type: string
    email:
      description: The User's email address.
      type: string
    id:
      description: The User's id.
      minimum: 1
      type: integer
    profile_id:
      description: The User's profile id.
      minimum: 1
      type: integer
    url:
      description: The url to the User's profile page.
      type: string
    username:
      description: The User's username.
      type: string
    role:
      description: The User's role.
      type: string
    first_name:
      description: The firstName of a User.
      type: string
    last_name:
      description: The lastName of a User.
      type: string
    title:
      description: The title of a User.
      type: string
    User:
      type: object
      properties:
        display_name:
          allOf:
            - $ref: "#/components/schemas/display_name"
            - readOnly: true
        email:
          allOf:
            - $ref: "#/components/schemas/email"
            - readOnly: true
        id:
          allOf:
            - $ref: "#/components/schemas/id"
            - readOnly: true
        profile_id:
          allOf:
            - $ref: "#/components/schemas/profile_id"
            - readOnly: true
        url:
          allOf:
            - $ref: "#/components/schemas/url"
            - readOnly: true
    UserInfo:
      type: object
      required:
        - email
        - id
        - username
        - role
      properties:
        email:
          allOf:
            - $ref: "#/components/schemas/email"
            - readOnly: true
        id:
          allOf:
            - $ref: "#/components/schemas/id"
            - readOnly: true
        username:
          allOf:
            - $ref: "#/components/schemas/username"
            - readOnly: true
        role:
          allOf:
            - $ref: "#/components/schemas/role"
            - readOnly: true
        first_name:
          allOf:
            - $ref: "#/components/schemas/first_name"
            - readOnly: true
        last_name:
          allOf:
            - $ref: "#/components/schemas/last_name"
            - readOnly: true
        title:
          allOf:
            - $ref: "#/components/schemas/title"
            - readOnly: true
    UserList:
      type: array
      items:
        $ref: "#/components/schemas/User"
    SuccessResponse:
      type: object
      description: Properties of a Success response object
      properties:
        Success:
          type: string
          description: Success
      example:
        Success: "No duplicate user accounts with mixed case usernames."
      required:
        - Success
    FileResponse:
      description: Generated File response object
      type: string
      format: binary

  parameters:
    User_id:
      description: The User ID to retrieve.
      example: 1
      in: path
      name: id
      required: true
      schema:
        $ref: "#/components/schemas/id"
    UserList_display_name:
      description: Filter by an exact match on display_name
      in: query
      name: display_name
      required: false
      schema:
        $ref: "#/components/schemas/display_name"
    UserList_display_name__contains:
      description: Filter by a case-sensitive substring on display_name
      in: query
      name: display_name__contains
      required: false
      schema:
        $ref: "#/components/schemas/display_name"
    UserList_display_name__icontains:
      description: Filter by a case-insensitive substring on display_name
      in: query
      name: display_name__icontains
      required: false
      schema:
        $ref: "#/components/schemas/display_name"
    UserList_email:
      description: Filter by an exact match on email
      in: query
      name: email
      required: false
      schema:
        $ref: "#/components/schemas/email"
    UserList_email__contains:
      description: Filter by a case-sensitive substring on email
      in: query
      name: email__contains
      required: false
      schema:
        $ref: "#/components/schemas/email"
    UserList_email__icontains:
      description: Filter by a case-insensitive substring on email
      in: query
      name: email__icontains
      required: false
      schema:
        $ref: "#/components/schemas/email"
    UserList_id:
      description: Filter by User ID. Multiple IDs can be specified using the format ?id=1&id=2.
      in: query
      name: id
      required: false
      schema:
        $ref: "#/components/schemas/id"
    UserList_profile_id:
      description: >
        Filter by User Profile ID. Multiple IDs can be specified using the format
        ?profile_id=1&profile_id=2.
      in: query
      name: profile_id
      required: false
      schema:
        $ref: "#/components/schemas/profile_id"
    UserList_order_by:
      description: >
        Sort by a specified property:
         * `display_name` - A to Z, by display_name
         * `-display_name` - Z to A, by display_name
         * `email` - A to Z, by email
         * `-email` - Z to A, by email
         * `id` - Ascending by id
         * `-id` - Descending by id
         * `profile_id` - Ascending by id
         * `-profile_id` - Descending by id
      in: query
      name: order_by
      required: false
      schema:
        allOf:
          - $ref: "./common/parameters.yaml#/components/schemas/OrderBy"
          - enum:
              - display_name
              - -display_name
              - email
              - -email
              - id
              - -id
              - profile_id
              - -profile_id

  securitySchemes:
    AlationAdminAPIKeyAuth:
      type: apiKey
      in: header
      name: TOKEN
      description: >
        API Key corresponding to a Server Admin user. See
        https://customerportal.alationdata.com/docs/GetToken/index.html and
        /openapi/api_authentication/ for more information.
    AlationUserAPIKeyAuth:
      type: apiKey
      in: header
      name: TOKEN
      description: >
        API Key corresponding to any user. See
        https://customerportal.alationdata.com/docs/GetToken/index.html and
        /openapi/api_authentication/ for more information.
    AlationBearerTokenAuth:
      type: http
      scheme: bearer
      description: >
        API Access Token corresponding to any user.
        Access Token can be generated manually or using OAuth endpoints.
        See /openapi/api_authentication/ or
        oauth endpoints specs for more information.
