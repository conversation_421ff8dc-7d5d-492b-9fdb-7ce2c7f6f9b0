info:
  description: PublicAPI for creating, reading, updating and deleting datasources,
    verifying connection configurations, metadata extraction configurations and triggering
    metadata extraction
  title: Datasources API
  version: 1.0.0
openapi: 3.0.0
paths:
  /datasource/:
    get:
      description: This API can be used to retrieve multiple existing data sources
        in bulk.
      operationId: getDatasources
      parameters:
      - $ref: '#/components/parameters/include_undeployed'
      - $ref: '#/components/parameters/include_hidden'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Datasources'
          description: Requested datasources
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Get a list of datasources
      tags:
      - datasource
    post:
      description: 'This API can be used to create a new datasource. Currently the
        certified types are `MySQL`, `Oracle`, `Postgres`, `SQL Server`, `Redshift`,
        `Teradata` and `Snowflake`. **Note**: Use the `host/port` **or** `uri` field
        to setup the datasource. The `uri` field can be used to store the jdbc uri
        connection string.'
      operationId: postDatasource
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Datasource'
        required: true
      responses:
        201:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Datasource'
          description: Datasource created successfully
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Create a datasource
      tags:
      - datasource
  /datasource/{datasource_id}/:
    delete:
      description: This API can be used to delete a datasource.
      operationId: deleteDatasourceById
      parameters:
      - $ref: '#/components/parameters/datasource_id'
      responses:
        200:
          description: Datasource successfully deleted
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Delete a datasource
      tags:
      - datasource
    get:
      description: This API can be used to retrieve a specific data source.
      operationId: getDatasourceById
      parameters:
      - $ref: '#/components/parameters/datasource_id'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Datasource'
          description: Requested datasource
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Get a datasource
      tags:
      - datasource
    put:
      description: 'This API can be used to update an existing datasource. **Note**:
        It is encouraged to update the jdbc connection string ie `uri` field instead
        of updating `host` and `port` information.'
      operationId: putDatasourceById
      parameters:
      - $ref: '#/components/parameters/datasource_id'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Datasource_Update_Payload'
        required: true
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Datasource'
          description: Datasource updated successfully
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Update a datasource
      tags:
      - datasource
  /datasource/{datasource_id}/available_schemas/:
    get:
      description: 'This API can be used to return the list of available schemas.
        **Note**: Run with `force_refresh = true` for the first time triggering this
        API. Not supported for virtual datasources.'
      operationId: getAvailableSchemas
      parameters:
      - $ref: '#/components/parameters/datasource_id'
      - $ref: '#/components/parameters/force_refresh'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Schemas'
          description: List of available schemas
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Get a list of available schemas
      tags:
      - datasource metadata extraction
  /datasource/{datasource_id}/configuration_check/:
    get:
      description: 'This API can be used to check various configuration of the datasource.
        Checks the network connections status, service account authentication and
        service account privileges. **Note**: Not supported for virtual datasources.'
      operationId: getAllConfigChecksById
      parameters:
      - $ref: '#/components/parameters/datasource_id'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status'
          description: All configs have been verified on the datasource.
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Verify configuration checks
      tags:
      - datasource config checks
  /datasource/{datasource_id}/configuration_check/network_connection/:
    get:
      description: 'This API can be used to check the network connection status of
        the datasource. **Note**: Not supported for virtual datasources.'
      operationId: getNetworkConnectionStatusById
      parameters:
      - $ref: '#/components/parameters/datasource_id'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status'
          description: Network connection is established
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Verify network connection status
      tags:
      - datasource config checks
  /datasource/{datasource_id}/configuration_check/service_account_authentication/:
    get:
      description: 'This API can be used to check the service account authentication
        status of the datasource. **Note**: Not supported for virtual datasources.'
      operationId: getServiceAccountAuthById
      parameters:
      - $ref: '#/components/parameters/datasource_id'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status'
          description: Service account has valid authentication status on the datasource.
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Verify service account authentication status
      tags:
      - datasource config checks
  /datasource/{datasource_id}/configuration_check/service_account_privileges/:
    get:
      description: 'This API can be used to check the service account privileges of
        the datasource. **Note**: Not supported for virtual datasources.'
      operationId: getServiceAccountPrivilegesById
      parameters:
      - $ref: '#/components/parameters/datasource_id'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status'
          description: Service account has valid privileges on the datasource.
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Verify service account privileges
      tags:
      - datasource config checks
  /datasource/{datasource_id}/metadata_extraction_job/:
    post:
      description: 'This API allows admins to trigger metadata extraction with or
        without parameters. It only works for datasources which are deployed. If parameters
        are passed then it overrides default parameters. Ensure that you run `available_schemas`
        API when you run this API only for the first time. **Note**: Not supported
        for virtual datasources.'
      operationId: postMDEJob
      parameters:
      - $ref: '#/components/parameters/datasource_id'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MDE_Extraction_Job'
        required: false
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JobID'
          description: MDE triggered successfully
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Trigger metadata extraction job
      tags:
      - datasource metadata extraction
  /datasource/{datasource_id}/sync_configuration/metadata_extraction/:
    get:
      description: 'This API can be used to retrieve the existing configurations for
        metadata extraction. **Note**: Not supported for virtual datasources.'
      operationId: getMDEConfigs
      parameters:
      - $ref: '#/components/parameters/datasource_id'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MDE_Configuration'
          description: Requested configuration of Metadata extraction
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Get metadata extraction configurations
      tags:
      - datasource metadata extraction
    patch:
      description: 'This API can be used to partially update the existing configurations
        for metadata extraction. **Note**: Not supported for virtual datasources.'
      operationId: patchMDEConfigs
      parameters:
      - $ref: '#/components/parameters/datasource_id'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MDE_Configuration_Patch_Payload'
        required: false
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MDE_Configuration'
          description: MDE configs updated successfully
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Update metadata extraction configurations
      tags:
      - datasource metadata extraction
    put:
      description: 'This API can be used to update the entire configurations for metadata
        extraction. **Note**: Not supported for virtual datasources.'
      operationId: putMDEConfigs
      parameters:
      - $ref: '#/components/parameters/datasource_id'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MDE_Configuration_Put_Payload'
        required: false
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MDE_Configuration'
          description: MDE configs updated successfully
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Update metadata extraction configurations
      tags:
      - datasource metadata extraction
  /datasources/bulk_migrate:
    post:
      description: This API is used to bulk migrate datasources for a particular connector
        which are using native connector to their respective OCF connector
      operationId: postMigrateDS
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Native_To_OCF_Migration_Body'
        required: true
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Native_To_OCF_Migration_Res'
          description: Datasources migrated successfully
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Bulk migrate datasources to OCF
      tags:
      - migrate native connector data sources to OCF
security:
- ApiKeyAuth: []
servers:
- url: '{protocol}://{base-url}/integration/v1/'
  variables:
    base-url:
      default: base-url
      description: Django BASE_URL setting for this instance.
    protocol:
      default: http
      enum:
      - http
      - https
tags:
- description: These APIs can be used to perform create, read, update and delete operations
    on datasource objects
  name: datasource
- description: These APIs can be used to run datasource configuration checks
  name: datasource config checks
- description: These APIs can be used to get available schemas, trigger metadata extraction
    and get or update metadata extraction configurations
  name: datasource metadata extraction
- description: These API(s) can be used to bulk migrate datasources using native connectors
    to their respective OCF connector
  name: migrate native connector data sources to OCF


components:
  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: TOKEN
      description: API Key for the user

  schemas:
    Datasource:
      description: Properties of a datasource Object
      type: object
      properties:
        dbtype:
          type: string
          description: >
            The database type. Currently the certified types are mysql, oracle, postgresql,
                sqlserver, redshift, teradata and snowflake.
          example: mysql
        host:
          type: string
          description: "The host of the datasource. Note: Not required if is_virtual == True
            or if the 'uri' parameter is provided."
          example: ************
        port:
          type: integer
          description: "The port of the datasource. Note: Not required if is_virtual == True
            or if the 'uri' parameter is provided."
          example: 3306
        uri:
          type: string
          description: Jdbc uri for connecting to datasources. Please ensure you either give host,
            port or just uri parameter. Please do not enter both the parameters.
          example: mysql://<hostname>:<port>/<db_name>
        dbname:
          type: string
          description: "The database name of the datasource. Note: Mandatory for Oracle (service
            name is dbname), Redshift and Postgresql datasource."
          example: sample_dbname
        db_username:
          type: string
          description: >
            The service account username. Note: Not required if deployment_setup_complete == False
                or is_virtual == True.
          example: alation
        db_password:
          type: string
          description: The service account password.
          writeOnly: true
          example: password
        title:
          type: string
          description: >
            The title of the datasource. Note: Not required if deployment_setup_complete == False.
          example: test_mysql
        description:
          type: string
          description: The description of the datasource.
          example: Sample mysql datasource setup
        deployment_setup_complete:
          type: boolean
          description: >
            Boolean flag determining if the deployment setup is complete. When set to true,
            complete datasource information is required, else, only partial information is
            required. Defaults to True.
          default: true
        private:
          type: boolean
          description: Boolean flag determining if the datasource is private. Defaults to False.
          default: false
        is_virtual:
          type: boolean
          description: Boolean flag determining if the datasource is virtual. Defaults to False.
          default: false
        is_hidden:
          type: boolean
          description: Boolean flag determining if the datasource is hidden. Defaults to False.
          default: false
        instance_or_warehouse_name:
          type: string
          description: The instance or warehouse name is required for Snowflake (when used with
            host/port) and optional for SQL Server. This is parsed and attached to the uri field.
          writeOnly: true
          example: instance_or_warehouse_name
        id:
          type: integer
          readOnly: true
          description: Unique identifier of the datasource. INTERNAL
        supports_explain:
          type: boolean
          readOnly: true
          description: INTERNAL
        data_upload_disabled_message:
          type: string
          readOnly: true
          description: INTERNAL
        hive_logs_source_type:
          type: integer
          readOnly: true
          description: Hive related
          default: 0
        metastore_uri:
          type: boolean
          readOnly: true
          description: Hive related
          nullable: true
        is_hive:
          type: boolean
          readOnly: true
          description: Hive related
        is_gone:
          type: boolean
          readOnly: true
          description: INTERNAL
        webhdfs_server:
          type: string
          readOnly: true
          description: Hive related
          nullable: true
        supports_qli_diagnostics:
          type: boolean
          readOnly: true
          description: INTERNAL
        is_presto_hive:
          type: boolean
          readOnly: true
          description: Presto related
        latest_extraction_time:
          type: string
          format: date-time
          description: INTERNAL
          readOnly: true
        negative_filter_words:
          type: array
          description: INTERNAL
          items:
            type: string
          nullable: true
          readOnly: true
        has_hdfs_based_qli:
          type: boolean
          readOnly: true
          description: Hive related
        can_data_upload:
          type: boolean
          readOnly: true
          description: INTERNAL
        qualified_name:
          type: string
          readOnly: true
          description: INTERNAL
        all_schemas:
          type: string
          description: INTERNAL
          nullable: true
          readOnly: true
        has_previewable_qli:
          type: boolean
          readOnly: true
          description: INTERNAL
        hive_tez_logs_source:
          type: string
          readOnly: true
          description: Hive related
          nullable: true
        has_metastore_uri:
          type: boolean
          readOnly: true
          description: Hive related
        webhdfs_port:
          type: integer
          readOnly: true
          description: Hive related
        supports_qli_daterange:
          type: boolean
          readOnly: true
          description: INTERNAL
        latest_extraction_successful:
          type: boolean
          readOnly: true
          description: INTERNAL
        owner_ids:
          type: array
          description: INTERNAL
          items:
            type: integer
          readOnly: true
        favorited_by_list:
          type: boolean
          readOnly: true
          description: INTERNAL
        supports_compose:
          type: boolean
          readOnly: true
          description: INTERNAL
        hive_logs_source:
          type: string
          readOnly: true
          description: Hive related
        enable_designated_credential:
          type: boolean
          readOnly: true
          description: INTERNAL
        deleted:
          type: boolean
          readOnly: true
          description: INTERNAL
        limit_schemas:
          type: string
          description: "Schemas to limit in MDE"
          nullable: true
          readOnly: true
        obfuscate_literals:
          type: array
          description: INTERNAL
          items:
            type: string
          nullable: true
          readOnly: true
        remove_filtered_schemas:
          type: boolean
          readOnly: true
          description: INTERNAL
        profiling_tip:
          type: string
          readOnly: true
          description: INTERNAL
          nullable: true
        supports_profiling:
          type: boolean
          readOnly: true
          description: INTERNAL
        webhdfs_username:
          type: string
          readOnly: true
          description: Hive related
        icon:
          type: string
          readOnly: true
          description: INTERNAL
        url:
          type: string
          readOnly: true
          description: INTERNAL
        otype:
          type: string
          readOnly: true
          description: INTERNAL
        exclude_schemas:
          type: string
          description: "Schemas to exclude in MDE"
          nullable: true
          readOnly: true
        qli_aws_region:
          type: string
          readOnly: true
          description: Hive related
        has_aws_glue_metastore:
          type: boolean
          readOnly: true
          description: Hive related
        exclude_additional_columns_in_qli:
          type: boolean
          readOnly: true
          description: INTERNAL
        can_toggle_ds_privacy:
          type: boolean
          readOnly: true
          description: INTERNAL
        aws_region:
          type: string
          readOnly: true
          description: Hive related
        aws_access_key_id:
          type: string
          readOnly: true
          description: Hive related
        supports_md_diagnostics:
          type: boolean
          readOnly: true
          description: INTERNAL
        nosql_mde_sample_size:
          type: integer
          readOnly: true
          description: INTERNAL
        disable_auto_extraction:
          type: boolean
          readOnly: true
          description: INTERNAL
        metastore_type:
          type: string
          readOnly: true
          description: Hive related
        unresolved_mention_fingerprint_method:
          type: string
          readOnly: true
          description: INTERNAL
        qli_hive_connection_source:
          type: string
          readOnly: true
          description: Hive related
        compose_oauth_enabled:
          type: boolean
          readOnly: true
          description: Snowflake and Databricks related
        enable_default_schema_extraction:
          type: boolean
          readOnly: true
          description: INTERNAL
        enabled_in_compose:
          type: boolean
          readOnly: true
          description: INTERNAL
        builtin_datasource:
          type: string
          readOnly: true
          description: INTERNAL
        has_aws_s3_based_qli:
          type: boolean
          readOnly: true
          description: Hive related
        qli_aws_access_key_id:
          type: string
          readOnly: true
          description: Hive related
        jdbc_driver:
          type: string
          readOnly: true
          description: INTERNAL
        cron_extraction:
          type: string
          readOnly: true
          description: INTERNAL
        supports_default_schema_extraction:
          type: boolean
          readOnly: true
          description: INTERNAL

      required:
        - dbtype
        - host
        - port
        - db_username
        - title

    Datasources:
      type: array
      items:
        $ref: "#/components/schemas/Datasource"

    Datasource_Update_Payload:
      description: Properties of Datasource Update Request payload.
      type: object
      properties:
        host:
          type: string
          description: "The host of the datasource. Note: Host/Port update only possible if data
            source is undeployed that is deployment_setup_complete == False."
          example: ************
        port:
          type: integer
          description: "The port of the datasource. Note: Host/Port update only possible if data
            source is undeployed that is deployment_setup_complete == False."
          example: 3306
        uri:
          type: string
          description: "The uri of the data source. Before deployment, data source host and port
            information can be updated. After deployment, a uri is constructed from the host and
                port, and instead of the host and port, the uri should be updated."
          example: mysql://<hostname>:<port>/<db_name>
        dbtype:
          type: string
          description: >
            The database type. Certified types are mysql, oracle, postgresql, sqlserver, redshift,
                teradata and snowflake.
            Note: Cannot update the dbtype if the datasource has been deployed that is
                deployment_setup_complete == True.
          example: mysql
        dbname:
          type: string
          description: "The database name of the datasource. Note: Mandatory for Oracle (service
            name is dbname), Redshift, Postgresql datasource. Cannot be updated if data source is
            deployed i.e. deployment_setup_complete == True. Please update the dbname directly in
            the jdbc uri"
          example: sample_dbname
        db_username:
          type: string
          description: >
            The service account username. Note: Mandatory only if the value was not previously
              provided when the data source was undeployed i.e. deployment_setup_complete was
              False.
          example: alation
        db_password:
          type: string
          description: The service account password.
          writeOnly: true
        title:
          type: string
          description: >
            The title of the datasource. Note: Mandatory only if the value was not previously
              provided when the data source was undeployed i.e. deployment_setup_complete was
              False.
          example: test_mysql
        description:
          type: string
          description: The description of the datasource.
          example: Sample mysql datasource setup
        deployment_setup_complete:
          type: boolean
          description: >
            Boolean flag determining if the deployment setup is complete. When set to true,
              complete data source information is required, otherwise, only partial information is
              required. After this field is updated to True and data source is deployed, it cannot
              be updated to False again. Defaults to True.
          default: true
        private:
          type: boolean
          description: Boolean flag determining if the datasource is private. Defaults to False.
          default: false
        compose_default_uri:
          type: string
          description: >
            Used to update Default Connection uri in Compose for a deployed Data Source i.e.
            deployment_setup_complete is True.
          example: mysql://<new_hostname>:<new_port>/<new_db_name>

    Status:
      description: Returns status of configuration check
      type: object
      properties:
        status:
          type: string
          description: Status of the configuration check

    Schemas:
      description: Returns list of schemas in a datasource
      type: array
      items:
        type: string

    MDE_Configuration:
      type: object
      description: Metadata extraction configuration object
      properties:
        cron_extraction:
          type: string
          description: >
            The extraction schedule in crontab format (minute, hour, day of month, month of year,
            day of week) example: "0 4 4 * 2".
        disable_auto_extraction:
          type: boolean
          description: >
            True if the extraction schedule should not be executed, false to run extraction
            according to cron_extraction.
        limit_schemas:
          type: array
          items:
            type: string
          description: >
            Schemas to include in metadata extraction. If there are no schemas to include, then set
              to an empty array. Both parameters - `limit_schemas` and `exclude_schemas` - cannot
              have values filled at the same time.
        exclude_schemas:
          type: array
          items:
            type: string
          description: >
            Schemas to exclude from metadata extraction. If there are no schemas to exclude,
              then set to an empty array. Both parameters - `exclude_schemas` and `limit_schemas` -
              cannot have values filled at the same time.
        remove_filtered_schemas:
          type: boolean
          description: Boolean flag determining if we need to remove existing schemas. If set to
            `True`, we mark existing schemas as deleted which are not present in metadata extraction
            process. Defaults to `False`.
          default: False

    MDE_Configuration_Patch_Payload:
      description: Metadata extraction configuration object for Patch payload
      allOf:
        - $ref: "#/components/schemas/MDE_Configuration"

    MDE_Configuration_Put_Payload:
      description: Metadata extraction configuration object for Put payload
      allOf:
        - $ref: "#/components/schemas/MDE_Configuration"
        - type: object
          required:
              - limit_schemas
              - exclude_schemas

    MDE_Extraction_Job:
      type: object
      description: Metadata extraction job object
      properties:
        limit_schemas:
          type: array
          items:
            type: string
          description: >
            Schemas to include in metadata extraction. If there are no schemas to include, then set
              to an empty array. Both parameters - `limit_schemas` and `exclude_schemas` - cannot
              have values filled at the same time.
        exclude_schemas:
          type: array
          items:
            type: string
          description: >
            Schemas to exclude from metadata extraction. If there are no schemas to exclude,
              then set to an empty array. Both parameters - `exclude_schemas` and `limit_schemas` -
              cannot have values filled at the same time.
        remove_filtered_schemas:
          type: boolean
          description: Boolean flag determining if we need to remove existing schemas. If set to
            `True`, we mark existing schemas as deleted which are not present in metadata extraction
            process. Defaults to `False`.
          default: False
      required:
        - limit_schemas
        - exclude_schemas
        - remove_filtered_schemas

    Native_To_OCF_Migration_Body:
      type: object
      description: Native to OCF migration payload
      properties:
        db_type: 
          type: string
          description: Type of connector to migrate from native to OCF from list 'AZUREDW', 'DB2', 'GREENPLUM', 'MEMSQL', 'MYSQL', 'ORACLE', 'POSTGRESQL', 'REDSHIFT', 'SAS',
                      'SNOWFLAKE', 'SYBASE', 'SYBASEASE', 'TERADATA', 'VERTICA', 'HIVE2', 'DATABRICKS', 'IMPALA', 
                      'BIGQUERY', 'SQLSERVER', 'SAP'.
          example: MYSQL
        ocf_connector_id:
          type: integer
          description: Connector id of the OCF connector to be migrated to.
          example: 2
        datasources_to_skip:
          type: array
          items:
            type: integer
          description: List of data sources ids that needs to be skipped.
      required:
        - db_type
        - ocf_connector_id
    
    Native_To_OCF_Migration_Res:
      description: Returns list of datasources migrated successfully, failed, skipped
      type: object
      properties:
        successful_datasource:
          type: array
          items:
            type: object
        failed_datasource:
          type: array
          items:
            type: object
        skipped_datasource:
          type: array
          items:
            type: object

    JobID:
      description: Properties of a Job Object
      type: object
      properties:
        job_id:
          type: integer
          description: >
            GET /api/v1/bulk_metadata/job/?id={job_id} will return the status of the underlying
            POST job

    Error:
      type: object
      description: Properties of a Error Object
      properties:
        status:
          type: string
          description: The HTTP status code
        title:
          type: string
          description: The title of the error message
        details:
          type: string
          description: More information about the error
      required:
        - status
        - title
        - details

  responses:

    Standard_400_Error_Response:
      description: Malformed Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            status: 400
            title: "Malformed Request"
            detail: "The request sent by user is in the wrong format."

    Standard_401_Error_Response:
      description: Unauthorized bad/missing token
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            status: 401
            title: "Unauthorized"
            detail: "You are unauthorized to access this resource. Please obtain valid credentials"
    Standard_403_Error_Response:
      description: Forbidden User cannot edit this resource
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            status: 403
            title: "Forbidden Action"
            detail: "This is forbidden to access. Make sure you access the right resource."
    Standard_404_Error_Response:
      description: The specified resource was not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            status: 404
            title: "Resource not found"
            detail: "This is not a valid resource. Please try something else."
    Standard_500_Error_Response:
      description: Internal Server Error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            status: 500
            title: "Internal Server Error"
            detail: "Something went wrong, Please try again later."

  parameters:
    include_undeployed:
      name: include_undeployed
      description: >
        Specifies if undeployed datasources should be included in retrieved list. Undeployed data
          sources are the ones whose configurations and deployment setup is not complete. Admins
          cannot trigger metadata extraction on undeployed data sources.
      in: query
      schema:
        type: boolean
      required: false

    include_hidden:
      name: include_hidden
      description: >
        Specifies if hidden datasources should be included in retrieved list. Hidden data sources
          are not visible in the UI. These are the data sources created via the API with the
          `is_hidden` property set to `True`. There is no UI for the Settings page of such sources
          and they can only be accessed through the API.
      in: query
      schema:
        type: boolean
      required: false

    datasource_id:
      name: datasource_id
      description: "`id` of the datasource"
      in: path
      schema:
        type: integer
      required: true
      examples:
        id:
          value: 10
          summary: A sample integer datasource id

    force_refresh:
      name: force_refresh
      description: Specifies if schema list retrieved should be refreshed.
      in: query
      schema:
        type: boolean
      required: false
