info:
  description: PublicAPI for Administration of Data Health
  title: Data Health API
  version: 1.0.0
openapi: 3.0.0
paths:
  /data_quality/:
    post:
      description: Handles the creation and update of data health fields and object
        health values
      operationId: data_health_ingestion
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ingestion_parameters'
        required: true
      responses:
        202:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/job_id'
          description: >
            The JobID and url of the underlying bulk operation running asynchronously.
            Perform a GET request on the url with your user TOKEN to inspect the job results
        400:
          $ref: '#/components/responses/bad_arguments'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Upsert rules and data health values
      tags:
      - Data Health Ingestion
    delete:
      description: Handles the deletion of data health fields and object
        health values
      operationId: data_health_deletion
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/deletion_parameters'
        required: true
      responses:
        202:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/job_id'
          description: >
            The JobID and url of the underlying bulk operation running asynchronously.
            Perform a GET request on the url with your user TOKEN to inspect the job results
        400:
          $ref: '#/components/responses/bad_arguments'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Delete rules and data health values
      tags:
      - Data Health Deletion
  /data_quality/fields/:
    get:
      description: Retrieve information about data quality fields
      summary: Retrieve information about data quality fields
      parameters:
        - in: query
          name: key
          schema:
            type: array
            items: 
              type: string
          description: Filter response by field_key. Pass in argument multiple times to filter on multiple field keys. i.e. `?key=field1&key=field2`
          example: ['field.key1','field.key2']
        - in: query
          name: order_by
          schema:
            type: array
            items:
              type: string
              enum: ['key', 'name', 'description', 'type', 'ts_created']
          description: Order by response by field. Prepend order_by value with '-' to sort by field descending
          example: ['-ts_created']
        - in: query
          name: limit
          schema:
            type: integer
          description: Size limit of the page. Defaults to 100 entries.
          example: 100
        - in: query
          name: skip
          schema:
            type: integer
          description: Skip x number of records.
          example: 0
      responses:
        200:
          description: Successfully retrieved field list
          content:
            application/json:
              schema: 
                type: array
                items:
                  $ref: '#/components/schemas/data_quality_field'
          headers:
            x-total-count:
              schema:
                type: integer
              description: Total count of all items that fulfill the request. This number can be greater than what is displayed per page of results.
            x-next-page:
              schema:
                type: string
              description: Url of the continuation onto the next page for this current result
            x-prev-page:
              schema:
                type: string
              description: Url of the previous page for this current result
      tags:
      - Data Health Retrieval
  /data_quality/values/:
    get:
      description: |
        Retrieve information about data quality values. Values by default will return all values applied to objects, both directly and related.
        Related values are values applied to objects that would indirectly affect another object.
        For example, a value applied on an Attribute would have that same value applied on the owning parent Table.
        Alation automatically sets and propagates these relations during ingestion.
        This default result is the same data as you would see on the Data Health tab in the Alation catalog.

        This indirect relation is reflected with the fields `object_key` `object_name` `otype` `oid`.

        The originating source object the value was applied to are reflected with the fields `source_object_key` `source_object_name` `source_otype` `source_oid`
        
        To only retrieve values that were directly assigned without propagated value results, use the `hide_related` query parameter.
        
      summary: Retrieve information about data quality values
      parameters:
        - in: query
          name: object_key
          schema:
            type: array
            items: 
              type: string
          example: ['1.mySchema.myTable.myColumn', '1.mySchema.myTable.myColumn2']
          description: >
            Filter values on those applied indirectly by the related object's api_keys. 
        - in: query
          name: source_object_key
          schema:
            type: array
            items: 
              type: string
          description: Filter values on those applied directly to a source object's api_key.
          example: ['1.mySchema.myTable.myColumn', '1.mySchema.myTable.myColumn2']
        - in: query
          name: field_key
          schema:
            type: array
            items: 
              type: string
          description: Filter values on those applied on specified field keys.
          example: ['1.mySchema.myTable.myColumn', '1.mySchema.myTable.myColumn2']
        - in: query
          name: value_quality
          schema:
            type: string
            enum: ['GOOD', 'WARNING', 'ALERT']
          description: Filter values by their quality
          example: 'ALERT'
        - in: query
          name: hide_related
          schema:
            type: boolean
          description: |
            Setting this to true will hide values that have been propagated to related objects. 
            For example, values that have been set on an Attribute will NOT be shown on the owning Table
            when this is set to `True`.

            With the value set to `False`, value assignments will be shown on related objects as inherited or propagated values.

            Parameter is set to `False` by default
          example: false
        - in: query
          name: order_by
          schema:
            type: array
            items:
              type: string
              enum: ['object_key', 'source_object_key', 'field_key', 'value_id', 'value_quality', 'value_last_updated']
          description: Order by response by field. Prepend order_by value with '-' to sort by field descending
          example: ['object_key']
        - in: query
          name: limit
          schema:
            type: integer
          description: Size limit of the page. Defaults to 100 entries.
          example: 100
        - in: query
          name: skip
          schema:
            type: integer
          description: Skip x number of records.
          example: 0
      responses:
        200:
          description: Successfully retrieved values list
          content:
            application/json:
              schema: 
                type: array
                items:
                  $ref: '#/components/schemas/data_quality_value'
          headers:
            x-total-count:
              schema:
                type: integer
              description: Count of all items that meet request
            x-next-page:
              schema:
                type: string
              description: Url of the continuation onto the next page for this current result
            x-prev-page:
              schema:
                type: string
              description: Url of the previous page for this current result     
      tags:
      - Data Health Retrieval
security:
- ApiKeyAuth: []
servers:
- url: '{protocol}://{base-url}/integration/v1'
  variables:
    base-url:
      default: localhost
      description: Alation BASE_URL setting for this instance.
    protocol:
      default: https
      enum:
      - http
      - https

components:
  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: TOKEN
      description: API Key for the user

  responses:
    bad_arguments:
      description:  >
        Returned when invalid arguments are sent to the request.
        The 'Entry' key reflects the position of item that failed validation.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/bad_ingestion_request'
    Standard_400_Error_Response:
      description: Malformed Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "400XXX"
            title: "Malformed Request"
            detail: "The request sent by user is in the wrong format.
            (Refer the error documentation for specific details of the error)"
    Standard_401_Error_Response:
      description: Unauthorized bad/missing token
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "401XXX"
            title: "Unauthorized"
            detail: "You are unauthorized to access this resource. Please obtain valid credentials.
            (Refer the error documentation for specific details of the error)"
    Standard_403_Error_Response:
      description: Forbidden User cannot edit this resource
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "403XXX"
            title: "Forbidden Action"
            detail: "This is forbidden to access. Make sure you access the right resource.
            (Refer the error documentation for specific details of the error)"
    Standard_404_Error_Response:
      description: The specified resource was not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "404XXX"
            title: "Resource not found"
            detail: "This is not a valid resource. Please try something else.
            (Refer the error documentation for specific details of the error)"
    Standard_500_Error_Response:
      description: Internal Server Error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "500XXX"
            title: "Internal Server Error"
            detail: "Something went wrong, Please try again later.
            (Refer the error documentation for specific details of the error)"


  schemas:
    Error:
      type: object
      description: Properties of a Error Object
      properties:
        status:
          type: string
          description: The HTTP status code
        title:
          type: string
          description: The title of the error message
        details:
          type: string
          description: More information about the error
      required:
        - status
        - title
        - details
    job_id:
      type: object
      properties:
        job_id:
          example: 2000
          description: >
            Provides the job identifier for the job that will be running asynchronously from request.  This must be provided 
            in order to get status of this operation.  It can be accessed at -> 
            GET /api/v1/bulk_metadata/job/?id={job_id} will return the status of the underlying POST job
        href:
          description: >
            Provides referential link to bulk job endpoint
          example: /api/v1/bulk_metadata/job/?id=2000 
    field_key_sample_response:
      type: object
      properties:
        field_key: 
          type: string
          example: field.key.1
          description: >
            Sample field key created
    value_key_sample_response:
      type: object
      properties:
        field_key:
          type: string
          example: field.key.1
          description: >
            Field key the value is attributed to
        object_key:
          type: string
          example: '1.schema_name.table_name.attr_name'
          description: >
            The key's object the value is attributed to
    ingestion_job_response:
      type: object
      properties:
        status:
          type: string
          example: "successful"
          description: >
            Can be one of "running" | "successful" | "failed". Will have different messages based on each
        msg:
          type: string
          example: "Job finished in 5 seconds at 2022-01-01 01:01:01+00:00"
          description: >
            Short description of the job state
        result:
          type: object
          properties:
            fields:
              type: object
              properties:
                created:
                  type: object
                  properties:
                    count:
                      type: integer
                      example: 10
                      description: >
                        The total count of fields that were created
                    sample:
                      type: array
                      items: 
                        $ref: '#/components/schemas/field_key_sample_response'
                      example: [{"field_key": "field.key.1"}, ...]
                      description: >
                        A limited sample of fields that were successfully created
                updated:
                  type: object
                  properties:
                    count:
                      type: integer
                      example: 10
                      description: >
                        The total count of fields that already existed in the system and were updated
                    sample:
                      type: array
                      items: 
                        $ref: '#/components/schemas/field_key_sample_response'
                      example: [{"field_key": "field.key.1"}, ...]
                      description: >
                        A limited sample of fields that were successfully updated
            values:
              type: object
              properties:
                created:
                  type: object
                  properties:
                    count:
                      type: integer
                      example: 10
                      description: >
                        The total count of values that were created
                    sample:
                      type: array
                      items:
                        $ref: '#/components/schemas/value_key_sample_response'
                      example: [{"field_key": "field.key.1", "object_key": "1.schema_name.table_name"}, ...]
                      description: >
                        A limited sample of values that were successfully created
                updated:
                  type: object
                  properties:
                    count:
                      type: integer
                      example: 10
                      description: >
                        The total count of values that already existed in the system and were updated
                    sample:
                      type: array
                      items:
                        $ref: '#/components/schemas/value_key_sample_response'
                      example: [{"field_key": "field.key.1", "object_key": "1.schema_name.table_name"}, ...]
                      description: >
                        A limited sample of values that were successfully updated
            object_attribution:
              type: object
              properties:
                success_count:
                  type: integer
                  example: 5
                  description: >
                    The total count of created data quality values that were able to find the corresponding object to
                    attribute the value to in Alation
                failure_count:
                  type: integer
                  example: 5
                  description: >
                    The total count of created values that were unable to find the corresponding object 
                    in Alation and will not be accessible in the catalog. Please confirm that the specified
                    object_key maps correctly to the fully qualified name as defined in Alation.
                success_sample:
                  type: array
                  items:
                    $ref: '#/components/schemas/value_key_sample_response'
                  example: [{"field_key": "field.key.1", "object_key": "1.schema_name.found_table"}, ...]
                  description: >
                    A limited sample of values that were successfully attributed to an object found in Alation
                failure_sample:
                  type: array
                  items:
                    $ref: '#/components/schemas/value_key_sample_response'
                  example: [{"field_key": "field.key.1", "object_key": "1.schema_name.not_found_table"}, ...]
                  description: >
                    A limited sample of values that was not able to be attributed to an existing object in Alation
            flag_counts:
              type: object
              properties:
                GOOD:
                  type: integer
                  example: 2
                  description: >
                    The total amount of values that have been updated or created to GOOD
                WARNING:
                  type: integer
                  example: 2
                  description: >
                    The total amount of values that have been updated or created to WARNING
                ALERT:
                  type: integer
                  example: 2
                  description: >
                    The total amount of values that have been updated or created to ALERT  
            error:
              type: string
              example: 'Could not find the associated field_key for the value'
              description: >
                This field will only appear when there's an error that occurs during the ingestion job.
    deletion_job_response:
      type: object
      properties:
        status:
          type: string
          example: "successful"
          description: >
            Can be one of "running" | "successful" | "failed". Will have different messages based on each
        msg:
          type: string
          example: "Job finished in 5 seconds at 2022-01-01 01:01:01+00:00"
          description: >
            Short description of the job state
        result:
          type: object
          properties:
            fields:
              type: object
              properties:
                deleted:
                  type: object
                  properties:
                    count:
                      type: integer
                      example: 10
                      description: >
                        The total count of fields that already existed in the system and were deleted
                    sample:
                      type: array
                      items:
                        $ref: '#/components/schemas/field_key_sample_response'
                      example: [{"field_key": "field.key.1"}, ...]
                      description: >
                        A limited sample of fields that were successfully deleted
                not_found:
                  type: object
                  properties:
                    count:
                      type: integer
                      example: 10
                      description: >
                        The total count of fields that don't exist in the system
                    sample:
                      type: array
                      items:
                        $ref: '#/components/schemas/field_key_sample_response'
                      example: [{"field_key": "field.key.1"}, ...]
                      description: >
                        A limited sample of the requested fields to delete that did not exist
            values:
              type: object
              properties:
                deleted:
                  type: object
                  properties:
                    count:
                      type: integer
                      example: 10
                      description: >
                        The total count of values that already existed in the system and were deleted
                    sample:
                      type: array
                      items:
                        $ref: '#/components/schemas/value_key_sample_response'
                      example: [{"field_key": "field.key.1", "object_key": "1.schema_name.table_name"}, ...]
                      description: >
                        A limited sample of values that were successfully deleted
                not_found:
                  type: object
                  properties:
                    count:
                      type: integer
                      example: 10
                      description: >
                        The total count of values that don't exist in the system
                    sample:
                      type: array
                      items:
                        $ref: '#/components/schemas/value_key_sample_response'
                      example: [{"field_key": "field.key.1", "object_key": "1.schema_name.table_name"}, ...]
                      description: >
                        A limited sample of values that don't exist
            error:
              type: string
              example: 'The deletion job was unable to finish'
              description: >
                This field will only appear when there's an error that occurs during the deletion job.
    bad_ingestion_request:
      type: object
      properties:
        Entry:
          type: string
          description: >
            The entry that ran into a validation problem.  This is a 1-based entry dependent on order passed in to parameter.
            An entry of 2, for example, connotates that the second entry that was passed into the request had validation problems.
          example: 2
        Message:
          type: string
          description: >
            The problem with the request that generated a validation error.
          example: Nulls are not allowed for this property.

    ingestion_field:
      type: object
      required:
        - field_key
        - name
        - type
      properties:
        field_key:
          type: string
          description: >
            The unique key for the rule being setup in Alation.
          example: my.example.rule.key
        name:
          type: string
          description: >
            The human-readable label for the rule.
          example: The Example Rule
        type:
          type: string
          description: >
            The data type of values associated with rule.  Allowed values are NUMERIC, STRING, BOOLEAN.
          example: STRING
        description:
          type: string
          description: >
            Allows for decoration of the rule to fully describe the context of the rule.
          example: Lorem ipsum dolor sit amet, consectetur adipiscing elit...
    ingestion_value:
      type: object
      required:
        - field_key
        - object_key
        - object_type
        - status
        - value
      properties:
        field_key:
          type: string
          description: >
            Unique key for a rule that has been setup in Alation.  The field must already exist or in the fields array to be valid.
          example: my.example.rule.key
        object_key:
          type: string
          description: >
            The api key descriptor for a catalog object.  
             * `table` - <datasource_id>.[&lt;dbname>/<catalog_name>.]<schema_name>.<table_name> NOTE:
             <db_name> has to be specified for SQL Server, Redshift, Snowflake and Netezza.
             * `column/attribute` - <datasource_id>.[&lt;dbname>/<catalog_name>.]<schema_name>.<table_name>.<column_name>
             NOTE: <db_name> has to be specified for SQL Server, Redshift, Snowflake and Netezza.
          example: 1.mySchema.myTable.myColumn
        object_type:
          type: string
          description: >
            Required descriptor with values of ATTRIBUTE, TABLE, SCHEMA that act as a hint for api key resolution.  Specifically this
            should be filled in if the object_key contains references to a multi-part schema.  If this is not filled out, resolution
            of object key will run through the default algorithm instead.
          example: ATTRIBUTE
        status:
          type: string
          description: >
            The status of the object on application of the rule.  Allowed values are GOOD, WARNING, ALERT.
          example: GOOD
        value:
          oneOf:
            - type: string
            - type: number
            - type: boolean
          description: >
            The value associated with the execution of a rule on target object.
          example: 25
        url: 
          type: string
          description: >
            External URL that allows pointing to specific site for further information about rule.
            Paths starting with `/` will point to pages on Alation (/article/100/)
            Use a full url including protocol to reference pages outside of Alation (https://outside.link/)
          example: /article/100/
        last_updated:
          type: string
          description:
            ISO 8601 formatted date time string indicating when the value was last updated.
            If no last_updated value is provided, the time will be set to when the value was ingested into Alation
          example: '2022-01-01T02:50:00+00:00'
    deletion_field:
      type: string
      description: >
        The unique key for the rule being deleted from Alation.
      example: my.example.rule.key
          
    deletion_value:
      type: object
      required:
        - field_key
        - object_key
      properties:
        field_key:
          type: string
          description: >
            Unique key for a rule that has been deleted from Alation.  The field must already exist or in the fields array to be valid.
          example: my.example.rule.key
        object_key:
          type: string
          description: >
            The api key descriptor for a catalog object.  
             * `table` - <datasource_id>.[&lt;dbname>/<catalog_name>.]<schema_name>.<table_name> NOTE:
             <db_name> has to be specified for SQL Server, Redshift, Snowflake and Netezza.
             * `column/attribute` - <datasource_id>.[&lt;dbname>/<catalog_name>.]<schema_name>.<table_name>.<column_name>
             NOTE: <db_name> has to be specified for SQL Server, Redshift, Snowflake and Netezza.
          example: 1.mySchema.myTable.myColumn

    ingestion_parameters:
      type: object
      description: >
        The parameters required for ingestion of data health information into Alation.  While fields and values are optional, one must be provided.
      properties:
        fields:
          type: array
          description: >
            Represents the list of health rules that will be created/updated 
          items:
            $ref: "#/components/schemas/ingestion_field"
        values:
          type: array
          description: >
            Represents the field rule value associated with a catalog object that will be created/updated
          items:
            $ref: "#/components/schemas/ingestion_value"

    deletion_parameters:
      type: object
      description: >
        The parameters required for deleting data health information from Alation.  While fields and values are optional, one must be provided.
        The deletion of a field will remove all values associated to that field.
      properties:
        fields:
          type: array
          description: >
            Represents the list of health rules that will be deleted 
          items:
            $ref: "#/components/schemas/deletion_field"
        values:
          type: array
          description: >
            Represents the field rule value associated with a catalog object that will be deleted
          items:
            $ref: "#/components/schemas/deletion_value"
    data_quality_field:
      type: object
      properties:
        key:
          type: string
          description: >
            The unique key that was assigned to the rule on ingestion
          example: my.example.rule.key
        name:
          type: string
          description: >
            The human-readable label for the rule.
          example: The Example Rule
        description:
          type: string
          description: >
            The assigned description of the given rule
          example: 'Lorem ipsum dolor sit amet...'
        type:
          type: string
          description: >
            The data type of values associated with rule.  Values are NUMERIC, STRING, BOOLEAN.
          example: NUMERIC
        ts_created:
          type: string
          description: >
            The ISO 8601 formatted date of when the rule was created
          example: '2022-01-01T02:50:00+00:00'
    data_quality_value:
      type: object
      properties:
        object_key:
          type: string
          description: >
            The applied object's api_key. It is used to uniquely identify an object in Alation.
            The value is applied on this object through relation
            It could, but does not necessarily mean the application of the value originated on this object
          example: 1.mySchema.myTable
        object_name:
          type: string
          description: >
            The applied object's name. 
            The value is applied on this object through relation
            It could, but does not necessarily mean the application of the value originated on this object
          example: myTable
        otype:
          type: string
          description: >
            The applied object's object type. An otype is used in conjunction with its oid to uniquely identify an object in Alation.
            The value is applied on this object through relation
            It could, but does not necessarily mean the application of the value originated on this object
          example: table
        oid:
          type: string
          description: >
            The applied object's id. An oid is used in conjunction with its otype is used to uniquely identify an object in Alation.
            The value is applied on this object through relation
            It could, but does not necessarily mean the application of the value originated on this object
          example: 1
        source_object_key:
          type: string
          description: >
            The original object's api_key that the rule was applied on.
            The value is directly applied on this object
            This field reflects original object that the value was applied to
          example: 1.mySchema.myTable.myColumn
        source_object_name:
          type: string
          description: >
            The original object's name
            The value is directly applied on this object
            This field reflects original object that the value was applied to
          example: myColumn
        source_otype:
          type: string
          description: >
            The original object's otype that the rule was applied on. An otype is used in conjunction with its oid to uniquely identify an object in Alation.
            The value is directly applied on this object
            This field reflects original object that the value was applied to
          example: attribute
        source_oid:
          type: string
          description: >
            The original object's oid that the rule was applied on. An oid is used in conjunction with its otype is used to uniquely identify an object in Alation.
            The value is directly applied on this object
            This field reflects original object that the value was applied to
          example: 1
        value_id:
          type: integer
          description: >
            The applied value's id
          example: 1
        value_value:
          anyOf:
            - type: string
            - type: integer
            - type: boolean
          description: >
            The applied value's value
          example: 100
        value_quality:
          type: string
          description: >
            The applied value's quality
          example: WARNING
        value_last_updated:
          type: string
          description: >
            The value's last updated timestamp in ISO-8601 format
            Is set automatically on ingestion if not explicitly defined
          example: "2022-01-01 01:01:01+00:00"
        value_external_url:
          type: string
          description: >
            The applied value external url
          example: "/article/10/"
        field_key:
          type: string
          description: >
            The applied field's key
          example: my.example.rule.key
        field_name:
          type: string
          description: 
            The applied field's name
          example: The Example Rule
        field_description:
          type: string
          description:
            The applied field's description
          example: Lorem ipsum dolor sit amet, consectetur adipiscing elit...
        
        
        
      