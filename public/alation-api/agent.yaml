info:
  description: Public API to enable programmatic management of the Alation Agent. This API is only accessible to Server Administrators.
  title: Alation Agent API
  version: 1.0.0
openapi: 3.0.0
paths:
  /agent/:
    get:
      description: Retrieves a list of Alation Agents in your Alation instance.
      operationId: listAgents
      parameters:
        - $ref: '#/components/parameters/Limit'
        - $ref: '#/components/parameters/Skip'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Agents'
          description: Agents retrieved successfully
          headers:
            X-Next-Page:
              description: URL to fetch next page of agents
              required: false
              schema:
                format: uri
                type: string
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Get a list of Agents
      tags:
        - Agent
    post:
      description: Creates an Alation Agent object in your Alation Cloud Service instance. After creating the Alation Agent object, a signed certificate must be created for the Agent so it can establish a connection to your Alation Cloud Service instance.
      operationId: createAgent
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Agent'
        required: true
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Agent'
          description: Agent created successfully
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Create an Alation Agent
      tags:
        - Agent
  /agent/endpoint/:
    get:
      description: Returns the Alation Cloud Service endpoint that your Alation Agent establishes an inbound connection to. The response can be used as the value of the `address` property in the Agent's `/etc/hydra/hydra.toml` file.
      operationId: getAgentConnectivityEndpoint
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AgentConnectivityEndpoint'
          description: Agent connectivity endpoint retrieved successfully
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Retrieve the Agent connectivity endpoint
      tags:
        - Agent
  /agent/{agent_id}/:
    delete:
      description: Deletes an Alation Agent, revoking its active certificate in the process.
      operationId: deleteAgent
      parameters:
        - $ref: '#/components/parameters/AgentId'
      responses:
        204:
          description: Agent deleted successfully
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Delete an Alation Agent
      tags:
        - Agent
    get:
      description: Retrieves details of an Alation Agent.
      operationId: getAgent
      parameters:
        - $ref: '#/components/parameters/AgentId'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Agent'
          description: Agent retrieved successfully
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Get an Alation Agent
      tags:
        - Agent
    patch:
      description: Updates details of an Alation Agent.
      operationId: updateAgent
      parameters:
        - $ref: '#/components/parameters/AgentId'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Agent'
        required: true
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Agent'
          description: Agent updated successfully
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Update an Alation Agent
      tags:
        - Agent
  /agent/{agent_id}/resync/:
    post:
      description: Deletes all connectors installed on the remote Alation Agent and triggers re-installation of the same connectors. For any given agent, only one re-sync task may be triggered at one time (that is, multiple simultaneous re-syncs are not permitted for one agent). However, any number of simultaneous re-syncs may be performed for differing agents.
      operationId: resyncAgentById
      parameters:
        - $ref: '#/components/parameters/AgentId'
      responses:
        202:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JobId'
          description: Agent Resync initiated successfully
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Resync an Alation Agent
      tags:
        - Agent
  /agent/{agent_id}/revoke_certificate/:
    post:
      description: Revokes the certificate of the Alation Agent. A new signed certificate for the agent must be created before operations on its connectors can resume. Alation Agents that are currently connected using a revoked certificate will be forcibly disconnected. This forceful disconnect typically occurs within the hour (pursuant to RFC 5019 Section 6 regarding OCSP response cache invalidation).
      operationId: revokeAgentCertificate
      parameters:
        - $ref: '#/components/parameters/AgentId'
      responses:
        204:
          description: Agent certificate revoked successfully
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Revoke the certificate of the Alation Agent
      tags:
        - Agent
  /agent/{agent_id}/sign_certificate/:
    post:
      description: Signs a digital identity certificate for the Alation Agent. If the Agent has an existing certificate active, it will be revoked. Install the returned certificate on your Alation Agent using `kratos` and restart `hydra` to establish a connection with your Alation Cloud Service instance.
      operationId: signAgentCertificate
      parameters:
        - $ref: '#/components/parameters/AgentId'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CertificateSigningRequest'
        required: true
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SignedCertificate'
          description: Agent certificate signed successfully
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Sign a certificate for the Alation Agent
      tags:
        - Agent
security:
  - AlationAdminAPIKeyAuth: []
servers:
  - url: '{protocol}://{base-url}/integration/v1'
    variables:
      base-url:
        default: <base-url>
        description: Django BASE_URL setting for this instance.
      protocol:
        default: http
        enum:
          - http
          - https
tags:
  - description: This API allows you to manage your Alation Agents programmatically.
    name: Agent
components:
  securitySchemes:
    AlationAdminAPIKeyAuth:
      type: apiKey
      in: header
      name: TOKEN
      description: >
        API Key corresponding to a Server Admin user. See https://developer.alation.com/dev/reference/createapiaccesstoken and /openapi/api_authentication/ for more information.

  schemas:
    AgentConnectivityEndpoint:
      description: Properties of an AgentConnectivityEndpoint object.
      type: object
      properties:
        endpoint:
          type: string
          format: uri
          example: "ocf.alationcloud.com"
          description: The Alation Cloud Service endpoint that your Alation Agent establishes an inbound connection to.
      required:
        - endpoint
    SignedCertificate:
      description: Properties of a signed agent certificate
      type: object
      properties:
        chain:
          type: string
          description: >
            A certificate path compliant with RFC 5280 Section 3.2. Each entity in the path is a PEM-encoded x.509 document. The path begins with the leaf (the agent's) certificate, followed by zero or more intermediates, and finally the Alation Agent root certificate authority.

      required:
        - chain
    CertificateMetadata:
      description: The Alation Agent certificate metadata, if the agent has a signed certificate.
      type: object
      properties:
        created_at:
          type: string
          format: rfc2822
          example: "Thu, 09 Mar 2023 10:30:00 -0000"
          readOnly: true
        expires_at:
          type: string
          format: rfc2822
          example: "Thu, 15 Feb 2024 5:50:00 -0000"
          readOnly: true
      required:
        - created_at
        - expires_at
    CertificateSigningRequest:
      description: Properties of a certificate signing request
      type: object
      properties:
        CSR:
          type: string
          description: >
            A PEM-encoded certificate signing request (CSR) compliant with RFC 2986. This CSR describes the public key installed to the originating Alation Agent and is used to generate a final certificate that the Alation Agent may use to authenticate itself.

      required:
        - CSR
    Agents:
      type: array
      items:
        $ref: "#/components/schemas/Agent"
    Agent:
      description: Properties of an Agent object
      type: object
      properties:
        id:
          type: integer
          example: 1
          description: The identifier of the Alation Agent.
          readOnly: true
        name:
          type: string
          example: "US-East Production"
          description: >
            A user friendly name for the Alation Agent. It is common to use this field to convey brief, high level information about the Alation Agent (such as environment, region, or network locality).

        description:
          type: string
          example: "Alation Agent for cataloging Vertica and Oracle"
          description: A user friendly description of the Alation Agent.
        is_connected:
          type: boolean
          description: Indicates whether the Alation Agent is connected to your Alation instance.
          readOnly: true
        certificate_metadata:
          allOf:
            - $ref: "#/components/schemas/CertificateMetadata"
          readOnly: true
      required:
        - name
        - description
        - is_connected
    JobId:
      description: Properties of a JobId Object
      type: object
      properties:
        job_id:
          type: integer
          description: >
            The identifier of the job launched to resync the targeted Alation Agent.

             GET /api/v1/bulk_metadata/job/?id={job_id} will return the status of the job.
          example: 714
      required:
        - job_id
    Error:
      type: object
      description: Properties of an Error Object
      properties:
        code:
          type: string
          description: The error status code
          example: "404000"
        title:
          type: string
          description: The title of the error message
          example: "Not Found"
        detail:
          type: string
          description: More information about the error
          example: "The Agent with provided Id was not found in Alation"
      required:
        - code
        - title
        - detail
  responses:
    Standard_400_Error_Response:
      description: Malformed Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "400XXX"
            title: "Malformed Request"
            detail: "The request sent by user is in the wrong format. (Refer the error documentation for specific details of the error)"
    Standard_401_Error_Response:
      description: Unauthorized bad/missing token
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "401XXX"
            title: "Unauthorized"
            detail: "You are unauthorized to access this resource. Please obtain valid credentials. (Refer the error documentation for specific details of the error)"
    Standard_403_Error_Response:
      description: Forbidden User cannot edit this resource
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "403XXX"
            title: "Forbidden Action"
            detail: "This is forbidden to access. Make sure you access the right resource. (Refer the error documentation for specific details of the error)"
    Standard_404_Error_Response:
      description: The specified resource was not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "404XXX"
            title: "Resource not found"
            detail: "This is not a valid resource. Please try something else. (Refer the error documentation for specific details of the error)"
    Standard_500_Error_Response:
      description: Internal Server Error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "500XXX"
            title: "Internal Server Error"
            detail: "Something went wrong, Please try again later. (Refer the error documentation for specific details of the error)"
  parameters:
    Limit:
      description: >
        Specifies the number of agents to be fetched in one paginated request. Response may have an X-Next-Page URL parameter in the header to fetch the next page or set of objects.

      in: query
      name: limit
      required: false
      schema:
        type: integer
        default: 10
        minimum: 1
        maximum: 100
    Skip:
      description: >
        Specifies the number of agents to be skipped in one request. Use this field, together with the Limit parameter, to fetch paginated requests. This is automatically set in the X-Next-Page response header if present.

      in: query
      name: skip
      required: false
      schema:
        type: integer
        default: 0
        minimum: 0
    AgentId:
      description: The Alation Agent identifier.
      in: path
      name: agent_id
      required: true
      schema:
        type: integer
