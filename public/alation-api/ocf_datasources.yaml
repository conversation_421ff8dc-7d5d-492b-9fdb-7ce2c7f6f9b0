info:
  description: PublicAPI for creating and reading OCF datasources
  title: OCF Datasources API
  version: 1.0.0
openapi: 3.0.0
paths:
  /datasource/:
    get:
      description: This API can be used to retrieve multiple existing OCF data sources
        in bulk.
      operationId: getDatasources
      parameters:
      - $ref: '#/components/parameters/include_hidden'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Datasources'
          description: Requested datasources
        400: &id001
          $ref: '#/components/responses/Standard_400_Error_Response'
        401: &id002
          $ref: '#/components/responses/Standard_401_Error_Response'
        403: &id003
          $ref: '#/components/responses/Standard_403_Error_Response'
        404: &id004
          $ref: '#/components/responses/Standard_404_Error_Response'
        500: &id005
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Get a list of datasources
      tags:
      - datasource
    post:
      description: This API can be used to create a new OCF datasource.
      operationId: postDatasource
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Datasource'
        required: true
      responses:
        201:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Datasource'
          description: Datasource created successfully
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: Create a datasource
      tags:
      - datasource
  /datasource/{datasource_id}/:
    delete:
      description: This API can be used to delete an OCF datasource.
      operationId: deleteDatasourceById
      parameters:
      - $ref: '#/components/parameters/datasource_id'
      responses:
        200:
          description: Datasource successfully deleted
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: Delete a datasource
      tags:
      - datasource
    get:
      description: This API can be used to retrieve a specific OCF data source.
      operationId: getDatasourceById
      parameters:
      - $ref: '#/components/parameters/datasource_id'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Datasource'
          description: Requested datasource
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: Get a datasource
      tags:
      - datasource
    put:
      description: This API can be used to update an existing OCF datasource.
      operationId: putDatasourceById
      parameters:
      - $ref: '#/components/parameters/datasource_id'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Datasource_Update_Payload'
        required: true
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Datasource'
          description: Datasource updated successfully
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: Update an OCF datasource
      tags:
      - datasource
  /datasource/{datasource_id}/available_schemas/:
    get:
      description: This API can be used to return the list of available schemas. Use
        the refresh_available_schemas endpoint first if you want to sync schemas from
        datasource to Alation.
      operationId: getAvailableSchemas
      parameters:
      - $ref: '#/components/parameters/datasource_id'
      - $ref: '#/components/parameters/Limit'
      - $ref: '#/components/parameters/Page'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Schemas'
          description: List of available schemas
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: Get a list of available schemas
      tags:
      - datasource metadata extraction
  /datasource/{datasource_id}/configuration_check/:
    get:
      description: This API can be used to check various configuration of the datasource.
        Checks the network connections status, service account authentication and
        service account privileges.
      operationId: getAllConfigChecksById
      parameters:
      - $ref: '#/components/parameters/datasource_id'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status'
          description: All configs have been verified on the datasource.
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: Verify configuration checks
      tags:
      - datasource config checks
  /datasource/{datasource_id}/configuration_check/network_connection/:
    get:
      description: This API can be used to check the network connection status of
        the datasource.
      operationId: getNetworkConnectionStatusById
      parameters:
      - $ref: '#/components/parameters/datasource_id'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status'
          description: Network connection is established
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: Verify network connection status
      tags:
      - datasource config checks
  /datasource/{datasource_id}/configuration_check/service_account_authentication/:
    get:
      description: This API can be used to check the service account authentication
        status of the datasource.
      operationId: getServiceAccountAuthById
      parameters:
      - $ref: '#/components/parameters/datasource_id'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status'
          description: Service account has valid authentication status on the datasource.
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: Verify service account authentication status
      tags:
      - datasource config checks
  /datasource/{datasource_id}/configuration_check/service_account_privileges/:
    get:
      description: This API can be used to check the service account privileges of
        the datasource.
      operationId: getServiceAccountPrivilegesById
      parameters:
      - $ref: '#/components/parameters/datasource_id'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status'
          description: Service account has valid privileges on the datasource.
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: Verify service account privileges
      tags:
      - datasource config checks
  /datasource/{datasource_id}/metadata_extraction_job/:
    post:
      description: This API allows admins to trigger metadata extraction with or without
        parameters. It only works for datasources which are deployed. If parameters
        are passed then it overrides default parameters. Ensure that you run `available_schemas`
        API when you run this API only for the first time.
      operationId: postMDEJob
      parameters:
      - $ref: '#/components/parameters/datasource_id'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MDE_Extraction_Job'
        required: false
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JobID'
          description: MDE triggered successfully
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: Trigger metadata extraction job
      tags:
      - datasource metadata extraction
  /datasource/{datasource_id}/refresh_available_schemas/:
    post:
      description: This endpoint triggers a job that syncs the available schemas from
        the data source to Alation. The syncing process may take some time. This endpoint
        returns a job ID that you can use with the Jobs API to check the status of
        the sync. When the job finishes, then you can use the available_schemas endpoint
        to fetch the list of schemas.
      operationId: postAvailableSchemas
      parameters:
      - $ref: '#/components/parameters/datasource_id'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JobID'
          description: Sync Schema Job triggered successfully
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: Trigger job to sync schemas from data source
      tags:
      - datasource metadata extraction
  /datasource/{datasource_id}/sync_configuration/metadata_extraction/:
    get:
      description: This API can be used to retrieve the existing configurations for
        metadata extraction.
      operationId: getMDEConfigs
      parameters:
      - $ref: '#/components/parameters/datasource_id'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MDE_Configuration'
          description: Requested configuration of Metadata extraction
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: Get metadata extraction configurations
      tags:
      - datasource metadata extraction
    patch:
      description: This API can be used to fully/partially update the existing configurations
        for metadata extraction.
      operationId: patchMDEConfigs
      parameters:
      - $ref: '#/components/parameters/datasource_id'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MDE_Configuration_Patch_Payload'
        required: false
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MDE_Configuration'
          description: MDE configs updated successfully
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: Update metadata extraction configurations
      tags:
      - datasource metadata extraction
security:
- ApiKeyAuth: []
servers:
- url: '{protocol}://{base-url}/integration/v2/'
  variables:
    base-url:
      default: base-url
      description: Django BASE_URL setting for this instance.
    protocol:
      default: http
      enum:
      - http
      - https
tags:
- description: These APIs can be used to perform create, read, update and delete operations
    on datasource objects
  name: datasource
- description: These APIs can be used to get available schemas, trigger metadata extraction
    and get or update metadata extraction configurations
  name: datasource metadata extraction


components:
    securitySchemes:
        ApiKeyAuth:
            type: apiKey
            in: header
            name: TOKEN
            description: API Key for the user

    schemas:
        Datasource:
            description: Properties of a datasource Object
            type: object
            properties:
                uri:
                    type: string
                    description: Jdbc uri for connecting to datasources. Please ensure you either give host,
                        port or just uri parameter. Please do not enter both the parameters.
                    example: mysql://<hostname>:<port>/<db_name>
                connector_id:
                    type: integer
                    description: The OCF conector's id that we want to create datasource with.
                    example: 101
                db_username:
                    type: string
                    description: >
                        The service account username.
                    example: alation
                db_password:
                    type: string
                    description: The service account password.
                    writeOnly: true
                    example: password
                title:
                    type: string
                    description: >
                        The title of the datasource.
                    example: test_mysql
                description:
                    type: string
                    description: The description of the datasource.
                    example: Sample mysql datasource setup
                private:
                    type: boolean
                    description: Boolean flag determining if the datasource is private. Defaults to False.
                    default: false
                is_hidden:
                    type: boolean
                    description: Boolean flag determining if the datasource is hidden. Defaults to False.
                    default: false
                id:
                    type: integer
                    readOnly: true
                    description: Unique identifier of the datasource. INTERNAL
                supports_explain:
                    type: boolean
                    readOnly: true
                    description: INTERNAL
                data_upload_disabled_message:
                    type: string
                    readOnly: true
                    description: INTERNAL
                is_gone:
                    type: boolean
                    readOnly: true
                    description: INTERNAL
                supports_qli_diagnostics:
                    type: boolean
                    readOnly: true
                    description: INTERNAL
                latest_extraction_time:
                    type: string
                    format: date-time
                    description: INTERNAL
                    readOnly: true
                negative_filter_words:
                    type: array
                    description: INTERNAL
                    items:
                        type: string
                    nullable: true
                    readOnly: true
                can_data_upload:
                    type: boolean
                    readOnly: true
                    description: INTERNAL
                qualified_name:
                    type: string
                    readOnly: true
                    description: INTERNAL
                all_schemas:
                    type: string
                    description: INTERNAL
                    nullable: true
                    readOnly: true
                has_previewable_qli:
                    type: boolean
                    readOnly: true
                    description: INTERNAL
                supports_qli_daterange:
                    type: boolean
                    readOnly: true
                    description: INTERNAL
                latest_extraction_successful:
                    type: boolean
                    readOnly: true
                    description: INTERNAL
                owner_ids:
                    type: array
                    description: INTERNAL
                    items:
                        type: integer
                    readOnly: true
                favorited_by_list:
                    type: boolean
                    readOnly: true
                    description: INTERNAL
                supports_compose:
                    type: boolean
                    readOnly: true
                    description: INTERNAL
                enable_designated_credential:
                    type: boolean
                    readOnly: true
                    description: INTERNAL
                deleted:
                    type: boolean
                    readOnly: true
                    description: INTERNAL
                limit_schemas:
                    type: string
                    description: "Schemas to limit in MDE"
                    nullable: true
                    readOnly: true
                obfuscate_literals:
                    type: array
                    description: INTERNAL
                    items:
                        type: string
                    nullable: true
                    readOnly: true
                remove_filtered_schemas:
                    type: boolean
                    readOnly: true
                    description: INTERNAL
                profiling_tip:
                    type: string
                    readOnly: true
                    description: INTERNAL
                    nullable: true
                supports_profiling:
                    type: boolean
                    readOnly: true
                    description: INTERNAL
                icon:
                    type: string
                    readOnly: true
                    description: INTERNAL
                url:
                    type: string
                    readOnly: true
                    description: INTERNAL
                otype:
                    type: string
                    readOnly: true
                    description: INTERNAL
                exclude_schemas:
                    type: string
                    description: "Schemas to exclude in MDE"
                    nullable: true
                    readOnly: true
                exclude_additional_columns_in_qli:
                    type: boolean
                    readOnly: true
                    description: INTERNAL
                can_toggle_ds_privacy:
                    type: boolean
                    readOnly: true
                    description: INTERNAL
                supports_md_diagnostics:
                    type: boolean
                    readOnly: true
                    description: INTERNAL
                supports_ocf_query_service_api:
                  type: boolean
                  readOnly: true
                  description: INTERNAL
                uses_ocf_agent:
                  type: boolean
                  readOnly: true
                  description: INTERNAL
                nosql_mde_sample_size:
                    type: integer
                    readOnly: true
                    description: INTERNAL
                disable_auto_extraction:
                    type: boolean
                    readOnly: true
                    description: INTERNAL
                unresolved_mention_fingerprint_method:
                    type: string
                    readOnly: true
                    description: INTERNAL
                enable_default_schema_extraction:
                    type: boolean
                    readOnly: true
                    description: INTERNAL
                enabled_in_compose:
                    type: boolean
                    readOnly: true
                    description: INTERNAL
                builtin_datasource:
                    type: string
                    readOnly: true
                    description: INTERNAL
                cron_extraction:
                    type: string
                    readOnly: true
                    description: INTERNAL
                supports_default_schema_extraction:
                    type: boolean
                    readOnly: true
                    description: INTERNAL

            required:
            - db_username
            - uri
            - title
            - connector_id

        Datasources:
            type: array
            items:
                $ref: "#/components/schemas/Datasource"

        Datasource_Update_Payload:
            description: Properties of Datasource Update Request payload.
            type: object
            properties:
                uri:
                    type: string
                    description: >
                        The uri of the data source.
                        example: mysql://<hostname>:<port>/<db_name>
                db_username:
                    type: string
                    description: >
                        The service account username. 
                    example: alation
                db_password:
                    type: string
                    description: The service account password.
                    writeOnly: true
                title:
                    type: string
                    description: >
                        The title of the datasource.
                    example: test_mysql
                description:
                    type: string
                    description: The description of the datasource.
                    example: Sample mysql datasource setup
                private:
                    type: boolean
                    description: Boolean flag determining if the datasource is private. Defaults to False.
                    default: false
                compose_default_uri:
                    type: string
                    description: >
                        Used to update Default Connection uri in Compose for a deployed(created) Data Source.
                    example: mysql://<new_hostname>:<new_port>/<new_db_name>

        Status:
            description: Returns status of configuration check
            type: object
            properties:
                status:
                    type: string
                    description: Status of the configuration check

        Schemas:
            description: Returns list of schemas in a datasource
            type: array
            items:
                type: string

        MDE_Configuration:
            type: object
            description: Metadata extraction configuration object
            properties:
                cron_extraction:
                    type: string
                    description: >
                        The extraction schedule in crontab format (minute, hour, day of month, month of year,
                        day of week) example: "0 4 4 * 2".
                disable_auto_extraction:
                    type: boolean
                    description: >
                        True if the extraction schedule should not be executed, false to run extraction
                        according to cron_extraction.
                schemas:
                    type: array
                    items:
                        type: string
                    description: >
                        Schemas to include/exclude in metadata extraction. If there are no schemas to include, then set
                        to an empty array.
                filter_mode:
                    type: boolean
                    description: >
                        Determines the filter type, If set to False(0) then EXCLUSIVE mode else INCLUSIVE mode.
                remove_filtered_schemas:
                    type: boolean
                    description: Boolean flag determining if we need to remove existing schemas. If set to
                        `True`, we mark existing schemas as deleted which are not present in metadata extraction
                        process. Defaults to `False`.
                    default: False
                filtered:
                    type: boolean
                    description: Denotes whether selective extraction toggle switch is turned on or off.
                    readOnly: true

        MDE_Configuration_Patch_Payload:
            description: Metadata extraction configuration object for Patch payload
            allOf:
                - $ref: "#/components/schemas/MDE_Configuration"

        MDE_Extraction_Job:
            type: object
            description: Metadata extraction job object
            properties:
                limit_schemas:
                    type: array
                    items:
                        type: string
                    description: >
                        Schemas to include in metadata extraction. If there are no schemas to include, then set
                        to an empty array. Both parameters - `limit_schemas` and `exclude_schemas` - cannot
                        have values filled at the same time.
                exclude_schemas:
                    type: array
                    items:
                        type: string
                    description: >
                        Schemas to exclude from metadata extraction. If there are no schemas to exclude,
                        then set to an empty array. Both parameters - `exclude_schemas` and `limit_schemas` -
                        cannot have values filled at the same time.
                remove_filtered_schemas:
                    type: boolean
                    description: Boolean flag determining if we need to remove existing schemas. If set to
                        `True`, we mark existing schemas as deleted which are not present in metadata extraction
                        process. Defaults to `False`.
                    default: False
            required:
                - limit_schemas
                - exclude_schemas
                - remove_filtered_schemas

        JobID:
            description: Properties of a Job Object
            type: object
            properties:
                job_id:
                    type: integer
                    description: >
                        GET /api/v2/bulk_metadata/job/?id={job_id} will return the status of the underlying
                        POST job

        Error:
            type: object
            description: Properties of a Error Object
            properties:
                status:
                    type: string
                    description: The HTTP status code
                title:
                    type: string
                    description: The title of the error message
                details:
                    type: string
                    description: More information about the error
            required:
                - status
                - title
                - details

    responses:
        Standard_400_Error_Response:
            description: Malformed Request
            content:
                application/json:
                    schema:
                        $ref: '#/components/schemas/Error'
                    example:
                        code: "400XXX"
                        title: "Malformed Request"
                        detail: "The request sent by user is in the wrong format.
                        (Refer the error documentation for specific details of the error)"

        Standard_401_Error_Response:
            description: Unauthorized bad/missing token
            content:
                application/json:
                    schema:
                        $ref: '#/components/schemas/Error'
                    example:
                        code: "401XXX"
                        title: "Unauthorized"
                        detail: "You are unauthorized to access this resource. Please obtain valid credentials.
                        (Refer the error documentation for specific details of the error)"
        Standard_403_Error_Response:
            description: Forbidden User cannot edit this resource
            content:
                application/json:
                    schema:
                        $ref: '#/components/schemas/Error'
                    example:
                        code: "403XXX"
                        title: "Forbidden Action"
                        detail: "This is forbidden to access. Make sure you access the right resource.
                        (Refer the error documentation for specific details of the error)"
        Standard_404_Error_Response:
            description: The specified resource was not found
            content:
                application/json:
                    schema:
                        $ref: '#/components/schemas/Error'
                    example:
                        code: "404XXX"
                        title: "Resource not found"
                        detail: "This is not a valid resource. Please try something else.
                        (Refer the error documentation for specific details of the error)"
        Standard_500_Error_Response:
            description: Internal Server Error
            content:
                application/json:
                    schema:
                        $ref: '#/components/schemas/Error'
                    example:
                        code: "500XXX"
                        title: "Internal Server Error"
                        detail: "Something went wrong, Please try again later.
                        (Refer the error documentation for specific details of the error)"

    parameters:
        include_hidden:
            name: include_hidden
            description: >
                Specifies if hidden datasources should be included in retrieved list. Hidden data sources
                are not visible in the UI. These are the data sources created via the API with the
                `is_hidden` property set to `True`. There is no UI for the Settings page of such sources
                and they can only be accessed through the API.
            in: query
            schema:
                type: boolean
            required: false

        datasource_id:
            name: datasource_id
            description: "`id` of the datasource"
            in: path
            schema:
                type: integer
            required: true
            examples:
                id:
                    value: 10
                    summary: A sample integer datasource id

        Limit:
            description: >
                Specifies the number of objects to be fetched in one paginated request.
            in: query
            name: limit
            required: false
            schema:
                type: integer
                default: 1000

        Page:
            description: >
                Specifies the page number to fetch a particular page in paginated fashion.
            in: query
            name: page
            required: false
            schema:
                type: integer
                default: 1
