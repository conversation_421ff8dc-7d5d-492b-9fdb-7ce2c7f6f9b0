info:
  description: PublicAPI for creating, reading, updating and deleting articles
  title: Articles API
  version: 0.1.0-beta
openapi: 3.0.0
paths:
  /article/:
    get:
      description: This API is used to list all the active articles, deleted articles
        are ignored. Users can see all the public articles and their respective private
        articles.
      operationId: getArticles
      parameters:
      - $ref: '#/components/parameters/title'
      - $ref: '#/components/parameters/title_icontains'
      - $ref: '#/components/parameters/has_children'
      - $ref: '#/components/parameters/custom_field_templates'
      - $ref: '#/components/parameters/values'
      - $ref: '#/components/parameters/otype'
      - $ref: '#/components/parameters/oid'
      - $ref: '#/components/parameters/include_shared'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Articles'
          description: Requested articles
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: GET all articles
      tags:
      - Article
    post:
      description: This API lets you add a new article to the alation catalog, along
        with a list of custom-templates and children.
      operationId: postArticle
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Article_Request_Body'
        required: true
      responses:
        201:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Article'
          description: Confirmation that article has been created.
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: POST an article
      tags:
      - Article
  /article/{article_id}/:
    delete:
      description: 'This API is used to delete an existing article. NOTE: You can
        delete an article only if you are an admin-user or you are the author of that
        article.'
      operationId: deleteArticleById
      parameters:
      - $ref: '#/components/parameters/ArticleId'
      responses:
        204:
          description: Confirmation that an article has been deleted.
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: DELETE an article
      tags:
      - Article
    get:
      description: This API fetches you an article.
      operationId: getArticleById
      parameters:
      - $ref: '#/components/parameters/ArticleId'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Article'
          description: Confirmation that an article has been fetched.
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: GET an article
      tags:
      - Article
    put:
      description: 'This API is used to update title, body, private, custom_templates
        and children of articles that are active. Deleted articles cannot be updated.
        NOTE: Children can only be added and not removed, as of now.'
      operationId: putArticleById
      parameters:
      - $ref: '#/components/parameters/ArticleId'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Article_Request_Body'
        required: true
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Article'
          description: Confirmation that an article has been updated.
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: UPDATE an article
      tags:
      - Article
  /article/{article_id}/resurrect/:
    post:
      description: 'This API is used to restore a deleted article. NOTE: You can resurrect
        an article only if you are an admin-user or you are the author of that article.'
      operationId: resurrectArticleById
      parameters:
      - $ref: '#/components/parameters/ArticleId'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Article'
          description: Confirmation that an article has been resurrected.
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Resurrect an article
      tags:
      - Article
security:
- ApiKeyAuth: []
servers:
- url: '{protocol}://{base-url}/integration/v1'
  variables:
    base-url:
      default: localhost
      description: Django BASE_URL setting for this instance.
    protocol:
      default: http
      enum:
      - http
      - https
tags:
- description: The article object. Provides access to creating, updating                 and
    reading articles
  name: Article


components:
  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: TOKEN
      description: API Key for the user

  schemas:
    Article:
      type: object
      properties:
        id:
          type: integer
          description: Unique identifier of the article
          readOnly: true
        private:
          type: boolean
          description: Boolean to indicate if the article is private
        title:
          type: string
          description: Title of the article
        ts_updated:
          type: string
          description: Timestamp when the article is last updated
          format: date-time
        ts_created:
          type: string
          description: Timestamp when the article is created
          format: date-time
        url:
          type: string
          readOnly: true
          description: The url the article
        body:
          type: string
          description: The html content of the article page.
        children:
          type: array
          items:
            $ref : "#/components/schemas/Article"
          description: Boolean to indicate if article has children
        custom_templates:
          type: object
          properties:
            id:
              type: integer
              description: Custom templates id.
            title:
              type: string
              description: Custom templates title.
          description: Custom templates associated with the article.
        has_children:
          type: boolean
          description: Boolean value indicating whether article has pages or not.
        attachments:
          type: array
          items:
            type: object
          readOnly: true
          description: Attachments associated with the article
        author:
          allOf:
            - $ref: "#/components/schemas/ArticleUser"
            - type: object
              readOnly: true
              description: The author of the article
        custom_fields:
          type: array
          items:
            type: object
          readOnly: true
          description: Custom fields associated with the article
        editors:
          type: array
          items:
            $ref : "#/components/schemas/ArticleUser"
          readOnly: true
          description: Editors associated with the article

    Articles:
      type: array
      items:
        $ref: "#/components/schemas/Article"

    ArticleUser:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
          description: The auto-generated id of the user
        display_name:
          type: string
          description: The user-friendly display name of the user
        email:
          type: string
          description: The email of the user
        username:
          type: string
          description: The username of the user
        url:
          type: string
          description: The url to access more information of the user

    Article_Request_Body:
      type: object
      properties:
        title:
          type: string
          description: title of the article
        body:
          type: string
          description: Body of the article
        private:
          type: boolean
          description: Is the article private? (By default, it is public)
        custom_templates:
          type: array
          items:
            type: integer
          description: >
            List of custom-template ID's, which can be obtained from CustomTemplates API call.
        children:
          type: object
          description: List of children (can be article or table)
      required:
        - title
        - body

    Error:
      type: object
      description: Properties of a Error Object
      properties:
        status:
          type: string
          description: The HTTP status code
        title:
          type: string
          description: The title of the error message
        details:
          type: string
          description: More information about the error
      required:
        - status
        - title
        - details

  responses:
    Standard_400_Error_Response:
      description: Malformed Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            status: 400
            title: "Malformed Request"
            detail: "The request sent by user is in the wrong format."

    Standard_401_Error_Response:
      description: Unauthorized bad/missing token
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            status: 401
            title: "Unauthorized"
            detail: "You are unauthorized to access this resource. Please obtain valid credentials
            and try again."
    Standard_403_Error_Response:
      description: Forbidden User cannot edit this resource
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            status: 403
            title: "Forbidden Action"
            detail: "This is forbidden to access. Make sure you access the right resource."
    Standard_404_Error_Response:
      description: The specified resource was not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            status: 404
            title: "Resource not found"
            detail: "This is not a valid resource. Please try something else."
    Standard_500_Error_Response:
      description: Internal Server Error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            status: 500
            title: "Internal Server Error"
            detail: "Something went wrong, Please try again later."

  parameters:
    ArticleId:
      description: "ID of the article"
      in: path
      name: article_id
      required: true
      schema:
        type: integer
      examples:
        ids:
          value: 1001
          summary: A sample integer server id

    title:
      description: "Filter articles with this title."
      in: query
      name: title
      required: false
      schema:
        type: string
      examples:
        title:
          value: Article API
          summary: Search keyword for title

    title_icontains:
      description: "Filter articles whose titles contain this word."
      in: query
      name: title_icontains
      required: false
      schema:
        type: string
      examples:
        title:
          value: sales
          summary: Search keyword for title_icontains

    has_children:
      description: "Filter articles with/without children."
      in: query
      name: has_children
      required: false
      schema:
        type: boolean

    custom_field_templates:
      description: "Filter articles with atleast one custom_template belonging to this list."
      in: query
      name: custom_field_templates
      required: false
      schema:
        type: array
        items:
          type: integer
      explode: false
      examples:
        custom_field_templates:
          value: [1,2]
          summary: Custom field template Id array

    values:
      description: "Fields that needs to be returned for each article in the response."
      in: query
      name: values
      required: false
      schema:
        type: array
        items:
          type: string
      explode: false
      examples:
        custom_field_templates:
          value: [id, children, custom_templates]
          summary: Fields that need to be returned for an article

    otype:
      description: "Otype of the object, whose relevant articles are desired."
      in: query
      name: otype
      required: false
      schema:
        type: string

    oid:
      description: "Oid of the object, whose relevant articles are desired."
      in: query
      name: oid
      required: false
      schema:
        type: integer

    include_shared:
      description: "Boolean to include shared articles or not."
      in: query
      name: include_shared
      required: false
      schema:
        type: boolean
