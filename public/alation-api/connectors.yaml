info:
  description: PublicAPIs for displaying installed connectors to the users.
  title: Connector APIs
  version: 1.0.0
openapi: 3.0.0
paths:
  /connectors/:
    get:
      description: This API lets you get a list of installed connectors.
      operationId: getConnectors
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Connectors'
          description: Requested connectors
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Get a list of connectors
      tags:
      - connectors
security:
- ApiKeyAuth: []
servers:
- url: '{protocol}://{base-url}/integration/v2'
  variables:
    base-url:
      default: localhost
      description: Django BASE_URL setting for this instance.
    protocol:
      default: http
      enum:
      - http
      - https
tags:
- description: Access the details related to the installed OCF connectors.
  name: connectors


components:
    securitySchemes:
        ApiKeyAuth:
            type: apiKey
            in: header
            name: TOKEN
            description: API Key for the user

    schemas:
        Connector:
            type: object
            properties:
                id:
                    type: integer
                    description: Connector id associated with connector when it is installed.
                    example: 100
                name:
                    type: string
                    description: Name of the connector installed.
                    example: oracle
                uses_agent:
                    type: boolean
                    description: Denotes whether the connector uses OCF agent.
                    example: true
                connector_version:
                    type: string
                    description: The version of connector installed.
                    example: 1.2.3

        Connectors:
            type: array
            items:
                 $ref: "#/components/schemas/Connector"

        Error:
            type: object
            description: Properties of a Error Object
            properties:
                errors:
                    type: object
                    description: Explicit information about the error message
                code:
                    type: string
                    description: >
                        A six digit code that identifies the problem. Refer the error documentation
                        for more information.
                detail:
                    type: string
                    description: Details about the error message
                title:
                    type: string
                    description: The title of the error message


    responses:
        Standard_400_Error_Response:
            description: Malformed Request
            content:
                application/json:
                    schema:
                        $ref: '#/components/schemas/Error'
                    example:
                        code: "400XXX"
                        title: "Malformed Request"
                        detail: "The request sent by user is in the wrong format.
                        (Refer the error documentation for specific details of the error)"

        Standard_401_Error_Response:
            description: Unauthorized bad/missing token
            content:
                application/json:
                    schema:
                        $ref: '#/components/schemas/Error'
                    example:
                        code: "401XXX"
                        title: "Unauthorized"
                        detail: "You are unauthorized to access this resource. Please obtain valid credentials.
                        (Refer the error documentation for specific details of the error)"
        Standard_403_Error_Response:
            description: Forbidden User cannot edit this resource
            content:
                application/json:
                    schema:
                        $ref: '#/components/schemas/Error'
                    example:
                        code: "403XXX"
                        title: "Forbidden Action"
                        detail: "This is forbidden to access. Make sure you access the right resource.
                        (Refer the error documentation for specific details of the error)"
        Standard_404_Error_Response:
            description: The specified resource was not found
            content:
                application/json:
                    schema:
                        $ref: '#/components/schemas/Error'
                    example:
                        code: "404XXX"
                        title: "Resource not found"
                        detail: "This is not a valid resource. Please try something else.
                        (Refer the error documentation for specific details of the error)"
        Standard_500_Error_Response:
            description: Internal Server Error
            content:
                application/json:
                    schema:
                        $ref: '#/components/schemas/Error'
                    example:
                        code: "500XXX"
                        title: "Internal Server Error"
                        detail: "Something went wrong, Please try again later.
                        (Refer the error documentation for specific details of the error)"

