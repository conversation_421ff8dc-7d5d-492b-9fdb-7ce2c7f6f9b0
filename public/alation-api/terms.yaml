info:
  description: Public API for creating Terms in bulk
  title: Term API
  version: 1.0.0
openapi: 3.0.0
paths:
  /term/:
    delete:
      description: This API is used to delete Terms in bulk.
      operationId: deleteTerms
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Bulk_Delete_Request_Payload'
        required: true
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Delete_Term_Response_Body'
          description: Returns the number of Terms deleted, and an array of their
            IDs.
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Deletes the requested Term(s).
      tags:
      - Bulk
    get:
      description: This API is used to retrieve Terms from the server, along with
        details including title, description, and ID. Add the `id` parameter to only
        retrieve details of a specific Term. You can only retrieve Terms for which
        you have view or edit permissions.
      operationId: getTerms
      parameters:
      - $ref: '#/components/parameters/id'
      - $ref: '#/components/parameters/limit'
      - $ref: '#/components/parameters/skip'
      - $ref: '#/components/parameters/search'
      - $ref: '#/components/parameters/deleted'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Get_Terms_Response_Body'
          description: Requested Terms
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Retrieve a list of Terms
      tags:
      - Bulk
    post:
      description: This API can be used to create Terms in bulk or individually.  Terms
        must include a title.
      operationId: postTerms
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Create_Term_Request_Payload'
        required: true
      responses:
        202:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Create_Term_Response_Body'
          description: Details for the created Terms.
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Create Terms in bulk.
      tags:
      - Bulk
    put:
      description: This API can be used to update Terms in bulk.
      operationId: updateTerms
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Update_Term_Request_Payload'
        required: true
      responses:
        202:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Update_Term_Response_Body'
          description: Details for the updated Terms.
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Update Terms in bulk.
      tags:
      - Bulk
security:
- ApiKeyAuth: []
servers:
- url: '{protocol}://{base-url}/integration/v2'
  variables:
    base-url:
      default: localhost
      description: Django BASE_URL setting for this instance.
    protocol:
      default: http
      enum:
      - http
      - https
tags:
- description: API endpoints supporting bulk operations on Terms.  Terms are a documentation
    type that provide a means for documenting business and technical definitions and
    linking them to data.
  name: Bulk


components:
  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: TOKEN
      description: API Key for the user

  schemas:
    Term:
      description: Properties of a Term object
      type: object
      properties:
        id:
          type: integer
          readOnly: true
          description: The ID of the Term object
        title:
          type: string
          description: The title of the Term
        description:
          type: string
          description: The description of the Term
        template_id:
          type: integer
          description: The ID of the custom template assigned to the Term
        glossary_ids:
          type: array
          description: An array containing the glossary IDs that the Term is a member of
          items:
            type: integer
        custom_fields:
          type: array
          description: An array of objects containing custom field information relative to the custom template ID
          items:
            type: object
            properties:
              field_id:
                type: integer
              value:
                oneOf:
                  - $ref: "#/components/schemas/TextField"
                  - $ref: "#/components/schemas/RichTextField"
                  - $ref: "#/components/schemas/DateField"
                  - $ref: "#/components/schemas/PickerField"
                  - $ref: "#/components/schemas/MultiPickerField"
                  - $ref: "#/components/schemas/ObjectSetField"
                  - $ref: "#/components/schemas/PeopleSetField"
                  - $ref: "#/components/schemas/ReferenceField"
      required:
        - title

    UpdateTerm:
      description: Properties to update on existing Term object
      type: object
      properties:
        id:
          type: integer
          description: The ID of the Term object
        title:
          type: string
          description: The title of the Term
        description:
          type: string
          description: The description of the Term
        template_id:
          type: integer
          description: The ID of the custom template assigned to the Term
        glossary_ids:
          type: array
          description: An array containing the glossary IDs that the Term is a member of
          items:
            type: integer
        custom_fields:
          type: array
          description: An array of objects containing custom field information relative to the custom template ID
          items:
            type: object
            properties:
              field_id:
                type: integer
              value:
                oneOf:
                  - $ref: "#/components/schemas/TextField"
                  - $ref: "#/components/schemas/RichTextField"
                  - $ref: "#/components/schemas/DateField"
                  - $ref: "#/components/schemas/PickerField"
                  - $ref: "#/components/schemas/MultiPickerField"
                  - $ref: "#/components/schemas/ObjectSetField"
                  - $ref: "#/components/schemas/PeopleSetField"
                  - $ref: "#/components/schemas/ReferenceField"
      required:
        - id

    Get_Terms_Response_Body:
      type: object
      properties:
        id:
          type: integer
          description: Term object ID
          example: 1
        deleted:
          type: boolean
          description: Determines if a Term is deleted
          example: False
        ts_deleted:
          type: string
          description: Timestamp when a Term was deleted
          example: "2022-07-05T15:09:40.421916Z"
        is_public:
          type: boolean
          description: Determines if a Term is private or public
          example: True
        ts_created:
          type: string
          description: Timestamp when a Term was created
          example: "2022-07-05T15:09:40.421916Z"
        ts_updated:
          type: string
          description: Timestamp when a Term was updated
          example: "2022-07-05T15:09:40.421916Z"
        otype:
          type: string
          description: Describes the object type
          example: "glossary_term"
        title:
          type: string
          description: Title of the Term object
          example: Sales
        description:
          type: string
          description: Description of the Term object
          example: Relevant data and articles for Sales Analytics

    Create_Term_Request_Payload:
      type: array
      items:
        $ref: "#/components/schemas/Term"
      example: [
      {
        "title": "Fancy Term",
        "description": "Fancy Desc",
        "glossary_ids": [17],
        "template_id": 159,
        "custom_fields": [
          {
            "field_id": 8,
             "value": [
                {
                  "otype": "user",
                  "oid": 1
                }
             ]
          }
        ]
      },
      {
        "title": "Another Fancy Term",
        "description": "Another Fancy Desc"
      }
    ]

    Create_Term_Response_Body:
      type: object
      properties:
        job_id:
          type: integer
          example: {315}

    Update_Term_Request_Payload:
      type: array
      items:
        $ref: "#/components/schemas/UpdateTerm"
      example: [
      {
        "id": 23,
        "description": "Updated Description",
      },
      {
        "id": 68,
        "glossary_ids": [17],
        "template_id": 159,
      }
    ]

    Update_Term_Response_Body:
      type: object
      properties:
        job_id:
          type: integer
          example: {315}

    Bulk_Delete_Request_Payload:
      type: object
      properties:
        id:
          type: array
          items:
            type: integer
          description: List of Term IDs to be deleted.
          example: [22222, 33333]
      required:
        - id
    Delete_Term_Response_Body:
      type: object
      properties:
        deleted_term_ids:
          type: array
          items:
              type: integer
          description: List of Term IDs that were deleted.
          example: [12,14,15]
        deleted_term_count:
          type: integer
          description: Number of Terms deleted
          example: 0
    TextField:
      description: >
        Simple string representation of the custom field value.
      type: string
      example: "sample text"

    RichTextField:
      description: >
          Rich-text compatible string representation of the custom field value.
      type: string
      example: "RTF"

    DateField:
      description: >
          ISO-8601 date-time representation of the custom field value.
      type: string
      format: date-time
      example: "1997-07-16T19:20+01:00"

    PickerField:
      description: >
          String representation of the selected picker value.
      type: string
      example: "Approved"

    MultiPickerField:
      description: >
          An array of strings representing the selected multi-picker values.
      type: array
      items:
          type: string
      example:
        - Approved
        - Rejected
        - Pending

    ObjectSetField:
      description: >
          An array of object keys representing the values of an Object Set Field. Can be used to add
           different objects to an article.
      type: array
      items:
          $ref: "#/components/schemas/ObjectKey"
      example:
      - otype: schema
        oid: 10

    PeopleSetField:
      description: >
          An array of object keys representing the values of a People Set Field. Can be used to
           add stewards or experts.
      type: array
      items:
          $ref: "#/components/schemas/ObjectKey"
      example:
      - otype: user
        oid: 1

    ReferenceField:
      description: >
          An array of object keys representing the values of References Field. Can be used to add
           references to a schema, user, table, article, etc.
      type: array
      items:
          $ref: "#/components/schemas/ObjectKey"
      example:
      - otype: article
        oid: 10

    ObjectKey:
      type: object
      properties:
        otype:
          $ref: "#/components/schemas/otype"
        oid:
          $ref: "#/components/schemas/oid"

    otype:
      description: >
          The type of the object that needs to be associated with this Custom Field Value.
      type: string
      example:
          schema

    oid:
      description: >
          The ID of the object that needs to be associated with this Custom Field Value.
      type: string
      example:
          5

    JobID:
      description: Properties of a Job Object
      type: object
      properties:
        job_id:
          type: integer
          description: GET /api/v1/bulk_metadata/job/?id={job_id} will return the status of the underlying POST job

    Error:
      type: object
      description: Properties of an Error Object
      properties:
        status:
          type: string
          description: The HTTP status code
        title:
          type: string
          description: The title of the error message
        details:
          type: string
          description: More information about the error
      required:
        - status
        - title
        - details

  parameters:
    deleted:
      description: Will return only deleted Terms when set to `True`.
      in: query
      name: deleted
      required: false
      schema:
        type: boolean
        default: false
    id:
      name: id
      description: The ID of the Term
      in: query
      required: false
      schema:
        type: integer
      examples:
        id:
          value: 123
          summary: A sample integer Term ID
    limit:
      description: >
        Specifies the number of rules to be fetched in one paginated request. Response may
        have a `X-Next-Page` URL parameter in the header to fetch next set of rules or page
      in: query
      name: limit
      required: false
      schema:
        type: integer
        default: 100
    search:
      description: >
        Filter by Term `title`
      in: query
      name: search
      required: false
      schema:
        type: string
    skip:
      description: >
        Specifies the number of rules to be skipped in one request. Used together with
        `Limit` parameter to fetch paginated requests. This is automatically set in `X-Next-Page`
        response header if present
      in: query
      name: skip
      required: false
      schema:
        type: integer
        default: 0

  responses:
    Standard_400_Error_Response:
      description: Malformed Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            status: 400
            title: "Malformed Request"
            detail: "The request sent by user is in the wrong format."

    Standard_401_Error_Response:
      description: Unauthorized bad/missing token
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            status: 401
            title: "Unauthorized"
            detail: "You are unauthorized to access this resource. Please obtain valid credentials
            and try again."
    Standard_403_Error_Response:
      description: Forbidden User cannot edit this resource
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            status: 403
            title: "Forbidden Action"
            detail: "This is forbidden to access. Make sure you access the right resource."
    Standard_404_Error_Response:
      description: The specified resource was not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            status: 404
            title: "Resource not found"
            detail: "This is not a valid resource. Please try something else."
    Standard_500_Error_Response:
      description: Internal Server Error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            status: 500
            title: "Internal Server Error"
            detail: "Something went wrong. Please try again later."
