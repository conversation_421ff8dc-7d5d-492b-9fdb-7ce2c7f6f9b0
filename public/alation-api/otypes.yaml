info:
  description: PublicAPI for Retrieving Permitted Otypes in Public APIs based on alation
    configs
  title: Otypes API
  version: 1.0.0
openapi: 3.0.0
paths:
  /otype/:
    get:
      description: API to fetch Permitted Otypes in Public APIs based on alation configs.
        These otypes should be able to be passed into APIs as arguments.
      operationId: getOtypes
      parameters: []
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Otypes'
          description: Permitted Otypes
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: GET a response from the otypes API endpoint
      tags:
      - otype
security:
- ApiKeyAuth: []
servers:
- url: '{protocol}://{base-url}/integration/v1'
  variables:
    base-url:
      default: <base-url>
      description: Alation BASE_URL setting. This is the host name for your Alation
        instance.
    protocol:
      default: http
      enum:
      - http
      - https
tags:
- description: Otypes API, returns permitted otypes based on alation configs
  name: otype


components:
  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: TOKEN
      description: API Key for the user

  schemas:
    Otypes:
      description: Permitted Otypes list from Search API
      type: array
      default: []
      items: 
        $ref: "#/components/schemas/Otype"
    
    Otype:
      description: Otype object
      properties:
        name:
          type: string
          description: Permitted otype name based on alation configs
      required:
        - name

    Error:
      type: object
      description: Properties of an Error Object
      properties:
        code:
          type: string
          description: A six digit code that identifies the problem. Refer the error documentation for more information.
        title:
          type: string
          description: The title of the error message
        detail:
          type: string
          description: More information about the error
      required:
        - code
        - title
        - detail

  responses:
    Standard_400_Error_Response:
      description: Malformed Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "400XXX"
            title: "Malformed Request"
            detail: "The request sent by user is in the wrong format. (Refer the error documentation for specific details of the error)"

    Standard_401_Error_Response:
      description: Unauthorized bad/missing token
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "401XXX"
            title: "Unauthorized"
            detail: "You are unauthorized to access this resource. Please obtain valid credentials. (Refer the error documentation for specific details of the error)"
    Standard_403_Error_Response:
      description: Forbidden User cannot edit this resource
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "403XXX"
            title: "Forbidden Action"
            detail: "This is forbidden to access. Make sure you access the right resource. (Refer the error documentation for specific details of the error)"
    Standard_404_Error_Response:
      description: The specified resource was not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "404XXX"
            title: "Resource not found"
            detail: "This is not a valid resource. Please try something else. (Refer the error documentation for specific details of the error)"
    Standard_500_Error_Response:
      description: Internal Server Error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "500XXX"
            title: "Internal Server Error"
            detail: "Something went wrong, Please try again later. (Refer the error documentation for specific details of the error)"
