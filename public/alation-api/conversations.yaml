info:
  description: Public API for working with conversations. Conversations and conversation
    posts can be created, read, replied to, modified, deleted, and searched.
  title: Conversations API
  version: 0.1.0-beta
openapi: 3.0.0
paths:
  /conversations/:
    get:
      description: Gets a list of conversations matching the given filters.
      operationId: get_conversations
      parameters:
      - $ref: '#/components/parameters/subject'
      - $ref: '#/components/parameters/status'
      - $ref: '#/components/parameters/reference_oid'
      - $ref: '#/components/parameters/reference_otype'
      - $ref: '#/components/parameters/author_id'
      - $ref: '#/components/parameters/assignee_id'
      - $ref: '#/components/parameters/liked'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Conversations'
          description: Found matching conversations
        400: &id001
          $ref: '#/components/responses/400_Error'
        403: &id002
          $ref: '#/components/responses/403_Error'
      summary: Get a list of conversations
      tags:
      - Conversations
    post:
      description: Creates a new conversation.
      operationId: create_conversation
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/New_Conversation'
        required: true
      responses:
        201:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Conversation'
          description: Newly created conversation
        400: *id001
        403: *id002
      summary: Create a conversation
      tags:
      - Conversations
  /conversations/{conversation_id}/:
    delete:
      description: Deletes a conversation with the given ID.
      operationId: delete_conversation
      parameters:
      - $ref: '#/components/parameters/conversation_id'
      responses:
        204:
          description: Confirmation that conversation has been deleted
        400: *id001
        403: *id002
        404: &id003
          $ref: '#/components/responses/404_Error'
      summary: Delete a conversation
      tags:
      - Conversations
    get:
      description: Gets a conversation with the given ID.
      operationId: get_conversation
      parameters:
      - $ref: '#/components/parameters/conversation_id'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Conversation'
          description: Conversation object data
        400: *id001
        403: *id002
        404: *id003
      summary: Get a conversation by ID
      tags:
      - Conversations
    patch:
      description: Updates conversation properties, for example the subject or reference.
      operationId: update_conversation
      parameters:
      - $ref: '#/components/parameters/conversation_id'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Conversation_Update'
        required: true
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Conversation'
          description: Updated conversation object data
        400: *id001
        403: *id002
        404: *id003
      summary: Update a conversation
      tags:
      - Conversations
  /conversations/{conversation_id}/posts/:
    get:
      description: Gets a list of conversation posts matching the given filters.
      operationId: get_conversation_posts
      parameters:
      - $ref: '#/components/parameters/conversation_id'
      - $ref: '#/components/parameters/is_deleted'
      - $ref: '#/components/parameters/author_id'
      - $ref: '#/components/parameters/type'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Posts'
          description: Found matching conversation posts
        400: *id001
        403: *id002
      summary: Get a list of conversation posts
      tags:
      - Posts
    post:
      description: Creates a new conversation post.
      operationId: create_conversation_post
      parameters:
      - $ref: '#/components/parameters/conversation_id'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/New_Post'
        required: true
      responses:
        201:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Post'
          description: Newly created conversation post
        400: *id001
        403: *id002
      summary: Create a conversation post
      tags:
      - Posts
  /conversations/{conversation_id}/posts/{post_id}:
    delete:
      description: Deletes a conversation post with the given ID.
      operationId: delete_conversation_post
      parameters:
      - $ref: '#/components/parameters/conversation_id'
      - $ref: '#/components/parameters/post_id'
      responses:
        204:
          description: Conversation post has been deleted
        400: *id001
        403: *id002
        404: *id003
      summary: Delete a conversation post
      tags:
      - Posts
    get:
      description: Gets a conversation post with the given ID.
      operationId: get_conversation_post
      parameters:
      - $ref: '#/components/parameters/conversation_id'
      - $ref: '#/components/parameters/post_id'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Post'
          description: Conversation post object data
        400: *id001
        403: *id002
        404: *id003
      summary: Get a conversation post by ID
      tags:
      - Posts
    patch:
      description: Updates a conversation post's properties, for example the post
        text.
      operationId: update_conversation_post
      parameters:
      - $ref: '#/components/parameters/conversation_id'
      - $ref: '#/components/parameters/post_id'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Post_Update'
        required: true
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Post'
          description: Updated conversation post object data
        400: *id001
        403: *id002
        404: *id003
      summary: Update a conversation post
      tags:
      - Posts
security:
- ApiKeyAuth: []
servers:
- url: '{protocol}://{base-url}/integration/v1'
  variables:
    base-url:
      default: localhost
      description: Django BASE_URL setting for this instance
    protocol:
      default: http
      enum:
      - http
      - https
tags:
- description: This API can be used for working with conversations.
  name: Conversations
- description: This API can be used for working with conversation posts.
  name: Posts


components:
  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: TOKEN
      description: API Key for the user

  schemas:
    Conversation:
      type: object
      properties:
        id:
          type: integer
          description: Unique identifier of the conversation
        subject:
          type: string
          description: Subject of the conversation
        last_updated:
          type: string
          description: Timestamp when the conversation was last updated
          format: date-time
        created:
          type: string
          description: Timestamp when the conversation was created
          format: date-time
        is_deleted:
          type: boolean
          description: Indicates if the conversation is marked as deleted
        url:
          type: string
          description: URL of the conversation
        author:
          allOf:
            - $ref: "#/components/schemas/Author"
            - type: object
              description: Author of the conversation
        participants:
          type: array
          items:
            $ref : "#/components/schemas/Author"
          description: List of participants who have responded to the conversation
        body:
          allOf:
            - $ref: "#/components/schemas/Body"
            - type: object
              description: Initial message of the conversation
        reference:
          allOf:
            - $ref: "#/components/schemas/Reference"
            - type: object
              description: Catalog object the conversation refers to
        status:
          type: string
          description: Indicates if the conversation is open or resolved
          enum:
            - open
            - resolved
        assignee:
          allOf:
            - $ref: "#/components/schemas/Author"
            - type: object
              description: User assigned to the conversation
      example:
        id: 303
        subject: Sample conversation subject
        last_updated: 2022-12-12T13:25:05.172Z
        created: 2022-12-12T13:25:05.172Z
        is_deleted: false
        url: /conversation/1/sample-conversation
        author:
          id: 1
          display_name: Main user
          url: /user/1
          is_active: true
          photo_url: /media/avatars/MainUser.png
        participants:
          - id: 1
            display_name: Main user
            url: /user/1
            is_active: true
            photo_url: /media/avatars/MainUser.png
          - id: 2
            display_name: Second user
            url: /user/2
            is_active: true
            photo_url: /media/avatars/SecondUser.png
        body:
          id: 567
          text: Sample conversation question?
          last_updated: 2022-12-12T13:25:05.172Z
          replies: []
          likes: 0
          liked_by: []
          liked_by_current_user: false
        reference:
          id: 303
          title: Sample article title
          name: Sample article name
          otype: article
          url: /article/1
        status: open
        assignee:
          id: 2
          display_name: Second user
          url: /user/2
          is_active: true
          photo_url: /media/avatars/SecondUser.png

    Reference:
      type: object
      properties:
        id:
          type: integer
          description: Unique identifier of the referenced catalog object
        title:
          type: string
          description: Title of the referenced catalog object
        name:
          type: string
          description: Name of the referenced catalog object
        otype:
          type: string
          description: Object type of the referenced catalog object
        url:
          type: string
          description: URL of the referenced catalog object
      required:
        - id
        - otype
        - url
      example:
        id: 303
        title: Sample article title
        name: Sample article name
        otype: article
        url: /article/1

    Conversation_Update:
      type: object
      properties:
        body:
          type: string
          description: Initial message of the conversation
        status:
          type: string
          description: Indicates if the conversation is open or resolved
          enum:
            - open
            - resolved
        reference_oid:
          type: integer
          description: Unique identifier of the conversation's referenced catalog object
        reference_otype:
          type: string
          description: Object type of the conversation's referenced catalog object
        assignee_id:
          type: integer
          description: Unique identifier of user assigned to the conversation
        liked:
          type: boolean
          description: Indicates if the current API user likes this conversation
      example:
        body: Sample conversation question?
        status: open
        reference_oid: 667
        reference_otype: article
        assignee_id: 102
        liked: true

    Conversation_Info:
      type: object
      properties:
        id:
          type: integer
          description: Unique identifier of the conversation
        subject:
          type: string
          description: Subject of the conversation
        last_updated:
          type: string
          description: Timestamp when the conversation was last updated
          format: date-time
        url:
          type: string
          description: URL of the conversation
        author:
          allOf:
            - $ref: "#/components/schemas/Author"
            - type: object
              description: Author of the conversation
        body:
          allOf:
            - $ref: "#/components/schemas/Body"
            - type: object
              description: Initial message of the conversation
        reference:
          allOf:
            - $ref: "#/components/schemas/Reference"
            - type: object
              description: Catalog object the conversation refers to
        status:
          type: string
          description: Indicates if the conversation is open or resolved
          enum:
            - open
            - resolved
        assignee:
          allOf:
            - $ref: "#/components/schemas/Author"
            - type: object
              description: User assigned to the conversation
      example:
        id: 303
        subject: Sample conversation subject
        last_updated: 2022-12-12T13:25:05.172Z
        url: /conversation/1/sample-conversation
        author:
          id: 1
          display_name: Main user
          url: /user/1
          is_active: true
          photo_url: /media/avatars/MainUser.png
        body:
          id: 567
          text: Sample conversation question?
          last_updated: 2022-12-12T13:25:05.172Z
          likes: 0
          liked_by: []
          liked_by_current_user: false
        reference:
          id: 303
          title: Sample article title
          name: Sample article name
          otype: article
          url: /article/1
        status: open
        assignee:
          id: 2
          display_name: Second user
          url: /user/2
          is_active: true
          photo_url: /media/avatars/SecondUser.png

    Conversations:
      type: array
      items:
        $ref: "#/components/schemas/Conversation_Info"
      example:
        - id: 303
          subject: Sample conversation subject
          last_updated: 2022-12-12T13:25:05.172Z
          url: /conversation/1/sample-conversation-subject
          author:
            id: 1
            display_name: Main user
            url: /user/1
            is_active: true
            photo_url: /media/avatars/MainUser.png
          body:
            id: 567
            text: Sample conversation question?
            last_updated: 2022-12-12T13:25:05.172Z
            likes: 0
            liked_by: []
            liked_by_current_user: false
          reference:
            id: 667
            title: Sample article title
            name: Sample article name
            otype: article
            url: /article/1
          status: open
          assignee:
            id: 2
            display_name: Second user
            url: /user/2
            is_active: true
            photo_url: /media/avatars/SecondUser.png
        - id: 304
          subject: Second conversation subject
          last_updated: 2022-12-12T13:25:05.172Z
          url: /conversation/2/second-conversation-subject
          author:
            id: 1
            display_name: Main user
            url: /user/1
            is_active: true
            photo_url: /media/avatars/MainUser.png
          body:
            id: 568
            text: Second conversation question?
            last_updated: 2022-12-12T13:25:05.172Z
            likes: 0
            liked_by: []
            liked_by_current_user: false
          reference:
            id: 667
            title: Sample article title
            name: Sample article name
            otype: article
            url: /article/1
          status: open
          assignee:

    Author:
      type: object
      properties:
        id:
          type: integer
          description: Unique identifier of the conversation's author
        display_name:
          type: string
          description: User-friendly display name of the conversation's author
        url:
          type: string
          description: URL of the conversation author's user profile
        is_active:
          type: boolean
          description: Indicates if user is still active
        photo_url:
          type: string
          description: URL of the conversation author's user photo/avatar
      example:
        id: 1
        display_name: Main user
        url: /user/1
        is_active: true
        photo_url: /media/avatars/MainUser.png

    New_Conversation:
      type: object
      properties:
        subject:
          type: string
          description: Subject of the conversation
        body:
          type: string
          description: Initial message of the conversation
        reference_oid:
          type: integer
          description: Unique identifier of the conversation's referenced catalog object
        reference_otype:
          type: string
          description: Object type of the conversation's referenced catalog object
        assignee_id:
          type: integer
          description: Unique identifier of user assigned to the conversation
      required:
        - subject
        - body
      example:
        subject: New conversation
        body: Sample conversation question?
        reference_oid: 667
        reference_otype: article
        assignee_id: 102

    Body:
      type: object
      properties:
        id:
          type: integer
          description: Unique identifier of the conversation body
        text:
          type: string
          description: Text of conversation's initial message
        last_updated:
          type: string
          description: Timestamp when the conversation body was last updated
          format: date-time
        replies:
          type: array
          items:
            $ref : "#/components/schemas/Post_Info"
          description: List of replies to the conversation
        likes:
          type: integer
          description: Number of users who have liked this post
        liked_by:
          type: array
          items:
            $ref : "#/components/schemas/Author"
          description: List of users who have liked this post
        liked_by_current_user:
          type: boolean
          description: Indicates if the current API user has liked this post
      required:
        - id
        - text
      example:
        id: 567
        text: Sample conversation question?
        last_updated: 2022-12-12T13:25:05.172Z
        replies:
          - id: 321
            text: Post text
            last_updated: 2022-12-12T13:25:05.172Z
            author:
              id: 1
              display_name: Main user
              url: /user/1
              is_active: true
              photo_url: /media/avatars/MainUser.png
            type: reply
            parent_post_id: 123
            likes: 2
            liked_by_current_user: true
            number_of_threaded_replies: 1
          - id: 322
            text: Second post text
            last_updated: 2022-12-12T13:25:05.172Z
            author:
              id: 1
              display_name: Main user
              url: /user/1
              is_active: true
              photo_url: /media/avatars/MainUser.png
            type: reply
            parent_post_id: 123
            likes: 0
            liked_by_current_user: false
            number_of_threaded_replies: 0
        likes: 1
        liked_by:
          - id: 2
            display_name: Second user
            url: /user/2
            is_active: true
            photo_url: /media/avatars/SecondUser.png
        liked_by_current_user: false

    Post:
      type: object
      properties:
        id:
          type: integer
          description: Unique identifier of the post
        text:
          type: string
          description: Text of the post
        last_updated:
          type: string
          description: Timestamp when the post was last updated
          format: date-time
        created:
          type: string
          description: Timestamp when the post was created
          format: date-time
        author:
          allOf:
            - $ref: "#/components/schemas/Author"
            - type: object
              description: Author of the post
        type:
          type: string
          description: Type of the post. `reply` is a response to the initial message. `threaded reply` is a response to a reply.
          enum:
            - reply
            - threaded_reply
        is_deleted:
          type: boolean
          description: Indicates if the post is marked as deleted
        conversation_id:
          type: integer
          description: Unique identifier of the conversation the post belongs to
        parent_post_id:
          type: integer
          description: Unique identifier of the post's parent (the reply this post is responding to). Only posts of type "threaded_reply" can have a parent, and the parent must be a reply.
        likes:
          type: integer
          description: Number of users who have liked this post
        liked_by:
          type: array
          items:
            $ref : "#/components/schemas/Author"
          description: List of users who have liked this post
        liked_by_current_user:
          type: boolean
          description: Indicates if the current API user has liked this post
        threaded_replies:
          type: array
          items:
            $ref : "#/components/schemas/Post_Info"
          description: List of children to this post. Only posts of type "reply" can have threaded replies (children).
      required:
        - id
        - text
      example:
        id: 321
        text: Post text
        last_updated: 2022-12-12T13:25:05.172Z
        created: 2022-12-12T13:25:05.172Z
        author:
          id: 1
          display_name: Main user
          url: /user/1
          is_active: true
          photo_url: /media/avatars/MainUser.png
        type: reply
        is_deleted: false
        conversation_id: 44
        parent_post_id: 123
        likes: 2
        liked_by:
          - id: 1
            display_name: Main user
            url: /user/1
            is_active: true
            photo_url: /media/avatars/MainUser.png
          - id: 2
            display_name: Second user
            url: /user/2
            is_active: true
            photo_url: /media/avatars/SecondUser.png
        liked_by_current_user: true
        threaded_replies:
          - id: 521
            text: Comment text
            last_updated: 2022-12-12T13:25:05.172Z
            author:
              id: 2
              display_name: Second user
              url: /user/2
              is_active: true
              photo_url: /media/avatars/SecondUser.png
            type: reply
            parent_post_id: 321
            likes: 0
            liked_by_current_user: false
            number_of_threaded_replies: 0
          - id: 522
            text: Second comment text
            last_updated: 2022-12-12T13:25:05.172Z
            author:
              id: 2
              display_name: Second user
              url: /user/2
              is_active: true
              photo_url: /media/avatars/SecondUser.png
            type: reply
            parent_post_id: 321
            likes: 0
            liked_by_current_user: false
            number_of_threaded_replies: 0

    Post_Info:
      type: object
      properties:
        id:
          type: integer
          description: Unique identifier of the post
        text:
          type: string
          description: Text of the post
        last_updated:
          type: string
          description: Timestamp when the post was last updated
          format: date-time
        author:
          allOf:
            - $ref: "#/components/schemas/Author"
            - type: object
              description: Author of the post
        type:
          type: string
          description: Type of the post. `reply` is a response to the initial message. `threaded reply` is a response to a reply.
          enum:
            - reply
            - threaded_reply
        parent_post_id:
          type: integer
          description: Unique identifier of the post's parent (the reply this post is responding to). Only posts of type "threaded_reply" can have a parent, and the parent must be a reply.
        likes:
          type: integer
          description: Number of users who have liked this post
        liked_by_current_user:
          type: boolean
          description: Indicates if the current API user has liked this post
        number_of_threaded_replies:
          type: integer
          description: Number of threaded replies as children under this post. Only posts of type "reply" can have threaded replies (children).
      example:
        id: 321
        text: Post text
        last_updated: 2022-12-12T13:25:05.172Z
        author:
          id: 1
          display_name: Main user
          url: /user/1
          is_active: true
          photo_url: /media/avatars/MainUser.png
        type: reply
        parent_post_id: 123
        likes: 2
        liked_by_current_user: true
        number_of_threaded_replies: 1

    Posts:
      type: array
      items:
        $ref: "#/components/schemas/Post_Info"
      example:
        - id: 321
          text: Post text
          last_updated: 2022-12-12T13:25:05.172Z
          author:
            id: 1
            display_name: Main user
            url: /user/1
            is_active: true
            photo_url: /media/avatars/MainUser.png
          type: reply
          parent_post_id:
          likes: 2
          liked_by_current_user: true
          number_of_threaded_replies: 1
        - id: 322
          text: Post text comment
          last_updated: 2022-12-12T13:25:05.172Z
          author:
            id: 1
            display_name: Main user
            url: /user/1
            is_active: true
            photo_url: /media/avatars/MainUser.png
          type: comment
          parent_post_id: 321
          likes: 0
          liked_by_current_user: false
          number_of_threaded_replies: 0

    New_Post:
      type: object
      properties:
        text:
          type: string
          description: Text of the post
        parent_post_id:
          type: integer
          description: Unique identifier of the post's parent (the reply this post is responding to). Only posts of type "threaded_reply" can have a parent, and the parent must be a reply.
      required:
        - text
      example:
        text: New post text
        parent_post_id: 123

    Post_Update:
      type: object
      properties:
        text:
          type: string
          description: Text of the post
        liked:
          type: boolean
          description: Indicates if the current API user likes this post
      example:
        text: Updated post text
        liked: false

    Error:
      type: object
      description: Properties of an Error Object
      properties:
        code:
          type: string
          description: The extended HTTP status code
        detail:
          type: string
          description: More information about the error
      required:
        - code
        - detail

  responses:
    400_Error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 400000
            detail: "The request sent by user is invalid."
    403_Error:
      description: Forbidden
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 403000
            detail: "User doesn't have rights to access resource."
    404_Error:
      description: Not Found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 404000
            detail: "Requested resource cannot be found."
    500_Error:
      description: Internal Server Error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 500000
            detail: "Something went wrong, Please try again later."

  parameters:
    conversation_id:
      description: Unique identifier of the conversation
      in: path
      name: conversation_id
      required: true
      schema:
        type: integer
      examples:
        conversation_id:
          value: 12
          summary: Conversation with unique ID of 12

    subject:
      description: Conversation subject or part of subject
      in: query
      name: subject
      required: false
      schema:
        type: string
      examples:
        subject:
          value: PII
          summary: Conversations with PII in the subject

    status:
      description: Must be `open` or `resolved`. Only conversations with the specified status will be returned.
      in: query
      name: status
      required: false
      schema:
        type: string
        enum:
          - open
          - resolved
      examples:
        open:
          value: open
          summary: Open conversations
        resolved:
          value: resolved
          summary: Resolved conversations

    reference_otype:
      description: Object type of the catalog object that the conversation refers to
      in: query
      name: reference_otype
      required: false
      schema:
        type: string
      examples:
        article:
          value: article
          summary: Conversations referring to articles
        domain:
          value: domain
          summary: Conversations referring to domains
        policy:
          value: policy
          summary: Conversations referring to policies

    reference_oid:
      description: Unique identifier of the catalog object that the conversation refers to
      in: query
      name: reference_oid
      required: false
      schema:
        type: integer
      examples:
        oid:
          value: 1234
          summary: Conversations referring to object with ID of 1234

    author_id:
      description: Unique identifier of the author
      in: query
      name: author_id
      required: false
      schema:
        type: integer
      examples:
        author_id:
          value: 44
          summary: The author with ID of 44

    assignee_id:
      description: Unique identifier of user assigned to the conversation
      in: query
      name: assignee_id
      required: false
      schema:
        type: integer
      examples:
        assignee_id:
          value: 102
          summary: Conversations assigned to user with ID of 102

    liked:
      description: Indicates if the current API user likes this conversation
      in: query
      name: liked
      required: false
      schema:
        type: boolean
      examples:
        liked:
          value: true
          summary: Indicates that the current API user liked this conversation
        not_liked:
          value: false
          summary: Indicates that the current API user hasn't liked this conversation

    post_id:
      description: Unique identifier of the conversation post (reply or threaded reply)
      in: path
      name: post_id
      required: true
      schema:
        type: integer
      examples:
        post_id:
          value: 44
          summary: Post with ID of 44

    is_deleted:
      description: Indicates if only deleted/not deleted conversation posts should be returned
      in: query
      name: is_deleted
      required: false
      schema:
        type: boolean
      examples:
        deleted:
          value: true
          summary: Posts marked as deleted
        not_deleted:
          value: false
          summary: Posts not marked as deleted

    type:
      description: Type of the post. Reply is a response to the initial message. Threaded reply is a response to a reply.
      in: query
      name: type
      required: false
      schema:
        type: string
        enum:
          - reply
          - threaded_reply
      examples:
        reply:
          value: reply
          summary: Posts of type reply
        threaded_reply:
          value: threaded_reply
          summary: Posts of type threaded_reply
