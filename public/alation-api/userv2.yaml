info:
  description: Public API for reading Users
  title: User Public API
  version: 2.0.0
openapi: 3.0.0
paths:
  /user/:
    get:
      description: This API is used to list multiple Users.
      operationId: getUsers
      parameters:
        - $ref: '#/components/parameters/UserList_display_name'
        - $ref: '#/components/parameters/UserList_display_name__contains'
        - $ref: '#/components/parameters/UserList_display_name__icontains'
        - $ref: '#/components/parameters/UserList_email'
        - $ref: '#/components/parameters/UserList_email__contains'
        - $ref: '#/components/parameters/UserList_email__icontains'
        - $ref: '#/components/parameters/UserList_id'
        - $ref: ./common/parameters.yaml#/components/parameters/Limit
        - $ref: '#/components/parameters/UserList_profile_id'
        - $ref: ./common/parameters.yaml#/components/parameters/Offset
        - $ref: '#/components/parameters/UserList_order_by'
        - $ref: ./common/parameters.yaml#/components/parameters/Values
        - $ref: '#/components/parameters/UserList_last_login'
        - $ref: '#/components/parameters/UserList_last_login__date__lt'
        - $ref: '#/components/parameters/UserList_last_login__date__gt'
        - $ref: '#/components/parameters/UserList_last_login__date__lte'
        - $ref: '#/components/parameters/UserList_last_login__date__gte'
        - $ref: '#/components/parameters/UserList_last_login__lt'
        - $ref: '#/components/parameters/UserList_last_login__gt'
        - $ref: '#/components/parameters/UserList_last_login__lte'
        - $ref: '#/components/parameters/UserList_last_login__gte'
        - $ref: '#/components/parameters/UserList_ts_created'
        - $ref: '#/components/parameters/UserList_ts_created__date__lt'
        - $ref: '#/components/parameters/UserList_ts_created__date__gt'
        - $ref: '#/components/parameters/UserList_ts_created__date__lte'
        - $ref: '#/components/parameters/UserList_ts_created__date__gte'
        - $ref: '#/components/parameters/UserList_ts_created__lt'
        - $ref: '#/components/parameters/UserList_ts_created__gt'
        - $ref: '#/components/parameters/UserList_ts_created__lte'
        - $ref: '#/components/parameters/UserList_ts_created__gte'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserList'
          description: Requested Users
        400:
          $ref: ./common/responses.yaml#/components/responses/Standard_400_Error_Response
        401:
          $ref: ./common/responses.yaml#/components/responses/Standard_401_Error_Response
        403:
          $ref: ./common/responses.yaml#/components/responses/Standard_403_Error_Response
        404:
          $ref: ./common/responses.yaml#/components/responses/Standard_404_Error_Response
        500:
          $ref: ./common/responses.yaml#/components/responses/Standard_500_Error_Response
      summary: GET multiple Users
      tags:
        - User
  /user/{id}/:
    get:
      description: This API fetches an individual User.
      operationId: getUserById
      parameters:
        - $ref: '#/components/parameters/User_id'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: Confirmation that a User has been fetched.
        400:
          $ref: ./common/responses.yaml#/components/responses/Standard_400_Error_Response
        401:
          $ref: ./common/responses.yaml#/components/responses/Standard_401_Error_Response
        403:
          $ref: ./common/responses.yaml#/components/responses/Standard_403_Error_Response
        404:
          $ref: ./common/responses.yaml#/components/responses/Standard_404_Error_Response
        500:
          $ref: ./common/responses.yaml#/components/responses/Standard_500_Error_Response
      summary: GET a User
      tags:
        - User
security:
  - AlationAdminAPIKeyAuth: []
servers:
  - url: '{protocol}://{base-url}/integration/v2'
    variables:
      base-url:
        default: localhost
        description: Alation BASE_URL setting for this instance.
      protocol:
        default: http
        enum:
          - http
          - https
tags:
  - description: The User object. Provides access to reading Users.
    name: User
components:
  schemas:
    display_name:
      description: The User's display name.
      type: string
    email:
      description: The User's email address.
      type: string
    id:
      description: The User's id.
      minimum: 1
      type: integer
    profile_id:
      description: The User's profile id.
      minimum: 1
      type: integer
    url:
      description: The url to the User's profile page.
      type: string
    last_login:
      description: The time at which the User last logged in.
      type: string
    ts_created:
      description: The time at which the User's profile was created.
      type: string
    User:
      type: object
      properties:
        display_name:
          allOf:
            - $ref: '#/components/schemas/display_name'
            - readOnly: true
        email:
          allOf:
            - $ref: '#/components/schemas/email'
            - readOnly: true
        id:
          allOf:
            - $ref: '#/components/schemas/id'
            - readOnly: true
        profile_id:
          allOf:
            - $ref: '#/components/schemas/profile_id'
            - readOnly: true
        url:
          allOf:
            - $ref: '#/components/schemas/url'
            - readOnly: true
        last_login:
          allOf:
            - $ref: '#/components/schemas/last_login'
            - readOnly: true
        ts_created:
          allOf:
            - $ref: '#/components/schemas/ts_created'
            - readOnly: true
    UserList:
      type: array
      items:
        $ref: '#/components/schemas/User'
  parameters:
    User_id:
      description: The User ID to retrieve.
      example: 1
      in: path
      name: id
      required: true
      schema:
        $ref: '#/components/schemas/id'
    UserList_display_name:
      description: Filter by an exact match on display_name
      in: query
      name: display_name
      required: false
      schema:
        $ref: '#/components/schemas/display_name'
    UserList_display_name__contains:
      description: Filter by a case-sensitive substring on display_name
      in: query
      name: display_name__contains
      required: false
      schema:
        $ref: '#/components/schemas/display_name'
    UserList_display_name__icontains:
      description: Filter by a case-insensitive substring on display_name
      in: query
      name: display_name__icontains
      required: false
      schema:
        $ref: '#/components/schemas/display_name'
    UserList_email:
      description: Filter by an exact match on email
      in: query
      name: email
      required: false
      schema:
        $ref: '#/components/schemas/email'
    UserList_email__contains:
      description: Filter by a case-sensitive substring on email
      in: query
      name: email__contains
      required: false
      schema:
        $ref: '#/components/schemas/email'
    UserList_email__icontains:
      description: Filter by a case-insensitive substring on email
      in: query
      name: email__icontains
      required: false
      schema:
        $ref: '#/components/schemas/email'
    UserList_id:
      description: Filter by User ID. Multiple IDs can be specified using the format ?id=1&id=2.
      in: query
      name: id
      required: false
      schema:
        $ref: '#/components/schemas/id'
    UserList_profile_id:
      description: Filter by User Profile ID. Multiple IDs can be specified using the format "?profile_id=1&profile_id=2".
      in: query
      name: profile_id
      required: false
      schema:
        $ref: '#/components/schemas/profile_id'
    UserList_last_login:
      description: >
        Filter by exact User last login time.
        The filter value should be in "YYYY-MM-DD HH:MM[:ss[.uuuuuu]]" format and its timezone will be defaulted to UTC.
        Multiple last login timestamps can be specified using the format "?last_login=2022-12-05 07:40:08.023453&last_login=2022-12-06 07:40:08.023453". 
        When multiple last login timestamps are provided in the filter, all the user profiles whose last login timestamps exactly match would be returned.
        The last login times of the user profiles returned in the API response are in UTC timezone.
      in: query
      name: last_login
      required: false
      schema:
        $ref: '#/components/schemas/last_login'
    UserList_last_login__date__lt:
      description: >
        Filters all the users whose last login date is less than the filter value.
        The filter value should be in "YYYY-MM-DD" format. For example: "?last_login\__date\__lt=2022-11-23".
        Range searches on the last login date can be achieved by combining multiple last login date filters.
        For example: "?last_login\__date\__gt=2022-11-22&last_login\__date\__lt=2022-11-23".
        The last login times of the user profiles returned in the API response are in UTC timezone.
      in: query
      name: last_login__date__lt
      required: false
      schema:
        $ref: '#/components/schemas/last_login'
    UserList_last_login__date__gt:
      description: >
        Filters all the users whose last login date is greater than the filter value.
        The filter value should be in "YYYY-MM-DD" format. For example: "?last_login\__date\__gt=2022-11-23".
        Range searches on the last login date can be achieved by combining multiple last login date filters.
        For example: "?last_login\__date\__gt=2022-11-22&last_login\__date\__lt=2022-11-23".
        The last login times of the user profiles returned in the API response are in UTC timezone.
      in: query
      name: last_login__date__gt
      required: false
      schema:
        $ref: '#/components/schemas/last_login'
    UserList_last_login__date__lte:
      description: >
        Filters all the users whose last login date is less than or equal to the filter value.
        The filter value should be in "YYYY-MM-DD" format. For example: "?last_login\__date\__lte=2022-11-23".
        Range searches on the last login date can be achieved by combining multiple last login date filters.
        For example: "?last_login\__date\__gte=2022-11-22&last_login\__date\__lte=2022-11-23".
        The last login times of the user profiles returned in the API response are in UTC timezone.
      in: query
      name: last_login__date__lte
      required: false
      schema:
        $ref: '#/components/schemas/last_login'
    UserList_last_login__date__gte:
      description: >
        Filters all the users whose last login date is greater than or equal to the filter value.
        The filter value should be in "YYYY-MM-DD" format. For example: "?last_login\__date\__gte=2022-11-23".
        Range searches on the last login date can be achieved by combining multiple last login date filters.
        For example: "?last_login\__date\__gte=2022-11-22&last_login\__date\__lte=2022-11-23".
        The last login times of the user profiles returned in the API response are in UTC timezone.
      in: query
      name: last_login__date__gte
      required: false
      schema:
        $ref: '#/components/schemas/last_login'
    UserList_last_login__lt:
      description: >
        Filters all the users whose last login time is less than the filter value. 
        The filter value should be in "YYYY-MM-DD HH:MM[:ss[.uuuuuu]]" format and its timezone will be defaulted to UTC.
        For example: "?last_login\__lt=2022-11-23 07:40:08.023453".
        Range searches on the last login time can be achieved by combining multiple last login datetime filters.
        For example: "?last_login\__gt=2022-11-22 07:40:08.023453&last_login\__lt=2022-11-23 07:40:08.023453". 
        The last login times of the user profiles returned in the API response are in UTC timezone.
      in: query
      name: last_login__lt
      required: false
      schema:
        $ref: '#/components/schemas/last_login'
    UserList_last_login__gt:
      description: >
        Filters all the users whose last login time is greater than the filter value.
        The filter value should be in "YYYY-MM-DD HH:MM[:ss[.uuuuuu]]" format and its timezone will be defaulted to UTC.
        For example: "?last_login\__gt=2022-11-23 07:40:08.023453".
        Range searches on the last login time can be achieved by combining multiple last login datetime filters.
        For example: "?last_login\__gt=2022-11-22 07:40:08.023453&last_login\__lt=2022-11-23 07:40:08.023453".
        The last login times of the user profiles returned in the API response are in UTC timezone.
      in: query
      name: last_login__gt
      required: false
      schema:
        $ref: '#/components/schemas/last_login'
    UserList_last_login__lte:
      description: >
        Filters all the users whose last login time is less than or equal to the filter value. 
        The filter value should be in "YYYY-MM-DD HH:MM[:ss[.uuuuuu]]" format and its timezone will be defaulted to UTC.
        For example: "?last_login\__lte=2022-11-23 07:40:08.023453".
        Range searches on the last login time can be achieved by combining multiple last login datetime filters.
        For example: "?last_login\__gte=2022-11-22 07:40:08.023453&last_login\__lte=2022-11-23 07:40:08.023453". 
        The last login times of the user profiles returned in the API response are in UTC timezone.
      in: query
      name: last_login__lte
      required: false
      schema:
        $ref: '#/components/schemas/last_login'
    UserList_last_login__gte:
      description: >
        Filters all the users whose last login time is greater than or equal to the filter value.
        The filter value should be in "YYYY-MM-DD HH:MM[:ss[.uuuuuu]]" format and its timezone will be defaulted to UTC.
        For example: "?last_login\__gte=2022-11-23 07:40:08.023453".
        Range searches on the last login time can be achieved by combining multiple last login datetime filters.
        For example: "?last_login\__gte=2022-11-22 07:40:08.023453&last_login\__lte=2022-11-23 07:40:08.023453".
        The last login times of the user profiles returned in the API response are in UTC timezone.
      in: query
      name: last_login__gte
      required: false
      schema:
        $ref: '#/components/schemas/last_login'
    UserList_ts_created:
      description: >
        Filter by exact User profile creation time. 
        The filter value should be in "YYYY-MM-DD HH:MM[:ss[.uuuuuu]]" format and its timezone will be defaulted to UTC.
        Multiple profile creation timestamps can be specified using the format "?ts_created=2022-12-05 07:40:08.023453&ts_created=2022-12-06 07:40:08.023453".
        When multiple profile creation timestamps are provided in the filter, all the user profiles whose profile creation timestamps exactly match would be returned.
        The profile creation times of the user profiles returned in the API response are in UTC timezone.
      in: query
      name: ts_created
      required: false
      schema:
        $ref: '#/components/schemas/ts_created'
    UserList_ts_created__date__lt:
      description: >
        Filters all the users whose profile creation date is less than the filter value.
        The filter value should be in "YYYY-MM-DD" format. For example: "?ts_created\__date\__lt=2022-11-23".
        Range searches on the profile creation date can be achieved by combining multiple profile creation date filters.
        For example: "?ts_created\__date\__gt=2022-11-22&ts_created\__date\__lt=2022-11-23".
        The profile creation times of the user profiles returned in the API response are in UTC timezone.
      in: query
      name: ts_created__date__lt
      required: false
      schema:
        $ref: '#/components/schemas/ts_created'
    UserList_ts_created__date__gt:
      description: >
        Filters all the users whose profile creation date is greater than the filter value.
        The filter value should be in "YYYY-MM-DD" format. For example: "?ts_created\__date\__gt=2022-11-23".
        Range searches on the profile creation date can be achieved by combining multiple profile creation date filters.
        For example: "?ts_created\__date\__gt=2022-11-22&ts_created\__date\__lt=2022-11-23".
        The profile creation times of the user profiles returned in the API response are in UTC timezone.
      in: query
      name: ts_created__date__gt
      required: false
      schema:
        $ref: '#/components/schemas/ts_created'
    UserList_ts_created__date__lte:
      description: >
        Filters all the users whose profile creation date is less than or equal to the filter value.
        The filter value should be in "YYYY-MM-DD" format. For example: "?ts_created\__date\__lte=2022-11-23".
        Range searches on the profile creation date can be achieved by combining multiple profile creation date filters.
        For example: "?ts_created\__date\__gte=2022-11-22&ts_created\__date\__lte=2022-11-23".
        The profile creation times of the user profiles returned in the API response are in UTC timezone.
      in: query
      name: ts_created__date__lte
      required: false
      schema:
        $ref: '#/components/schemas/ts_created'
    UserList_ts_created__date__gte:
      description: >
        Filters all the users whose profile creation date is greater than or equal to the filter value.
        The filter value should be in "YYYY-MM-DD" format. For example: "?ts_created\__date\__gte=2022-11-23".
        Range searches on the profile creation date can be achieved by combining multiple profile creation date filters.
        For example: "?ts_created\__date\__gte=2022-11-22&ts_created\__date\__lte=2022-11-23".
        The profile creation times of the user profiles returned in the API response are in UTC timezone.
      in: query
      name: ts_created__date__gte
      required: false
      schema:
        $ref: '#/components/schemas/ts_created'
    UserList_ts_created__lt:
      description: >
        Filter all the users whose profile creation time is less than the filter value.
        The filter value should be in "YYYY-MM-DD HH:MM[:ss[.uuuuuu]]" format and its timezone will be defaulted to UTC.
        For example: "?ts_created\__lt=2022-11-23 07:40:08.023453".
        Range searches on the profile creation time can be achieved by combining multiple profile creation datetime filters.
        For example: "?ts_created\__gt=2022-11-22 07:40:08.023453&ts_created\__lt=2022-11-23 07:40:08.023453". 
        The profile creation times of the user profiles returned in the API response are in UTC timezone.
      in: query
      name: ts_created__lt
      required: false
      schema:
        $ref: '#/components/schemas/ts_created'
    UserList_ts_created__gt:
      description: >
        Filter all the users whose profile creation time is greater than the filter value.
        The filter value should be in "YYYY-MM-DD HH:MM[:ss[.uuuuuu]]" format and its timezone will be defaulted to UTC.
        For example: "?ts_created\__gt=2022-11-23 07:40:08.023453".
        Range searches on the profile creation time can be achieved by combining multiple profile creation datetime filters.
        For example: "?ts_created\__gt=2022-11-22 07:40:08.023453&ts_created\__lt=2022-11-23 07:40:08.023453".
        The profile creation times of the user profiles returned in the API response are in UTC timezone.
      in: query
      name: ts_created__gt
      required: false
      schema:
        $ref: '#/components/schemas/ts_created'
    UserList_ts_created__lte:
      description: >
        Filter all the users whose profile creation time is less than or equal to the filter value.
        The filter value should be in "YYYY-MM-DD HH:MM[:ss[.uuuuuu]]" format and its timezone will be defaulted to UTC.
        For example: "?ts_created\__lte=2022-11-23 07:40:08.023453".
        Range searches on the profile creation time can be achieved by combining multiple profile creation datetime filters.
        For example: "?ts_created\__gte=2022-11-22 07:40:08.023453&ts_created\__lte=2022-11-23 07:40:08.023453". 
        The profile creation times of the user profiles returned in the API response are in UTC timezone.
      in: query
      name: ts_created__lte
      required: false
      schema:
        $ref: '#/components/schemas/ts_created'
    UserList_ts_created__gte:
      description: >
        Filter all the users whose profile creation time is greater than or equal to the filter value.
        The filter value should be in "YYYY-MM-DD HH:MM[:ss[.uuuuuu]]" format and its timezone will be defaulted to UTC.
        For example: "?ts_created\__gte=2022-11-23 07:40:08.023453".
        Range searches on the profile creation time can be achieved by combining multiple profile creation datetime filters.
        For example: "?ts_created\__gte=2022-11-22 07:40:08.023453&ts_created\__lte=2022-11-23 07:40:08.023453".
        The profile creation times of the user profiles returned in the API response are in UTC timezone.
      in: query
      name: ts_created__gte
      required: false
      schema:
        $ref: '#/components/schemas/ts_created'
    UserList_order_by:
      description: >
        Sort by a specified property:
         * `display_name` - A to Z, by display_name
         * `-display_name` - Z to A, by display_name
         * `email` - A to Z, by email
         * `-email` - Z to A, by email
         * `id` - Ascending by id
         * `-id` - Descending by id
         * `profile_id` - Ascending by id
         * `-profile_id` - Descending by id
         * `last_login` - Ascending by user last login
         * `-last_login` - Descending by user last login
         * `ts_created` - Ascending by user profile creation time
         * `-ts_created` - Descending by user profile creation time
      in: query
      name: order_by
      required: false
      schema:
        allOf:
          - $ref: './common/parameters.yaml#/components/schemas/OrderBy'
          - enum:
              - display_name
              - -display_name
              - email
              - -email
              - id
              - -id
              - profile_id
              - -profile_id
              - last_login
              - -last_login
              - ts_created
              - -ts_created

  securitySchemes:
    AlationAdminAPIKeyAuth:
      type: apiKey
      in: header
      name: TOKEN
      description: >
        API Key corresponding to a Server Admin user. See
        https://customerportal.alationdata.com/docs/GetToken/index.html and
        /openapi/api_authentication/ for more information.
