info:
  description: Public API for working with Policies, Data Policies & Policy Groups.
    These are types of documentation objects that provide a means for documenting
    business and other domain related policy governing artifacts and facilitate linking
    them to data.
  title: Policy API
  version: 1.0.0
openapi: 3.0.0
paths:
  /business_policies/:
    get:
      description: This API is used to retrieve Policies from the server, along with
        details including `title`, `description`, and ID. Add the `id` parameter to
        only retrieve details of a specific Policy. You can only retrieve Policies
        for which you have view or edit permissions.
      operationId: listPolicies
      parameters:
      - $ref: common/parameters.yaml#/components/parameters/ID
      - $ref: common/parameters.yaml#/components/parameters/PageLimit
      - $ref: common/parameters.yaml#/components/parameters/ItemSkip
      - $ref: common/parameters.yaml#/components/parameters/Search
      - $ref: common/parameters.yaml#/components/parameters/Deleted
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/List_Policies_Response_Body'
          description: List Policies
        400: &id001
          $ref: common/responses.yaml#/components/responses/Standard_400_Error_Response
        401: &id002
          $ref: common/responses.yaml#/components/responses/Standard_401_Error_Response
        403: &id003
          $ref: common/responses.yaml#/components/responses/Standard_403_Error_Response
        404: &id004
          $ref: common/responses.yaml#/components/responses/Standard_404_Error_Response
        500: &id005
          $ref: common/responses.yaml#/components/responses/Standard_500_Error_Response
      summary: List Policies
      tags:
      - Policies
    post:
      description: This API can be used to create Policies in bulk. A Policy must
        include a `title`.
      operationId: createPoliciesInBulk
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Create_Policy_Bulk_Request_Payload'
        required: true
      responses:
        202:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Create_Policy_Bulk_Response_Body'
          description: Details of the asynchronous task registered for creating the
            Policies and its creation status monitoring.
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: Create Policies in bulk
      tags:
      - Policies
  /policy_group:
    get:
      description: This API is used to retrieve a list of Policy Groups from the server,
        along with details including `title`, `description`, `id`, `otype`, `ts_created`,
        `url`, `policies_count` and `stewards`. Add the `id` parameter to only retrieve
        details of a specific Policy Group.
      operationId: listPolicyGroups
      parameters:
      - $ref: common/parameters.yaml#/components/parameters/ID
      - $ref: common/parameters.yaml#/components/parameters/PageLimit
      - $ref: common/parameters.yaml#/components/parameters/ItemSkip
      - $ref: common/parameters.yaml#/components/parameters/OrderBy
      - $ref: common/parameters.yaml#/components/parameters/Search
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/List_PolicyGroups_Response_Body'
          description: List Policy Groups
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: List Policy Groups
      tags:
      - Policy Groups
security:
- ApiKeyAuth: []
servers:
- url: '{protocol}://{base-url}/integration/v1'
  variables:
    base-url:
      default: localhost
      description: BASE_URL setting for this instance.
    protocol:
      default: http
      enum:
      - http
      - https
tags:
- description: API endpoints to manage Policy objects.
  name: Policies
- description: API endpoints to manage Policy Group objects. Policy Group is an object
    type that provides a means for grouping Policies and Data Policies.
  name: Policy Groups


components:
  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: TOKEN
      description: API Key for the user.

  schemas:
    Policy:
      description: Properties of a Policy.
      type: object
      properties:
        id:
          description: The ID of the Policy.
          type: integer
          readOnly: true
          example: 1
        title:
          description: The title of the Policy.
          type: string
          example: HR Leave Policy
        description:
          description: The description of the Policy.
          type: string
          example: <p>This document prescribes the leave policy which employees ... <snip> ...</p>
        otype:
          description: the Policy object type name, value being `business_policy`.
          type: string
          readOnly: true
        ts_created:
          description: Timestamp when the Policy was created.
          type: string
          format: date-time
          readOnly: true
          example: "2023-02-01T07:45:46.236895Z"
        url:
          description: URL of the Policy on UI.
          type: string
          readOnly: true
          example: "/policy/2/"
        stewards:
          type: array
          description: >
            An array of `PeopleSetField` objects containing stewards associated with the Policy.
          readOnly: true
          items:
            type: object
            properties:
              otype:
                type: enum
                enum:
                  - user
                  - groupprofile
              oid:
                type: number
              otype_display_name:
                description: Human readable name of the steward type.
                type: string
              name:
                description: The name of the steward.
                type: string
                example: John
              url:
                description: URL of the steward on UI.
                type: string
          example: [
            {
              "otype": "user",
              "otype_display_name": "User",
              "id": 4,
              "name": "cat-admin2",
              "title": "",
              "url": "/user/4/",
              "deleted": false,
              "snippet": null,
              "photo_url": "/static/img/user.png",
              "email": "<EMAIL>"
            },
            {
                "otype": "groupprofile",
                "otype_display_name": "Group Profile",
                "id": 40,
                "name": "policy-steward-grp",
                "title": "",
                "url": "/group/40/",
                "deleted": false,
                "snippet": null
            }
          ]
      example: {
        "description": "",
        "title": "HR policy 1",
        "id": 2,
        "otype": "business_policy",
        "ts_created": "2022-11-16T07:04:32.888040Z",
        "url": "/policy/2/",
        "stewards": []
      }

    PolicyGroup:
      description: Properties of a Policy Group.
      type: object
      properties:
        id:
          description: The ID of the Policy Group.
          type: integer
          readOnly: true
          example: 1
        title:
          description: The title of the Policy Group.
          type: string
          example: Sales
        description:
          description: The description of the Policy Group.
          type: string
          example: Relevant data and articles for Sales Analytics
        otype:
          description: the Policy Group object type name, value being `policy_group`.
          type: string
          readOnly: true
        ts_created:
          description: Timestamp when the Policy Group was created.
          type: string
          format: date-time
          readOnly: true
          example: "2023-02-01T07:45:46.236895Z"
        url:
          description: URL of the Policy Group on UI.
          type: string
          readOnly: true
          example: "/policy_group/1/"
        policies_count:
          description: number of all types of Policies linked to this Policy Group.
          type: number
          readOnly: true
          example: 100
        stewards:
          type: array
          description: >
            An array of `PeopleSetField` objects containing stewards linked to the Policy Group.
          readOnly: true
          items:
            type: object
            properties:
              otype:
                type: enum
                enum:
                  - user
                  - groupprofile
              oid:
                type: number
              otype_display_name:
                description: Human readable name of the steward type.
                type: string
              name:
                description: The name of the steward.
                type: string
                example: John
              url:
                description: URL of the steward on UI.
                type: string
          example: [
            {
              "otype": "user",
              "otype_display_name": "User",
              "id": 4,
              "name": "cat-admin2",
              "title": "",
              "url": "/user/4/",
              "deleted": false,
              "snippet": null,
              "photo_url": "/static/img/user.png",
              "email": "<EMAIL>"
            },
            {
                "otype": "groupprofile",
                "otype_display_name": "Group Profile",
                "id": 40,
                "name": "sales-steward-grp",
                "title": "",
                "url": "/group/40/",
                "deleted": false,
                "snippet": null
            }
          ]
      example: {
        "description": "",
        "title": "HR policy 1",
        "id": 2,
        "otype": "business_policy",
        "ts_created": "2022-11-16T07:04:32.888040Z",
        "url": "/policy/2/",
        "stewards": []
      }

    List_Policies_Response_Body:
      description: A collection of Policies.
      type: array
      items:
        $ref: "#/components/schemas/Policy"
      example: [
        {
          "description": "Policy governing human resources",
          "title": "HR policy",
          "id": 2,
          "otype": "business_policy",
          "ts_created": "2022-11-16T07:04:32.888040Z",
          "url": "/policy/2/",
          "stewards": []
        },
        {
          "description": "Policy governing financial transactions",
          "title": "Finance policy",
          "id": 1,
          "otype": "business_policy",
          "ts_created": "2022-10-07T15:08:13.425924Z",
          "url": "/policy/1/",
          "stewards": [
            {
              "otype": "user",
              "otype_display_name": "User",
              "id": 4,
              "name": "cat-admin2",
              "title": "",
              "url": "/user/4/",
              "deleted": false,
              "snippet": null,
              "photo_url": "/static/img/user.png",
              "email": "<EMAIL>"
            },
            {
              "otype": "user",
              "otype_display_name": "User",
              "id": 7,
              "name": "steward1",
              "title": "",
              "url": "/user/7/",
              "deleted": false,
              "snippet": null,
              "photo_url": "/static/img/user.png",
              "email": "<EMAIL>"
            },
            {
              "otype": "groupprofile",
              "otype_display_name": "Group Profile",
              "id": 40,
              "name": "policy-steward-grp",
              "title": "",
              "url": "/group/40/",
              "deleted": false,
              "snippet": null
            }
          ]
        }
      ]

    List_PolicyGroups_Response_Body:
      description: A collection of Policy Groups.
      type: array
      items:
        $ref: "#/components/schemas/PolicyGroup"
      example: [
        {
          "description": "All Policies governing human resources",
          "title": "HR policies",
          "id": 2,
          "otype": "policy_group",
          "ts_created": "2022-11-16T07:04:32.888040Z",
          "url": "/policy_group/1/",
          "stewards": [],
          "policies_count": 0
        },
        {
          "description": "Finance Policies",
          "title": "Finance policies",
          "id": 1,
          "otype": "policy_group",
          "ts_created": "2023-05-19T11:20:09.976141Z",
          "url": "/policy_group/2/",
          "stewards": [
            {
              "otype": "user",
              "otype_display_name": "User",
              "id": 4,
              "name": "cat-admin2",
              "title": "",
              "url": "/user/4/",
              "deleted": false,
              "snippet": null,
              "photo_url": "/static/img/user.png",
              "email": "<EMAIL>"
            },
            {
              "otype": "user",
              "otype_display_name": "User",
              "id": 7,
              "name": "steward1",
              "title": "",
              "url": "/user/7/",
              "deleted": false,
              "snippet": null,
              "photo_url": "/static/img/user.png",
              "email": "<EMAIL>"
            },
            {
              "otype": "groupprofile",
              "otype_display_name": "Group Profile",
              "id": 40,
              "name": "policy-steward-grp",
              "title": "",
              "url": "/group/40/",
              "deleted": false,
              "snippet": null
            }
          ],
          "policies_count": 25
        }
      ]

    Create_Policy_Bulk_Request_Payload:
      description: List of Policy objects to be created in bulk manner.
      type: array
      items:
        description: Payload of Policy object to be created.
        type: object
        properties:
          title:
            description: The title of the Policy.
            type: string
          description:
            description: The description of the Policy.
            type: string
          template_id:
            description: The ID of the custom template to be assigned to this Policy.
            type: integer
          policy_group_ids:
            description: >
              An array containing the Policy Group IDs that this Policy should be a member of.
            type: array
            items:
              type: integer
          fields:
            type: array
            description: >
              An array of objects containing custom field information relative to the custom
              template ID specified by `template_id`. This will get updated as metadata content
              for this Policy.
            items:
              type: object
              properties:
                field_id:
                  $ref: "common/custom_fields.yaml#/components/schemas/FieldID"
                value:
                  anyOf:
                    - $ref: "common/custom_fields.yaml#/components/schemas/TextField"
                    - $ref: "common/custom_fields.yaml#/components/schemas/RichTextField"
                    - $ref: "common/custom_fields.yaml#/components/schemas/DateField"
                    - $ref: "common/custom_fields.yaml#/components/schemas/PickerField"
                    - $ref: "common/custom_fields.yaml#/components/schemas/MultiPickerField"
                    - $ref: "common/custom_fields.yaml#/components/schemas/ObjectSetField"
                    - $ref: "common/custom_fields.yaml#/components/schemas/PeopleSetField"
                    - $ref: "common/custom_fields.yaml#/components/schemas/ReferenceField"
              required:
                - field_id
                - value
        required:
          - title
      example: [
        {
          "title": "A Financial Policy",
          "description": "Lorem ipsum ... bibendum euismod erat. Nulla vitae justo ac.
",
          "template_id": 43,
          "fields": [
            {
              "field_id": 8,
              "value": [
                  {
                      "otype": "user",
                      "oid": 3
                  },
                  {
                      "otype": "groupprofile",
                      "oid": 3
                  }
              ]
            }
          ],
          "policy_group_ids": [1, 3]
        },
        {
          "title": "Another business policy",
          "description": "<p>Another business policy desc</p>"
        },
        {
          "title": "Yet another business policy"
        }
      ]

    Create_Policy_Bulk_Response_Body:
      type: object
      properties:
        task:
          type: object
          properties:
            id:
              type: number
            type:
              type: string
            state:
              type: enum
              enum:
                - not_started
                - queued
                - started
                - finished
            status:
              type: enum
              enum:
                - na
                - succeeded
                - failed
                - partial_success
                - skipped
            ts_started:
              description: Timestamp when the task was initiated.
              type: string
              format: date-time
            links:
              type: array
              items:
                type: object
                properties:
                  rel:
                    description: relation of task to `href` link.
                    type: string
                  href:
                    description: URL link for task status or error monitoring.
                    type: string
                    format: uri
          example: {
            "task": {
                "id": 17,
                "type": "BUSINESS_POLICY_BULK_CREATE",
                "state": "QUEUED",
                "status": "NA",
                "ts_started": "2023-05-12T15:10:59.629273Z",
                "links": [
                    {
                        "rel": "status",
                        "href": "http://localhost:8000/api/v1/bulk_metadata/job/?id=17"
                    },
                    {
                        "rel": "info",
                        "href": "http://localhost:8000/api/job/17/"
                    },
                    {
                        "rel": "errors",
                        "href": "http://localhost:8000/api/job_error/?job_id=17"
                    }
                ]
            }
        }

