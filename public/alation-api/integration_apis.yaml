info:
  description: PublicAPI for creating and reading technical and logical Metadata for
    virtual and non-virtual RDBMS data sources.
  title: Relational Integration API
  version: 1.0.0
openapi: 3.0.0
paths:
  /column/:
    get:
      description: This API allows fetching of multiple columns along with its custom
        fields.
      operationId: getColumns
      parameters:
      - $ref: '#/components/parameters/id'
      - $ref: '#/components/parameters/name'
      - $ref: '#/components/parameters/table_id'
      - $ref: '#/components/parameters/table_name'
      - $ref: '#/components/parameters/schema_id'
      - $ref: '#/components/parameters/datasource_id'
      - $ref: '#/components/parameters/order_by'
      - $ref: '#/components/parameters/custom_fields'
      - $ref: '#/components/parameters/limit'
      - $ref: '#/components/parameters/skip'
      - $ref: '#/components/parameters/id__gt'
      - $ref: '#/components/parameters/id__gte'
      - $ref: '#/components/parameters/id__lt'
      - $ref: '#/components/parameters/id__lte'
      - $ref: '#/components/parameters/name__iexact'
      - $ref: '#/components/parameters/name__contains'
      - $ref: '#/components/parameters/name__icontains'
      - $ref: '#/components/parameters/name__startswith'
      - $ref: '#/components/parameters/name__istartswith'
      - $ref: '#/components/parameters/name__endswith'
      - $ref: '#/components/parameters/name__iendswith'
      - $ref: '#/components/parameters/table_id__gt'
      - $ref: '#/components/parameters/table_id__gte'
      - $ref: '#/components/parameters/table_id__lt'
      - $ref: '#/components/parameters/table_id__lte'
      - $ref: '#/components/parameters/table_name__iexact'
      - $ref: '#/components/parameters/table_name__contains'
      - $ref: '#/components/parameters/table_name__icontains'
      - $ref: '#/components/parameters/table_name__startswith'
      - $ref: '#/components/parameters/table_name__istartswith'
      - $ref: '#/components/parameters/table_name__endswith'
      - $ref: '#/components/parameters/table_name__iendswith'
      - $ref: '#/components/parameters/schema_id__gt'
      - $ref: '#/components/parameters/schema_id__gte'
      - $ref: '#/components/parameters/schema_id__lt'
      - $ref: '#/components/parameters/schema_id__lte'
      - $ref: '#/components/parameters/ds_id__gt'
      - $ref: '#/components/parameters/ds_id__gte'
      - $ref: '#/components/parameters/ds_id__lt'
      - $ref: '#/components/parameters/ds_id__lte'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ColumnResponse'
          description: List of columns matching the filters.
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        413:
          $ref: '#/components/responses/Standard_413_Error_Response'
        429:
          $ref: '#/components/responses/Standard_429_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Get columns along with its custom fields
      tags:
      - Column
    post:
      description: This API allows creation of multiple columns under a table for
        a data source denoted by ds_id, along with their custom fields. The custom
        field ids can be obtained using the Custom Fields API (https://{base-url}/openapi/custom_field/).
        Please read the request schema for more information.
      operationId: postColumns
      parameters:
      - $ref: '#/components/parameters/ds_id'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Column'
        required: true
      responses:
        202:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JobID'
          description: Job Id of the metadata upload job created.
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        413:
          $ref: '#/components/responses/Standard_413_Error_Response'
        429:
          $ref: '#/components/responses/Standard_429_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Create new columns under a particular data source
      tags:
      - Column
  /schema/:
    get:
      description: This API allows fetching of multiple schemas along with its custom
        fields.
      operationId: getSchemas
      parameters:
      - $ref: '#/components/parameters/id'
      - $ref: '#/components/parameters/name'
      - $ref: '#/components/parameters/datasource_id'
      - $ref: '#/components/parameters/order_by'
      - $ref: '#/components/parameters/custom_fields'
      - $ref: '#/components/parameters/limit'
      - $ref: '#/components/parameters/skip'
      - $ref: '#/components/parameters/id__gt'
      - $ref: '#/components/parameters/id__gte'
      - $ref: '#/components/parameters/id__lt'
      - $ref: '#/components/parameters/id__lte'
      - $ref: '#/components/parameters/name__contains'
      - $ref: '#/components/parameters/name__startswith'
      - $ref: '#/components/parameters/name__endswith'
      - $ref: '#/components/parameters/ds_id__gt'
      - $ref: '#/components/parameters/ds_id__gte'
      - $ref: '#/components/parameters/ds_id__lt'
      - $ref: '#/components/parameters/ds_id__lte'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SchemaResponse'
          description: List of schemas matching the filters.
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        413:
          $ref: '#/components/responses/Standard_413_Error_Response'
        429:
          $ref: '#/components/responses/Standard_429_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Get schemas along with its custom fields
      tags:
      - Schema
    post:
      description: This API allows creation of multiple schemas for a data source
        denoted by ds_id, along with their custom fields. The custom field ids can
        be obtained using the Custom Fields API (https://{base-url}/openapi/custom_field/).
        Please read the request schema for more information.
      operationId: postSchemas
      parameters:
      - $ref: '#/components/parameters/ds_id'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Schema'
        required: true
      responses:
        202:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JobID'
          description: Job Id of the metadata upload job created
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        413:
          $ref: '#/components/responses/Standard_413_Error_Response'
        429:
          $ref: '#/components/responses/Standard_429_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Create new schemas under a particular data source
      tags:
      - Schema
  /table/:
    get:
      description: This API allows fetching of multiple tables along with its custom
        fields.
      operationId: getTables
      parameters:
      - $ref: '#/components/parameters/id'
      - $ref: '#/components/parameters/name'
      - $ref: '#/components/parameters/schema_id'
      - $ref: '#/components/parameters/schema_name'
      - $ref: '#/components/parameters/datasource_id'
      - $ref: '#/components/parameters/order_by'
      - $ref: '#/components/parameters/custom_fields'
      - $ref: '#/components/parameters/limit'
      - $ref: '#/components/parameters/skip'
      - $ref: '#/components/parameters/id__gt'
      - $ref: '#/components/parameters/id__gte'
      - $ref: '#/components/parameters/id__lt'
      - $ref: '#/components/parameters/id__lte'
      - $ref: '#/components/parameters/name__iexact'
      - $ref: '#/components/parameters/name__contains'
      - $ref: '#/components/parameters/name__icontains'
      - $ref: '#/components/parameters/name__startswith'
      - $ref: '#/components/parameters/name__istartswith'
      - $ref: '#/components/parameters/name__endswith'
      - $ref: '#/components/parameters/name__iendswith'
      - $ref: '#/components/parameters/schema_id__gt'
      - $ref: '#/components/parameters/schema_id__gte'
      - $ref: '#/components/parameters/schema_id__lt'
      - $ref: '#/components/parameters/schema_id__lte'
      - $ref: '#/components/parameters/schema_name__iexact'
      - $ref: '#/components/parameters/schema_name__contains'
      - $ref: '#/components/parameters/schema_name__icontains'
      - $ref: '#/components/parameters/schema_name__startswith'
      - $ref: '#/components/parameters/schema_name__istartswith'
      - $ref: '#/components/parameters/schema_name__endswith'
      - $ref: '#/components/parameters/schema_name__iendswith'
      - $ref: '#/components/parameters/ds_id__gt'
      - $ref: '#/components/parameters/ds_id__gte'
      - $ref: '#/components/parameters/ds_id__lt'
      - $ref: '#/components/parameters/ds_id__lte'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TableResponse'
          description: List of tables matching the filters.
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        413:
          $ref: '#/components/responses/Standard_413_Error_Response'
        429:
          $ref: '#/components/responses/Standard_429_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Get tables along with its custom fields
      tags:
      - Table
    post:
      description: This API allows creation of multiple tables under a schema for
        a data source denoted by ds_id, along with their custom fields. The custom
        field ids can be obtained using the Custom Fields API (https://{base-url}/openapi/custom_field/).
        Please read the request schema for more information.
      operationId: postTables
      parameters:
      - $ref: '#/components/parameters/ds_id'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Table'
        required: true
      responses:
        202:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JobID'
          description: Job Id of the metadata upload job created.
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        413:
          $ref: '#/components/responses/Standard_413_Error_Response'
        429:
          $ref: '#/components/responses/Standard_429_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Create new tables under a particular data source
      tags:
      - Table
security:
- ApiKeyAuth: []
servers:
- url: '{protocol}://{base-url}/integration/v2/'
  variables:
    base-url:
      default: <base-url>
      description: Django BASE_URL setting for this instance.
    protocol:
      default: http
      enum:
      - http
      - https
tags:
- description: Represents a schema under an existing data source in Alation
  name: Schema
- description: Represents a table under an existing data source in Alation
  name: Table
- description: Represents a column under an existing data source in Alation
  name: Column


components:
  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: TOKEN
      description: API Key for the user

  schemas:

    Table:
      description: Properties of a `table` Object
      type: object
      properties:
        key:
          type: string
          description: >
            Period delimited, Fully Qualified Name of a `table`. Format
            `{DS_ID}.{SCHEMA_NAME}.{TABLE_NAME}` eg: `"7.Human Resources.Employees"`,
            `"7.catalog_name.Human_Resources.Employees"`
        title:
          type: string
          description: Title of the `table`.
        description:
          type: string
          description: The description of the `table`.
        table_comment:
          type: string
          description: Comments/information on the `table` from the source database.
        table_type:
          type: string
          description: >
            The type of the table. Value can be `TABLE`, `VIEW` or `SYNONYM`. Defaults to `TABLE`.
          enum: [TABLE, VIEW, SYNONYM]
        table_type_name:
          type: string
          description: The datasource specific name. eg. MATERIALIZED_VIEW, EXTERNAL_TABLE, ...
        owner:
          type: string
          description: Name of the database account that owns this table.
        sql:
          type: string
          description: Data definition language (SQL query) associated with `table` or `view`.
        base_table_key:
          type: string
          description: >
            The API key for referencing the base table when the table type is a `SYNONYM`.
            Note: Make sure the base_table_key is a valid and exists in the catalog.
        partition_definition:
          type: string
          description: The name/information of the partition from the source database.
        partition_columns:
          type: array
          items:
            type: string
            description: >
                The API key for columns used for the partitions. Note: Make sure you provide key
                to the valid columns and correct ds_id.
        custom_fields:
          description: >
            Custom fields that need to be uploaded for the `table` object. It is an array of
            custom field objects with `field_id` and `value`. See structure below.

             Note : These custom fields first should be associated with the `table` otype
             template. You can create and add a new custom field to the `table` otype template
             from Customize Catalog option in the UI.
          type: array
          items:
            type: object
            properties:
              field_id:
                type: string
                description: >
                    The `id` of the Custom Field. You can fetch these ids from Custom Fields
                    v2 API at `https://{base-url}/openapi/custom_field/`.


                    Note: Do not update built-in custom fields `title` (field_id=3) and
                    `description` (field_id=4) from here. Please update `title` and `description`
                    separately as shown above. Other built-in and user defined fields can be
                    updated using this.
                example: 10009
              value:
                description: >
                  Contents of Field Values.  Data structure depends on the type of field.
                oneOf:
                    - $ref: "#/components/schemas/TextField"
                    - $ref: "#/components/schemas/RichTextField"
                    - $ref: "#/components/schemas/DateField"
                    - $ref: "#/components/schemas/PickerField"
                    - $ref: "#/components/schemas/MultiPickerField"
                    - $ref: "#/components/schemas/ObjectSetField"
                    - $ref: "#/components/schemas/PeopleSetField"
                    - $ref: "#/components/schemas/ReferenceField"
      required:
        - key

      example:
        - key: 95.marketing_dept.employee_quota
          title: Employee Quota data
          description: Stores information of employees from marketing department and their quota.
          table_comment: Quota table in marketing_dept
          table_type: TABLE
          table_type_name: Org specific table
          owner: sales_admin
          sql: Create table employee_quota(integer id, float quota);
          custom_fields:
            - field_id: 10006
              value: 'True'
            - field_id: 8
              value:
                - otype: user
                  oid: 1

    Column:
      description: Properties of a `column` Object
      type: object
      properties:
        key:
          type: string
          description: >
            Period delimited, Fully Qualified Name of a `column`. Format
            `{DS_ID}.{SCHEMA_NAME}.{TABLE_NAME}.{COLUMN_NAME}}` eg:
            `"7.Human_Resources.Employees.First_Name"`,
            `"7.catalog_name.schema_name.table_name.col_name"`
        column_type:
          type: string
          description: >
            The string describing the type of the `column`. eg. int, varchar(100)
        title:
          type: string
          description: Title of the `column`.
        description:
          type: string
          description: The description of the `column`.
        column_comment:
          type: string
          description: A comment field that stores a description of the `column` which is ingested
            from the source system. eg. "Column has a default value"
        nullable:
          type: boolean
          description: Field to indicate if the `column` can contain null values. Set this to
            `True` if the column contains null value otherwise set it to `False`. Defaults to
            `False`. Note - Since it defaults to `False`, that means that this column cannot
            contain null values. The text `NO NULLS` appears in the column catalog page
            representing this behavior.
        position:
          type: string
          description: Position of the `column` in the table.
        index:
          type: object
          description: Define the index that the `column` is associated with.
          properties:
            isPrimaryKey:
              type: boolean
              description: If the `column` is a primary key then set it to `true`.
                Defaults to `False`.
            isForeignKey:
              type: boolean
              description: If the `column` is part of a foreign key, set it to `true` and pass
                the associated `column` in the `referencedColumnId` field. Defaults to `false`.
            referencedColumnId:
              type: string
              description: API key of the `column` being referenced from the datasource.
                eg. `1.schema.table.col1`
            isOtherIndex:
              type: boolean
              description: If the `column` is a part of any other index, set it to `true` and pass
                the associated `column` in the `referencedColumnId` field. Defaults to `false`.
        custom_fields:
          description: >
            Custom fields that need to be uploaded for the `column` object. It is an array of
            custom field objects with `field_id` and `value`. See structure below.

             Note : These custom fields first should be associated with the `column` otype
             template. You can create and add a new custom field to the `column` otype template
             from Customize Catalog option in the UI.
          type: array
          items:
            type: object
            properties:
              field_id:
                type: string
                description: >
                    The `id` of the Custom Field. You can fetch these ids from Custom Fields
                    v2 API at `https://{base-url}/openapi/custom_field/`.


                    Note: Do not update built-in custom fields `title` (field_id=3) and
                    `description` (field_id=4) from here. Please update `title` and `description`
                    separately as shown above. Other built-in and user defined fields can be
                    updated using this.
                example: 10009
              value:
                description: >
                  Contents of Field Values.  Data structure depends on the type of field.
                oneOf:
                    - $ref: "#/components/schemas/TextField"
                    - $ref: "#/components/schemas/RichTextField"
                    - $ref: "#/components/schemas/DateField"
                    - $ref: "#/components/schemas/PickerField"
                    - $ref: "#/components/schemas/MultiPickerField"
                    - $ref: "#/components/schemas/ObjectSetField"
                    - $ref: "#/components/schemas/PeopleSetField"
                    - $ref: "#/components/schemas/ReferenceField"
      required:
        - key
        - column_type

      example:
        - key: 95.marketing_dept.employee_quota.sales_id
          title: Employee Quota data sales ids
          description: Stores the sales id amount data from the employee_quota table.
          column_comment: Quota table sales id in marketing_dept
          column_type: int
          nullable: false
          position: 1
          index:
            isForeignKey: true
            referencedColumnId: 95.marketing_dept.sales_table.id
          custom_fields:
            - field_id: 10006
              value: 'True'
            - field_id: 8
              value:
                - otype: user
                  oid: 1

    Schema:
      description: Properties of a `Schema` Object
      type: object
      properties:
        key:
          type: string
          description: >
            Period delimited, Fully Qualified Name of a `schema`. Format `{DS_ID}.{SCHEMA_NAME}`
            eg: `"7.Human Resources"`, `"7.HR.2017"`
            (Schema name with a period)
        title:
          type: string
          description: Title of the `schema`.
        description:
          type: string
          description: The description of the `schema`.
        db_comment:
          type: string
          description: Comments on the `schema` from the data source.
        custom_fields:
          description: >
            The Custom fields that need to be uploaded for the `schema` object. It is an array of
            custom field objects with `field_id` and `value`. See structure below.

             Note : These custom fields first should be associated with the `schema` otype
             template. You can create and add a new custom field to the `schema` otype template
             from Customize Catalog option in the UI.
          type: array
          items:
            type: object
            properties:
              field_id:
                type: string
                description:  >
                    The `id` of the Custom Field. You can fetch these ids from Custom Fields
                    v2 API at `https://{base-url}/openapi/custom_field/`.


                    Note: Do not update built-in custom fields `title` (field_id=3) and
                    `description` (field_id=4) from here. Please update `title` and `description`
                    separately as shown above. Other built-in and user defined fields can be
                    updated using this.
                example: 10009
              value:
                description: >
                  Contents of Field Values.  Data structure depends on the type of field.
                oneOf:
                    - $ref: "#/components/schemas/TextField"
                    - $ref: "#/components/schemas/RichTextField"
                    - $ref: "#/components/schemas/DateField"
                    - $ref: "#/components/schemas/PickerField"
                    - $ref: "#/components/schemas/MultiPickerField"
                    - $ref: "#/components/schemas/ObjectSetField"
                    - $ref: "#/components/schemas/PeopleSetField"
                    - $ref: "#/components/schemas/ReferenceField"
      required:
        - key

      example:
        - key: 95.employees
          title: Schema for employees data.
          description: Stores all the tables related to employees from marketing department.
          db_comment: This schema is part of company database storing important PII data.
          custom_fields:
            - field_id: 10009
              value: 'True'
            - field_id: 8
              value:
                - otype: user
                  oid: 1

    SchemaResponse:
      description: Get Schema response
      type: object
      properties:
        id:
          type: integer
          description: Integer id of the schema
        name:
          type: string
          description: name of the schema object
        title:
          type: string
          description: Title of the `schema`.
        description:
          type: string
          description: The description of the `schema`.
        ds_id:
          type: integer
          description: data source id associated with the schema object.
        key:
          type: string
          description: >
            Period delimited, Fully Qualified Name of a `schema`. Format `{DS_ID}.{SCHEMA_NAME}`
            eg: `"7.Human Resources"`, `"7.HR.2017"`
            (Schema name with a period)
        url:
          type: string
          description: Relative url of the object in alation.
        custom_fields:
          description: >
            The Custom fields that are associated for the `schema` object. It is an array of
            custom field objects with `value`, `field_id`, `field_name`. See structure below.
          type: array
          items:
            type: object
            properties:
              value:
                description: >
                  Contents of Field Values.  Data structure depends on the type of field.
                oneOf:
                    - $ref: "#/components/schemas/TextField"
                    - $ref: "#/components/schemas/RichTextField"
                    - $ref: "#/components/schemas/DateField"
                    - $ref: "#/components/schemas/PickerField"
                    - $ref: "#/components/schemas/MultiPickerField"
                    - $ref: "#/components/schemas/ObjectSetField"
                    - $ref: "#/components/schemas/PeopleSetField"
                    - $ref: "#/components/schemas/ReferenceField"
              field_id:
                type: string
                description:  >
                    The `id` of the Custom Field.
                example: 10009
              field_name:
                type: string
                description:  >
                    The `name` of the Custom Field.
                example: "Notes"
        db_comment:
          type: string
          description: Comments on the `schema` from the data source.


      example:
        - id: 181
          name: "Employees"
          title: Schema for employees data.
          description: Stores all the tables related to employees from marketing department.
          ds_id: 95
          key: "95.employees"
          url: "/schema/181"
          custom_fields:
            - value: "This field contains Important Notes"
              field_id: 10009
              field_name: "Notes"
            - value:
                - otype: user
                  oid: 1
              field_id: 8
              field_name: Steward
          db_comment: This schema is part of company database storing important PII data.

    TableResponse:
      description: Get `table` Response
      type: object
      properties:
        id:
          type: integer
          description: Integer id of the `table`
        name:
          type: string
          description: name of the `table` object
        title:
          type: string
          description: Title of the `table`.
        description:
          type: string
          description: The description of the `table`.
        ds_id:
          type: integer
          description: data source id associated with the `table` object.
        key:
          type: string
          description: >
            Period delimited, Fully Qualified Name of a `table`. Format
            `{DS_ID}.{SCHEMA_NAME}.{TABLE_NAME}` eg: `"7.Human Resources.Employees"`,
            `"7.catalog_name.Human_Resources.Employees"`
        url:
          type: string
          description: Relative url of the object in alation.
        custom_fields:
          description: >
            Custom fields that are associated with the `table` object. It is an array of
            custom field objects with `value`, `field_id` and `field_name`. See structure below.
          type: array
          items:
            type: object
            properties:
              value:
                description: >
                  Contents of Field Values.  Data structure depends on the type of field.
                oneOf:
                    - $ref: "#/components/schemas/TextField"
                    - $ref: "#/components/schemas/RichTextField"
                    - $ref: "#/components/schemas/DateField"
                    - $ref: "#/components/schemas/PickerField"
                    - $ref: "#/components/schemas/MultiPickerField"
                    - $ref: "#/components/schemas/ObjectSetField"
                    - $ref: "#/components/schemas/PeopleSetField"
                    - $ref: "#/components/schemas/ReferenceField"
              field_id:
                type: string
                description: >
                    The `id` of the Custom Field.
                example: 10009
              field_name:
                type: string
                description:  >
                    The `name` of the Custom Field.
                example: "Notes"
        table_type:
          type: string
          description: >
            The type of the table. Value can be `TABLE`, `VIEW` or `SYNONYM`. Defaults to `TABLE`.
          enum: [TABLE, VIEW, SYNONYM]
        schema_id:
          type: integer
          description: Id of the schema object associated with the `table`
        schema_name:
          type: string
          description: Name of the schema object associated with the `table`
        base_table_key:
          type: string
          description: >
            The API key for referencing the base table when the table type is a `SYNONYM`.
        sql:
          type: string
          description: Data definition language (SQL query) associated with `table` or `view`.
        partition_definition:
          type: string
          description: The name/information of the partition from the source database.
        partition_columns:
          type: array
          items:
            type: string
            description: >
                The API key for columns used for the partitions.
        table_comment:
          type: string
          description: Comments/information on the `table` from the source database.

      example:
        - id: 7744
          name: employee_quota
          title: Employee Quota data
          description: Stores information of employees from marketing department and their quota.
          ds_id: 95
          key: 95.marketing_dept.employee_quota
          url: "/table/7744"
          custom_fields:
            - value: "This field contains Important Notes"
              field_id: 10009
              field_name: "Notes"
            - value:
                - otype: user
                  oid: 1
              field_id: 8
              field_name: Steward
          table_type: TABLE
          schema_id: 777
          schema_name: marketing_dept
          sql: Create table employee_quota(integer id, float quota);
          table_comment: Quota table in marketing_dept

    ColumnResponse:
      description: Properties of a `column` Object
      type: object
      properties:
        id:
          type: integer
          description: Integer id of the `column`
        name:
          type: string
          description: name of the `column` object
        title:
          type: string
          description: Title of the `column`.
        description:
          type: string
          description: The description of the `column`.
        ds_id:
          type: integer
          description: data source id associated with the `column` object.
        key:
          type: string
          description: >
            Period delimited, Fully Qualified Name of a `column`. Format
            `{DS_ID}.{SCHEMA_NAME}.{TABLE_NAME}.{COLUMN_NAME}}` eg:
            `"7.Human_Resources.Employees.First_Name"`,
            `"7.catalog_name.schema_name.table_name.col_name"`
        url:
          type: string
          description: Relative url of the object in alation.
        custom_fields:
          description: >
            Custom fields that are associated with the `column` object. It is an array of
            custom field objects with `value`, `field_id` and `field_name`. See structure below.
          type: array
          items:
            type: object
            properties:
              value:
                description: >
                  Contents of Field Values.  Data structure depends on the type of field.
                oneOf:
                    - $ref: "#/components/schemas/TextField"
                    - $ref: "#/components/schemas/RichTextField"
                    - $ref: "#/components/schemas/DateField"
                    - $ref: "#/components/schemas/PickerField"
                    - $ref: "#/components/schemas/MultiPickerField"
                    - $ref: "#/components/schemas/ObjectSetField"
                    - $ref: "#/components/schemas/PeopleSetField"
                    - $ref: "#/components/schemas/ReferenceField"
              field_id:
                type: string
                description: >
                    The `id` of the Custom Field.
                example: 10009
              field_name:
                type: string
                description:  >
                    The `name` of the Custom Field.
                example: "Notes"
        column_type:
          type: string
          description: >
            The string describing the type of the `column`. eg. int, varchar(100)
        column_comment:
          type: string
          description: A comment field that stores a description of the `column` which is ingested
            from the source system. eg. "Column has a default value"
        index:
          type: object
          description: Define the index that the `column` is associated with.
          properties:
            isPrimaryKey:
              type: boolean
              description: If the `column` is a primary key then `true` else `false`.
            isForeignKey:
              type: boolean
              description: If the `column` is part of a foreign key then `true` else `false`. Associated `column` in the `referencedColumnId` field.
            referencedColumnId:
              type: string
              description: API key of the `column` being referenced from the datasource.
                eg. `1.schema.table.col1`
            isOtherIndex:
              type: boolean
              description: If the `column` is a part of any other index it is `true` else `false`. Associated `column` in the `referencedColumnId` field.
        nullable:
          type: boolean
          description: Field to indicate if the `column` can contain null values. `True` if the column contains null value otherwise `False`. Since it defaults to
            `False` while creation that means that this column cannot
            contain null values. The text `NO NULLS` appears in the column catalog page
            representing this behavior.
        schema_id:
          type: integer
          description: Id of the `schema` object associated with the `column`
        table_id:
          type: integer
          description: Id of the `table` object associated with the `column`
        table_name:
          type: string
          description: Name of the `table` object associated with the `column`
        position:
          type: string
          description: Position of the `column` in the table.

      example:
        - id: 161782
          name: sales_id
          title: Employee Quota data sales ids
          description: Stores the sales id amount data from the employee_quota table.
          ds_id: 95
          key: 95.marketing_dept.employee_quota.sales_id
          url: /attribute/161782/
          custom_fields:
            - value: "This field contains Important Notes"
              field_id: 10009
              field_name: "Notes"
            - value:
                - otype: user
                  oid: 1
              field_id: 8
              field_name: Steward
          column_type: int
          column_comment: Quota table sales id in marketing_dept
          index:
            isForeignKey: true
            referencedColumnId: 95.marketing_dept.sales_table.id
          nullable: false
          schema_id: 777
          table_id: 7741
          table_name: employee_quota
          position: 1

    TextField:
      description: >
        Simple string representation of the custom field value.
      type: string
      example: "sample text"

    RichTextField:
      description: >
          Rich-text compatible string representation of the custom field value.
      type: string
      example: "RTF"

    DateField:
      description: >
          ISO-8601 date-time representation of the custom field value.
      type: string
      format: date-time
      example: "1997-07-16T19:20+01:00"

    PickerField:
      description: >
          String representation of the selected picker value.
      type: string
      example: "Approved"

    MultiPickerField:
      description: >
          An array of strings representing the selected multi-picker values.
      type: array
      items:
          type: string
      example:
        - Approved
        - Rejected
        - Pending

    ObjectSetField:
      description: >
          An array of object keys representing the values of an Object Set Field. Eg. To add
           different objects to an article.
      type: array
      items:
          $ref: "#/components/schemas/ObjectKey"
      example:
      - otype: schema
        oid: 10

    PeopleSetField:
      description: >
          An array of object keys representing the values of an People Set Field. Eg. To
           add stewards, experts.
      type: array
      items:
          $ref: "#/components/schemas/ObjectKey"
      example:
      - otype: user
        oid: 1

    ReferenceField:
      description: >
          An array of object keys representing the values of References Field. Eg. To add
           references to a schema, user, table, article etc.
      type: array
      items:
          $ref: "#/components/schemas/ObjectKey"
      example:
      - otype: article
        oid: 10

    ObjectKey:
      type: object
      properties:
        otype:
          $ref: "#/components/schemas/otype"
        oid:
          $ref: "#/components/schemas/oid"

    otype:
      description: >
          The type of the object that needs to be associated with this Custom Field Value.
      type: string
      example:
          schema

    oid:
      description: >
          The id of the object that needs to be associated with this Custom Field Value.
      type: string
      example:
          5

    JobID:
      description: Properties of a Job Object
      type: object
      properties:
        job_id:
          type: integer
          description: >
            GET /api/v1/bulk_metadata/job/?id={job_id} will return the status of the underlying
            POST job

    Error:
      type: object
      description: Properties of a Error Object
      properties:
        errors:
          type: object
          description: Explicit information about the error message
        code:
          type: string
          description: >
            A six digit code that identifies the problem. Refer the error documentation
            for more information.
        detail:
          type: string
          description: Details about the error message
        title:
          type: string
          description: The title of the error message

  responses:
    Standard_400_Error_Response:
      description: Malformed Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "400XXX"
            title: "Malformed Request"
            detail: "The request sent by user is in the wrong format.
            (Refer the error documentation for specific details of the error)"
    Standard_401_Error_Response:
      description: Unauthorized bad/missing token
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "401XXX"
            title: "Unauthorized"
            detail: "You are unauthorized to access this resource. Please obtain valid credentials.
            (Refer the error documentation for specific details of the error)"
    Standard_403_Error_Response:
      description: Forbidden User cannot edit this resource
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "403XXX"
            title: "Forbidden Action"
            detail: "This is forbidden to access. Make sure you access the right resource.
            (Refer the error documentation for specific details of the error)"
    Standard_404_Error_Response:
      description: The specified resource was not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "404XXX"
            title: "Resource not found"
            detail: "This is not a valid resource. Please try something else.
            (Refer the error documentation for specific details of the error)"
    Standard_413_Error_Response:
      description: Request Too Large
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "413XXX"
            title: "Request Too Large"
            detail: "Payload contains 100000 objects. It is currently recommended to use
            lesser than or equal to 1000 objects at a time for this API.
            (Refer the error documentation for specific details of the error)"
    Standard_429_Error_Response:
      description: Too many requests
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "429XXX"
            title: "Too many requests"
            detail: "Rate limit for read/ write requests reached. Expected available in X seconds."
    Standard_500_Error_Response:
      description: Internal Server Error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "500XXX"
            title: "Internal Server Error"
            detail: "Something went wrong, Please try again later.
            (Refer the error documentation for specific details of the error)"

  parameters:
    ds_id:
      name: ds_id
      description: Unique identifier of the data source
      in: query
      schema:
        type: integer
      required: true
    datasource_id:
      name: ds_id
      description: Unique identifier of the data source
      in: query
      schema:
        type: integer
      required: false
    limit:
      description: >
        Specifies the number of objects to be fetched in one paginated request. Response may
        have a `X-Next-Page` URL parameter in the header to fetch next set of objects or page
      in: query
      name: limit
      required: false
      schema:
        type: integer
        default: 100
    skip:
      description: >
        Specifies the number of objects to be skipped in one request. Used together with
        `Limit` parameter to fetch paginated requests. This is automatically set in `X-Next-Page`
        response header if present
      in: query
      name: skip
      required: false
      schema:
        type: integer
        default: 0
    order_by:
      description: Ordering of objects by `id` and `name`
      in: query
      name: order_by
      required: false
      schema:
        type: string
        enum: [id, name]
    ds_id__gt:
      name: ds_id__gt
      description: filter by data source `id` greater than a value
      in: query
      schema:
        type: integer
      required: false
    ds_id__gte:
      name: ds_id__gte
      description: filter by data source `id` greater than or equal to a value
      in: query
      schema:
        type: integer
      required: false
    ds_id__lt:
      name: ds_id__lt
      description: filter by data source `id` lesser than a value
      in: query
      schema:
        type: integer
      required: false
    ds_id__lte:
      name: ds_id__lte
      description: filter by data source `id` lesser than or equal to a value
      in: query
      schema:
        type: integer
      required: false
    schema_id:
      name: schema_id
      description: filter by schema `id`
      in: query
      schema:
        type: integer
      required: false
    schema_id__gt:
      name: schema_id__gt
      description: filter by schema `id` greater than a value
      in: query
      schema:
        type: integer
      required: false
    schema_id__gte:
      name: schema_id__gte
      description: filter by schema `id` greater than or equal to a value
      in: query
      schema:
        type: integer
      required: false
    schema_id__lt:
      name: schema_id__lt
      description: filter by schema `id` lesser than a value
      in: query
      schema:
        type: integer
      required: false
    schema_id__lte:
      name: schema_id__lte
      description: filter by schema `id` lesser than or equal to a value
      in: query
      schema:
        type: integer
      required: false
    schema_name:
      name: schema_name
      description: filter by schema `name`
      in: query
      schema:
        type: string
      required: false
    schema_name__iexact:
      name: schema_name__iexact
      description: filter by schema `name` case insensitive exact match
      in: query
      schema:
        type: string
      required: false
    schema_name__contains:
      name: schema_name__contains
      description: filter by schema `name` containing the given string
      in: query
      schema:
        type: string
      required: false
    schema_name__icontains:
      name: schema_name__icontains
      description: filter by schema `name` containing the given string, case insensitive match
      in: query
      schema:
        type: string
      required: false
    schema_name__startswith:
      name: schema_name__startswith
      description: filter by schema `name` starting with the given string
      in: query
      schema:
        type: string
      required: false
    schema_name__istartswith:
      name: schema_name__istartswith
      description: filter by schema `name` starting with the given string, case insensitive match
      in: query
      schema:
        type: string
      required: false
    schema_name__endswith:
      name: schema_name__endswith
      description: filter by schema `name` ending with the given string
      in: query
      schema:
        type: string
      required: false
    schema_name__iendswith:
      name: schema_name__iendswith
      description: filter by schema `name` ending with the given string, case insensitive match
      in: query
      schema:
        type: string
      required: false
    table_id:
      name: table_id
      description: filter by table `id`
      in: query
      schema:
        type: integer
      required: false
    table_id__gt:
      name: table_id__gt
      description: filter by table `id` greater than a value
      in: query
      schema:
        type: integer
      required: false
    table_id__gte:
      name: table_id__gte
      description: filter by table `id` greater than or equal to a value
      in: query
      schema:
        type: integer
      required: false
    table_id__lt:
      name: table_id__lt
      description: filter by table `id` lesser than a value
      in: query
      schema:
        type: integer
      required: false
    table_id__lte:
      name: table_id__lte
      description: filter by table `id` lesser than or equal to a value
      in: query
      schema:
        type: integer
      required: false
    table_name:
      name: table_name
      description: filter by table `name`
      in: query
      schema:
        type: string
      required: false
    table_name__iexact:
      name: table_name__iexact
      description: filter by table `name` case insensitive exact match
      in: query
      schema:
        type: string
      required: false
    table_name__contains:
      name: table_name__contains
      description: filter by table `name` containing the given string
      in: query
      schema:
        type: string
      required: false
    table_name__icontains:
      name: table_name__icontains
      description: filter by table `name` containing the given string, case insensitive match
      in: query
      schema:
        type: string
      required: false
    table_name__startswith:
      name: table_name__startswith
      description: filter by table `name` starting with the given string
      in: query
      schema:
        type: string
      required: false
    table_name__istartswith:
      name: table_name__istartswith
      description: filter by table `name` starting with the given string, case insensitive match
      in: query
      schema:
        type: string
      required: false
    table_name__endswith:
      name: table_name__endswith
      description: filter by table `name` ending with the given string
      in: query
      schema:
        type: string
      required: false
    table_name__iendswith:
      name: table_name__iendswith
      description: filter by table `name` ending with the given string, case insensitive match
      in: query
      schema:
        type: string
      required: false
    name:
      name: name
      description: filter by object `name`
      in: query
      schema:
        type: string
      required: false
    name__iexact:
      name: name__iexact
      description: filter by object `name` case insensitive exact match
      in: query
      schema:
        type: string
      required: false
    name__contains:
      name: name__contains
      description: filter by object `name` containing the given string
      in: query
      schema:
        type: string
      required: false
    name__icontains:
      name: name__icontains
      description: filter by object `name` containing the given string, case insensitive match
      in: query
      schema:
        type: string
      required: false
    name__startswith:
      name: name__startswith
      description: filter by object `name` starting with the given string
      in: query
      schema:
        type: string
      required: false
    name__istartswith:
      name: name__istartswith
      description: filter by object `name` starting with the given string, case insensitive match
      in: query
      schema:
        type: string
      required: false
    name__endswith:
      name: name__endswith
      description: filter by object `name` ending with the given string
      in: query
      schema:
        type: string
      required: false
    name__iendswith:
      name: name__iendswith
      description: filter by object `name` ending with the given string, case insensitive match
      in: query
      schema:
        type: string
      required: false
    id:
      description: filter by `id` of the object
      in: query
      name: id
      required: false
      schema:
        type: integer
    id__gt:
      name: id__gt
      description: filter by `id` greater than a value
      in: query
      schema:
        type: integer
      required: false
    id__gte:
      name: id__gte
      description: filter by `id` greater than or equal to a value
      in: query
      schema:
        type: integer
      required: false
    id__lt:
      name: id__lt
      description: filter by `id` lesser than a value
      in: query
      schema:
        type: integer
      required: false
    id__lte:
      name: id__lte
      description: filter by `id` lesser than or equal to a value
      in: query
      schema:
        type: integer
      required: false
    custom_fields:
      name: custom_fields
      description: >
        filter objects by `custom fields`. Currently we can only filter by a single
        custom field. The example below shows only `{}`, please add array braces `[]` around the
        `{}` object for it work properly. eg: To list objects which have user id 1 as a steward is
        `[{"field_id":8,"value":[{"otype": "user", "oid":1}]}]`. Note: Use the appropritate data
        structure for the value.
      in: query
      schema:
        type: array
        items:
          type: object
          properties:
            field_id:
              type: integer
              description: >
                The `id` of the Custom Field. You can fetch these ids from Custom Fields
                v2 API at `https://{base-url}/openapi/custom_field/`.
              example: 10009
            value:
              description: >
                Contents of Field Values.  Data structure depends on the type of field.
              oneOf:
                - $ref: "#/components/schemas/TextField"
                - $ref: "#/components/schemas/RichTextField"
                - $ref: "#/components/schemas/DateField"
                - $ref: "#/components/schemas/PickerField"
                - $ref: "#/components/schemas/MultiPickerField"
                - $ref: "#/components/schemas/ObjectSetField"
                - $ref: "#/components/schemas/PeopleSetField"
                - $ref: "#/components/schemas/ReferenceField"
      required: false
