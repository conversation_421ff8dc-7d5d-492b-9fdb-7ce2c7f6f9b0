info:
  description: PublicAPIs for managing refresh and API access tokens for the user.
  title: Token Authentication and Management APIs.
  version: 1.0.0
openapi: 3.0.0
paths:
  /createAPIAccessToken/:
    post:
      description: This API lets you to create a new API Access Token that can be
        used for authenticating Alation public API requests.
      operationId: createAPIAccessToken
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RefreshTokenPayload'
        required: true
      responses:
        201:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIAccessToken'
          description: Created
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Creates a new API Access Token.
      tags:
      - APIAccessToken
  /createRefreshToken/:
    post:
      description: This API lets you create a new RefreshToken.
      operationId: createRefreshToken
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserCredsPayload'
        required: true
      responses:
        201:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RefreshToken'
          description: Created
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Creates a new RefreshToken for the user.
      tags:
      - RefreshToken
  /regenRefreshToken/:
    post:
      description: This API lets you to regenerate a RefreshToken, invalidating the
        current token passed in the request.
      operationId: regenRefreshToken
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RefreshTokenPayload'
        required: true
      responses:
        201:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RefreshToken'
          description: Created
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Regenerates a RefreshToken, invalidating the current token in request
        body.
      tags:
      - RefreshToken
  /revokeAPIAccessTokens/:
    post:
      description: This API lets you to revoke all active API Access Tokens for the
        user.
      operationId: revokeAPIAccessTokens
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RefreshTokenPayload'
        required: true
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RevokeTokensSuccessResponse'
          description: OK
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Revokes all the active API Access Tokens for the user.
      tags:
      - APIAccessToken
  /validateAPIAccessToken/:
    post:
      description: This API lets you to validate an API AccessToken, returning useful
        metadata about the token, if valid.
      operationId: validateAPIAccessToken
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/APIAccessTokenPayload'
        required: true
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIAccessToken'
          description: ''
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Validates the given API Access Token.
      tags:
      - APIAccessToken
  /validateRefreshToken/:
    post:
      description: This API lets you validate an existing RefreshToken, returning
        useful metadata about the token, if valid.
      operationId: validateRefreshToken
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RefreshTokenPayload'
        required: true
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RefreshToken'
          description: OK
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Validate the given RefreshToken.
      tags:
      - RefreshToken
servers:
- url: '{protocol}://{base-url}/integration/v1'
  variables:
    base-url:
      default: localhost
      description: Django BASE_URL setting for this instance.
    protocol:
      default: http
      enum:
      - http
      - https
tags:
- description: Represents a refresh token used for generating API access tokens in
    Alation
  name: RefreshToken
- description: Represents an API access token used for authenticating and accessing
    other public APIs in Alation
  name: APIAccessToken


components:

  schemas:

    UserCredsPayload:
      type: object
      properties:
        username:
          type: string
          description: Username of the user on Alation.
          example: <EMAIL>
        password:
          type: string
          description: Password associated with the user on Alation.
          example: P@s$w0rd!
        name:
          type: string
          description: Create the RefreshToken with this name.
          maxLength: 256
          example: TableauRefreshToken

      required:
        - username
        - password
        - name

    APIAccessTokenPayload:
      type: object
      properties:
        api_access_token:
          type: string
          description:  >
            APIAccessToken that can be used as token header for authenticating public API requests to
            Alation Server.
          example: e25388fde8290dc286a6164fa2d97e551b53498dcbf7bc378eb1f178
        user_id:
          type: integer
          description: >
           User ID associated with the refresh token who generated this token on Alation.
          example: 102

      required:
        - user_id
        - api_access_token

    RefreshTokenPayload:
      type: object
      properties:
        refresh_token:
          type: string
          description: RefreshToken generated for the user in Alation.
          example: 6b86b273ff34fce19d6b804eff5a3f5747ada4eaa22f1d49c01e52ddb7875b4b
        user_id:
          type: integer
          description: >
           User ID associated with the refresh token who generated this token on Alation.
          example: 102

      required:
        - user_id
        - refresh_token

    APIAccessToken:
      type: object
      properties:
        api_access_token:
          type: string
          description: >
            APIAccessToken that can be used as token header for authenticating public API requests
            to Alation Server.
          example: e25388fde8290dc286a6164fa2d97e551b53498dcbf7bc378eb1f178
        user_id:
          type: integer
          description: >
           User ID associated with the refresh token who generated this token on Alation.
          example: 102
        created_at:
          type: string
          format: date-time
          description: >
           Timezone aware date-time at which the api access token is created at.
        token_expires_at:
          type: string
          format: date-time
          description: >
           Timezone aware date-time until which the api access token is valid for.
        token_status:
          type: string
          enum: [active, expired, revoked]
          description: Current status of the access token.
          example: active

    RefreshToken:
      type: object
      properties:
        refresh_token:
          type: string
          description: RefreshToken generated for the user in Alation.
          example: 6b86b273ff34fce19d6b804eff5a3f5747ada4eaa22f1d49c01e52ddb7875b4b
        user_id:
          type: integer
          description: >
           User ID associated with the refresh token who generated this token on Alation.
          example: 102
        created_at:
          type: string
          format: date-time
          description: >
           Timezone aware date-time at which the refresh token is created at.
        name:
          type: string
          description: Name of the RefreshToken.
          maxLength: 256
          example: TableauRefreshToken
        token_expires_at:
          type: string
          format: date-time
          description: >
           Timezone aware date-time until which the refresh token is valid for.
        token_status:
          type: string
          enum: [active, expired, revoked]
          description: Current status of the refresh token.
          example: active
    
    RevokeTokensSuccessResponse:
      type: object
      properties:
        detail:
          type: string
          description: Success Message after revoking the tokens.
          example: "Revoked active access tokens for user: '10'"
    Error:
      type: object
      description: Properties of a Error Object
      properties:
        errors:
          type: object
          description: Explicit information about the error message
        code:
          type: string
          description: >
            A six digit code that identifies the problem. Refer the error documentation
            for more information.
        detail:
          type: string
          description: Details about the error message
        title:
          type: string
          description: The title of the error message

  requestBodies:
    UserCredsPayload_Request:
      description: Request body for user credentials to be used when using createRefreshToken API.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/UserCredsPayload'

  responses:
    Standard_400_Error_Response:
      description: Malformed Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "400XXX"
            title: "Malformed Request"
            detail: "The request sent by user is in the wrong format.
            (Refer the error documentation for specific details of the error)"

    Standard_401_Error_Response:
      description: Unauthorized bad/missing token
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "401XXX"
            title: "Unauthorized"
            detail: "You are unauthorized to access this resource. Please obtain valid credentials.
            (Refer the error documentation for specific details of the error)"
    Standard_403_Error_Response:
      description: Forbidden User cannot edit this resource
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "403XXX"
            title: "Forbidden Action"
            detail: "This is forbidden to access. Make sure you access the right resource.
            (Refer the error documentation for specific details of the error)"
    Standard_404_Error_Response:
      description: The specified resource was not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "404XXX"
            title: "Resource not found"
            detail: "This is not a valid resource. Please try something else.
            (Refer the error documentation for specific details of the error)"
    Standard_500_Error_Response:
      description: Internal Server Error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "500XXX"
            title: "Internal Server Error"
            detail: "Something went wrong, Please try again later.
            (Refer the error documentation for specific details of the error)"
