info:
  description: PublicAPI for Creating, Reading, Updating and Deleting BI objects
  title: BI Source API
  version: 1.0.0
openapi: 3.0.0
paths:
  /server/:
    get:
      description: GET /server/ returns a list of all BI Server objects in Alation.
        Note that this includes BI Servers catalogued outside of the API (i.e. Tableau
        Servers).
      operationId: getServers
      parameters:
      - $ref: '#/components/parameters/ObjectIds'
      - $ref: '#/components/parameters/Limit'
      - $ref: '#/components/parameters/Offset'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Servers'
          description: Requested BI Servers
        400: &id001
          $ref: '#/components/responses/Standard_400_Error_Response'
        401: &id002
          $ref: '#/components/responses/Standard_401_Error_Response'
        403: &id003
          $ref: '#/components/responses/Standard_403_Error_Response'
        404: &id004
          $ref: '#/components/responses/Standard_404_Error_Response'
        500: &id005
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: GET a list of BI Servers
      tags:
      - server
    post:
      description: POST /server/ creates one or more Virtual BI Servers. Child objects
        for these servers can then be further populated by the API
      operationId: postServers
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Servers'
        required: true
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BulkServerResponse'
          description: Confirmation that servers have been created. Return Count,
            list of server IDs created and Errors if any
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: Create a list of BI Servers
      tags:
      - server
  /server/{server_id}/:
    get:
      description: GET a single BI Server. This method is allowed for Virtual and
        Non-Virtual servers.
      operationId: getServerById
      parameters:
      - $ref: '#/components/parameters/ServerId'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Server'
          description: Requested BI Object
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: GET a BI Server
      tags:
      - server
    patch:
      description: PATCH /server/ will partial update a single BI Server metadata.
        Note that this method is not allowed for Non-Virtual BI Servers.
      operationId: patchServerById
      parameters:
      - $ref: '#/components/parameters/ServerId'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ServerPatch'
        required: true
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Server'
          description: Confirmation that the server has been updated.
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: Update a BI Server
      tags:
      - server
  /server/{server_id}/connection/:
    delete:
      description: 'DELETE a range of connection objects by ID. Note that all bulk
        DELETE methods require a range of IDs. This method is not allowed for non-virtual
        BI Servers. '
      operationId: deleteConnections
      parameters:
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/ObjectIdsRequired'
      - $ref: '#/components/parameters/KeyField'
      responses:
        204:
          description: Successful Delete
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: DELETE a set of connections from a specified BI Server
      tags:
      - connection
    get:
      description: GET a set of connections from a specified BI Server.
      operationId: getConnections
      parameters:
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/ObjectIds'
      - $ref: '#/components/parameters/KeyField'
      - $ref: '#/components/parameters/Limit'
      - $ref: '#/components/parameters/Offset'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Connections'
          description: The requested Connection objects
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: GET a list of connections from a specified BI Server
      tags:
      - connection
    post:
      description: POST /connection/ creates and updates Connection objects via external_id.
        If an object with a matching external_id exists, it is updated with the given
        payload. Otherwise, it is created. Note that this method is not allowed for
        non-virtual servers.
      operationId: postConnections
      parameters:
      - $ref: '#/components/parameters/ServerId'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Connections'
        required: true
      responses:
        202:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JobID'
          description: The JobID of the underlying bulk operation
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: Create/Update a list of connection objects
      tags:
      - connection
  /server/{server_id}/connection/column/:
    delete:
      description: DELETE a range of connection column objects by ID. Note that all
        bulk DELETE methods require a range of IDs. This method is not allowed for
        non-virtual BI Servers.
      operationId: deleteConnectionsColumns
      parameters:
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/ObjectIdsRequired'
      - $ref: '#/components/parameters/KeyField'
      responses:
        204:
          description: Successful Delete
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: DELETE a set of connection columns from a specified BI Server
      tags:
      - connection
    get:
      description: GET a set of connection columns from a specified BI Server
      operationId: getConnectionsColumns
      parameters:
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/ObjectIds'
      - $ref: '#/components/parameters/KeyField'
      - $ref: '#/components/parameters/Limit'
      - $ref: '#/components/parameters/Offset'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConnectionColumns'
          description: A list of Connection Columns
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: GET a set of connection columns from a specified BI Server
      tags:
      - connection
    post:
      description: POST /connection/column/ creates and updates Connection Column
        objects via external_id. If an object with a matching external_id exists,
        it is updated with the given payload. Otherwise, it is created. Note that
        this method is not allowed for non-virtual servers.
      operationId: postConnectionsColumns
      parameters:
      - $ref: '#/components/parameters/ServerId'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConnectionColumns'
        required: true
      responses:
        202:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JobID'
          description: The JobID of the underlying bulk operation
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: Create/Update a list of connection column objects
      tags:
      - connection
  /server/{server_id}/connection/column/{id}/:
    delete:
      description: DELETE /connection/column/{id}/ will delete a single Connection
        Column. This method is not allowed for non-virtual BI Servers.
      operationId: deleteConnectionColumnById
      parameters:
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/ObjectId'
      responses:
        204:
          description: Successful Delete
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: DELETE a connection column
      tags:
      - connection
    get:
      description: GET /connection/column/{id}/ will fetch a single Connection column.
      operationId: getConnectionColumnById
      parameters:
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/ObjectId'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConnectionColumn'
          description: Requested Connection Column Object
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: GET a connection column
      tags:
      - connection
    patch:
      description: PATCH /connection/column/{id}/ will partially update a single Connection
        column. This method is not allowed for non-virtual BI Servers.
      operationId: patchConnectionColumnById
      parameters:
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/ObjectId'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConnectionColumn'
        required: true
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConnectionColumn'
          description: Connection Column Object after successful Patch.
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: Partially update a connection column
      tags:
      - connection
  /server/{server_id}/connection/{id}/:
    delete:
      description: DELETE /connection/{id}/ will delete a single Connection. This
        method is not allowed for non-virtual BI Servers.
      operationId: deleteConnectionById
      parameters:
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/ObjectId'
      responses:
        204:
          description: Successful Delete
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: DELETE a connection
      tags:
      - connection
    get:
      description: GET /connection/{id}/ will return a single Connection.
      operationId: getConnectionById
      parameters:
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/ObjectId'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Connection'
          description: Requested Connection Object
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: GET a connection
      tags:
      - connection
    patch:
      description: PATCH /connection/{id}/ will partially update a single Connection.
        This method is not allowed for non-virtual BI Servers.
      operationId: patchConnectionById
      parameters:
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/ObjectId'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Connection'
        required: true
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Connection'
          description: Connection Object after successful Patch.
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: Partially update a connection
      tags:
      - connection
  /server/{server_id}/datasource/:
    delete:
      description: DELETE a range of datasource objects by ID. Note that all bulk
        DELETE methods require a range of IDs. This method is not allowed for non-virtual
        BI Servers.
      operationId: deleteDatasources
      parameters:
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/ObjectIdsRequired'
      - $ref: '#/components/parameters/KeyField'
      responses:
        204:
          description: Successful Delete
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: DELETE a set of datasources from a specified BI Server
      tags:
      - datasource
    get:
      description: GET a range of datasource objects by ID.
      operationId: getDatasources
      parameters:
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/ObjectIds'
      - $ref: '#/components/parameters/KeyField'
      - $ref: '#/components/parameters/Limit'
      - $ref: '#/components/parameters/Offset'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Datasources'
          description: A list of Datasources
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: GET a set of datasources from a specified BI Server
      tags:
      - datasource
    post:
      description: POST /datasource/ creates and updates Datasource objects via external_id.
        If an object with a matching external_id exists, it is updated with the given
        payload. Otherwise, it is created. Note that this method is not allowed for
        non-virtual servers.
      operationId: postDatasources
      parameters:
      - $ref: '#/components/parameters/ServerId'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Datasources'
        required: true
      responses:
        202:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JobID'
          description: The JobID of the underlying bulk operation
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: Create/Update a list of Datasources
      tags:
      - datasource
  /server/{server_id}/datasource/column/:
    delete:
      description: DELETE a range of datasource column objects by ID. Note that all
        bulk DELETE methods require a range of IDs. This method is not allowed for
        non-virtual BI Servers.
      operationId: deleteDatasourcesColumns
      parameters:
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/ObjectIdsRequired'
      - $ref: '#/components/parameters/KeyField'
      responses:
        204:
          description: Successful Delete
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: DELETE a set of datasource columns from a specified BI Server
      tags:
      - datasource
    get:
      description: GET a range of datasource column objects by ID.
      operationId: getDatasourcesColumns
      parameters:
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/ObjectIds'
      - $ref: '#/components/parameters/KeyField'
      - $ref: '#/components/parameters/Limit'
      - $ref: '#/components/parameters/Offset'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DatasourceColumns'
          description: A list of Datasource Columns
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: GET a set of datasource columns from a specified BI Server
      tags:
      - datasource
    post:
      description: POST /datasource/column/ creates and updates Datasource Column
        objects via external_id. If an object with a matching external_id exists,
        it is updated with the given payload. Otherwise, it is created. Note that
        this method is not allowed for non-virtual servers.
      operationId: postDatasourcesColumns
      parameters:
      - $ref: '#/components/parameters/ServerId'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DatasourceColumns'
        required: true
      responses:
        202:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JobID'
          description: The JobID of the underlying bulk operation
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: Create/Update a list of Datasource Columns
      tags:
      - datasource
  /server/{server_id}/datasource/column/{id}/:
    delete:
      description: DELETE /datasource/column/{id}/ will delete a single Datasource
        Column. This method is not allowed for non-virtual BI Servers.
      operationId: deleteDatasourceColumnById
      parameters:
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/ObjectId'
      responses:
        204:
          description: Successful Delete
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: DELETE a Datasource column
      tags:
      - datasource
    get:
      description: GET /datasource/column/{id}/ will get a single Datasource Column.
      operationId: getDatasourceColumnById
      parameters:
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/ObjectId'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DatasourceColumn'
          description: Requested Datasource Column Object
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: GET a Datasource column
      tags:
      - datasource
    patch:
      description: PATCH /datasource/column/{id}/ will partially update a single Datasource
        Column. This method is not allowed for non-virtual BI Servers.
      operationId: patchDatasourceColumnById
      parameters:
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/ObjectId'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DatasourceColumn'
        required: true
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DatasourceColumn'
          description: Datasource Column Object after successful Patch.
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: Partially update a Datasource column
      tags:
      - datasource
  /server/{server_id}/datasource/{id}/:
    delete:
      description: DELETE /datasource/{id}/ will delete a single Datasource. This
        method is not allowed for non-virtual BI Servers.
      operationId: deleteDatasourceById
      parameters:
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/ObjectId'
      responses:
        204:
          description: Successful Delete
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: DELETE a Datasource
      tags:
      - datasource
    get:
      description: GET /datasource/{id}/ will get a single Datasource.
      operationId: getDatasourceById
      parameters:
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/ObjectId'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Datasource'
          description: Requested Datasource Object
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: GET a Datasource
      tags:
      - datasource
    patch:
      description: PATCH /datasource/{id}/ will partially update a single Datasource.
        This method is not allowed for non-virtual BI Servers.
      operationId: patchDatasourceById
      parameters:
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/ObjectId'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Datasource'
        required: true
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Datasource'
          description: Datasource Object after successful Patch.
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: Partially update a Datasource
      tags:
      - datasource
  /server/{server_id}/folder/:
    delete:
      description: DELETE a range of folder objects by ID. Note that all bulk DELETE
        methods require a range of IDs. This method is not allowed for non-virtual
        BI Servers.
      operationId: deleteFolders
      parameters:
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/ObjectIdsRequired'
      - $ref: '#/components/parameters/KeyField'
      responses:
        204:
          description: Successful Delete
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: DELETE a set of folders from a specified BI Server
      tags:
      - folder
    get:
      description: GET a set of folders from a specified BI Server.
      operationId: getFolders
      parameters:
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/ObjectIds'
      - $ref: '#/components/parameters/KeyField'
      - $ref: '#/components/parameters/Limit'
      - $ref: '#/components/parameters/Offset'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Folders'
          description: A list of Folders
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: GET a list of folders from a specified BI Server
      tags:
      - folder
    post:
      description: POST /folder creates and updates folder objects via external_id.
        If an object with a matching external_id exists, it is updated with the given
        payload. Otherwise, it is created. Note that this method is not allowed for
        non-virtual servers.
      operationId: postFolders
      parameters:
      - $ref: '#/components/parameters/ServerId'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Folders'
        required: true
      responses:
        202:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JobID'
          description: The JobID of the underlying bulk operation
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: Create/Update a list of folder objects
      tags:
      - folder
  /server/{server_id}/folder/{id}/:
    delete:
      description: DELETE /folder/{id}/ will delete a single Folder. This method is
        not allowed for non-virtual BI Servers.
      operationId: deleteFolderById
      parameters:
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/ObjectId'
      responses:
        204:
          description: Successful Delete
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: DELETE a BI folder
      tags:
      - folder
    get:
      description: GET /folder/{id}/ will get a single folder.
      operationId: getFolderById
      parameters:
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/ObjectId'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Folder'
          description: Requested Folder Object
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: GET a BI folder
      tags:
      - folder
    patch:
      description: PATCH /folder/{id}/ will partially update a single Folder. This
        method is not allowed for non-virtual BI Servers.
      operationId: patchFolderById
      parameters:
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/ObjectId'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Folder'
        required: true
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Folder'
          description: Folder Object after successful Patch.
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: Partially update a BI folder
      tags:
      - folder
  /server/{server_id}/permission/:
    delete:
      description: DELETE a range of BI permissions by ID. Note that all bulk DELETE
        methods require a range of IDs. This method is not allowed for non-virtual
        BI Servers.
      operationId: deletePermissions
      parameters:
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/ObjectIdsRequired'
      responses:
        204:
          description: Successful Delete
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: DELETE a set of BI permissions from a specified BI server
      tags:
      - permission
    get:
      description: GET a set of BI permissions from a specified BI Server.
      operationId: getPermissions
      parameters:
      - $ref: '#/components/parameters/ObjectIds'
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/Limit'
      - $ref: '#/components/parameters/Offset'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Permissions'
          description: Requested Permission Object
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: GET a list of BI permissions from a specified BI Server
      tags:
      - permission
    post:
      description: POST /permission/ creates permission objects. If an object with
        matching permission exists there is no change in the permissions, otherwise
        it is created. Note that this method is not allowed for non-virtual servers.
        Note that no fields can be updated via this endpoint.
      operationId: postPermissions
      parameters:
      - $ref: '#/components/parameters/ServerId'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Permissions'
        required: true
      responses:
        202:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JobID'
          description: The JobID of the underlying bulk operation
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: Create/Update a list of BI Permissions
      tags:
      - permission
  /server/{server_id}/permission/{id}/:
    delete:
      description: DELETE /permission/{id}/ will delete a single BI Permission object.
        This method is not allowed for non-virtual BI Servers
      operationId: deletePermissionById
      parameters:
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/ObjectId'
      responses:
        204:
          description: Successful Delete
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: DELETE a BI permission
      tags:
      - permission
    get:
      description: GET /permission/{id}/ will get a single BI Permission
      operationId: getPermissionById
      parameters:
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/ObjectId'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Permission'
          description: The requested object
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: GET a BI permission
      tags:
      - permission
  /server/{server_id}/report/:
    delete:
      description: DELETE a range of report objects by ID. Note that all bulk DELETE
        methods require a range of IDs. This method is not allowed for non-virtual
        BI Servers.
      operationId: deleteReports
      parameters:
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/ObjectIdsRequired'
      - $ref: '#/components/parameters/KeyField'
      responses:
        204:
          description: Successful Delete
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: DELETE a set of reports from a specified BI Server
      tags:
      - report
    get:
      description: GET a range of report objects by ID.
      operationId: getReports
      parameters:
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/ObjectIds'
      - $ref: '#/components/parameters/KeyField'
      - $ref: '#/components/parameters/Limit'
      - $ref: '#/components/parameters/Offset'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Reports'
          description: A list of Connection reports
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: GET a list of reports from a specified BI Server
      tags:
      - report
    post:
      description: POST /report creates and updates Report objects via external_id.
        If an object with a matching external_id exists, it is updated with the given
        payload. Otherwise, it is created. Note that this method is not allowed for
        non-virtual servers.
      operationId: postReports
      parameters:
      - $ref: '#/components/parameters/ServerId'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Reports'
        required: true
      responses:
        202:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JobID'
          description: The JobID of the underlying bulk operation
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: Create/Update a list of report objects
      tags:
      - report
  /server/{server_id}/report/column/:
    delete:
      description: DELETE a range of report column objects by ID. Note that all bulk
        DELETE methods require a range of IDs. This method is not allowed for non-virtual
        BI Servers.
      operationId: deleteReportsColumns
      parameters:
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/ObjectIdsRequired'
      - $ref: '#/components/parameters/KeyField'
      responses:
        204:
          description: Successful Delete
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: DELETE a set of report columns from a specified BI Server
      tags:
      - report
    get:
      description: GET a range of report column objects by ID.
      operationId: getReportsColumns
      parameters:
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/ObjectIds'
      - $ref: '#/components/parameters/KeyField'
      - $ref: '#/components/parameters/Limit'
      - $ref: '#/components/parameters/Offset'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReportColumns'
          description: A list of Connection report columns
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: GET a set of report columns from a specified BI Server
      tags:
      - report
    post:
      description: POST /report/column/ creates and updates Report Column objects
        via external_id. If an object with a matching external_id exists, it is updated
        with the given payload. Otherwise, it is created. Note that this method is
        not allowed for non-virtual servers.
      operationId: postReportsColumns
      parameters:
      - $ref: '#/components/parameters/ServerId'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReportColumns'
        required: true
      responses:
        202:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JobID'
          description: The JobID of the underlying bulk operation
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: Create/Update a list of report column objects
      tags:
      - report
  /server/{server_id}/report/column/{id}/:
    delete:
      description: DELETE /report/column/{id}/ will delete a single Report Column.
        This method is not allowed for non-virtual BI Servers.
      operationId: deleteReportColumnById
      parameters:
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/ObjectId'
      responses:
        204:
          description: Successful Delete
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: DELETE a Report column
      tags:
      - report
    get:
      description: GET /report/column/{id}/ will get a single Report Column.
      operationId: getReportColumnById
      parameters:
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/ObjectId'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReportColumn'
          description: Requested Report Column Object
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: GET a Report column
      tags:
      - report
    patch:
      description: PATCH /report/column/{id}/ will partially update a single Report
        Column. This method is not allowed for non-virtual BI Servers.
      operationId: patchReportColumnById
      parameters:
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/ObjectId'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReportColumn'
        required: true
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReportColumn'
          description: Report Column Object after successful Patch.
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: Partially update a Report column
      tags:
      - report
  /server/{server_id}/report/{id}/:
    delete:
      description: DELETE /report/{id}/ will delete a single Report. This method is
        not allowed for non-virtual BI Servers.
      operationId: deleteReportById
      parameters:
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/ObjectId'
      responses:
        204:
          description: Successful Delete
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: DELETE a Report
      tags:
      - report
    get:
      description: GET /report/{id}/ will get a single Report.
      operationId: getReportById
      parameters:
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/ObjectId'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Report'
          description: Requested Report Object
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: GET a Report
      tags:
      - report
    patch:
      description: PATCH /report/{id}/ will partially update a single Report. This
        method is not allowed for non-virtual BI Servers.
      operationId: patchReportById
      parameters:
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/ObjectId'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Report'
        required: true
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Report'
          description: Report Object after successful Patch.
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: Partially update a Report
      tags:
      - report
  /server/{server_id}/report/{id}/image/:
    delete:
      description: DELETE an image associated with a BI object from a BI Server
      operationId: deleteImageById
      parameters:
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/ObjectId'
      responses:
        204:
          description: Successful Delete
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: DELETE the current image associated to this object using imagetype
        query
      tags:
      - images
    get:
      description: GET (view) the current image associated to this object using imagetype
        query
      operationId: getImageById
      parameters:
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/ObjectId'
      responses:
        200:
          $ref: '#/components/responses/Image_200_Response'
      summary: GET an image associated with a BI object from a BI Server
      tags:
      - images
    post:
      description: Add/Replace the image(s) associated to this object
      operationId: postImageById
      parameters:
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/ObjectId'
      requestBody:
        $ref: '#/components/requestBodies/Image_Request_Body'
      responses:
        204:
          description: Image has been associated to the object successfully
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: POST an image associated with a BI object from a BI Server
      tags:
      - images
  /server/{server_id}/user/:
    delete:
      description: DELETE a range of BI users by ID. Note that all bulk DELETE methods
        require a range of IDs. This method is not allowed for non-virtual BI Servers.
      operationId: deleteUsers
      parameters:
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/ObjectIdsRequired'
      - $ref: '#/components/parameters/KeyField'
      responses:
        204:
          description: Successful Delete
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: DELETE a set of BI users from a specified BI Server
      tags:
      - user
    get:
      description: GET a set of BI users from a specified BI Server.
      operationId: getUsers
      parameters:
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/ObjectIds'
      - $ref: '#/components/parameters/KeyField'
      - $ref: '#/components/parameters/Limit'
      - $ref: '#/components/parameters/Offset'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Users'
          description: The requested user object
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: GET a list of BI users from a specified BI Server
      tags:
      - user
    post:
      description: POST /user/ creates or updates BI user objects via external_id.
        If an object with a matching external_id exists, it is updated with the given
        payload. Otherwise, it is created. Note that this method is not allowed for
        non-virtual servers. A corresponding Alation user should exist for successfully
        creating or updating a BI User.
      operationId: postUsers
      parameters:
      - $ref: '#/components/parameters/ServerId'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Users'
        required: true
      responses:
        202:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JobID'
          description: The JobID of the underlying bulk operation
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: Create/Update a list of BI users
      tags:
      - user
  /server/{server_id}/user/{id}/:
    delete:
      description: DELETE /user/{id}/ will delete a BI user. This method is not allowed
        for non-virtual BI Servers
      operationId: deleteUserById
      parameters:
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/ObjectId'
      responses:
        204:
          description: Successful Delete
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: DELETE a BI user
      tags:
      - user
    get:
      description: GET /user/{id}/ will get a single BI User.
      operationId: getUserById
      parameters:
      - $ref: '#/components/parameters/ServerId'
      - $ref: '#/components/parameters/ObjectId'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: The requested object.
        400: *id001
        401: *id002
        403: *id003
        404: *id004
        500: *id005
      summary: GET a BI user
      tags:
      - user
security:
- ApiKeyAuth: []
servers:
- url: '{protocol}://{base-url}/integration/v2/bi'
  variables:
    base-url:
      default: localhost
      description: Django BASE_URL setting for this instance.
    protocol:
      default: http
      enum:
      - http
      - https
tags:
- description: The Parent object for all BI objects. Provides access to creating,
    updating and reading BI servers
  name: server
- description: BI Folder/Dashboard/Containers. Create/Read/Update/Delete for BI objects
    that contain other objects
  name: folder
- description: Connections represent how data is gathered for the BI Source. Connection
    columns represent transformations that are used to populate reports via connections.
  name: connection
- description: Reports represent a display of data within a BI object. Report Columns
    are transformations on data used to populate reports
  name: report
- description: A Datasource represents a physical source of BI Data. Datasource Columns
    are transformations on data used to populate reports via these sources
  name: datasource
- description: The Permissions for all BI objects. Provides access to creating, updating
    and deleting Permission for BI objects
  name: permission
- description: Images associated with BI objects. Provides access to creating, fetching
    and deleting Images
  name: images
- description: User object represents a user of the BI tool. Provides access to creating,
    updating and deleting BI Users
  name: user


components:
  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: TOKEN
      description: API Key for the user

  schemas:
    Server:
      description: Properties of a Server Object
      type: object
      properties:
        id:
          type: integer
          readOnly: true
          description: The Numeric ID of the server
        uri:
          type: string
          description: The uri of the underlying BI server
          nullable: true
        title:
          type: string
          description: The title of the underlying BI server
          nullable: true
        description:
          type: string
          description: The description of the underlying BI server
          nullable: true
        name_configuration:
          type: object
          description: >
            Key-Value pairs matching BI object names to new user defined names. Keys supported
            ['bi_report', 'bi_datasource', 'bi_folder', 'bi_connection']. Values are the renamed
            user defined versions.
          additionalProperties:
            type: string
      required:
        - uri
        - title

    ServerPatch:
      description: Properties of a Server Object
      type: object
      properties:
        id:
          type: integer
          readOnly: true
          description: The Numeric ID of the server
        uri:
          type: string
          description: The uri of the underlying BI server
          nullable: true
        title:
          type: string
          description: The title of the underlying BI server
          nullable: true
        description:
          type: string
          description: The description of the underlying BI server
          nullable: true
        name_configuration:
          type: object
          description: >
            Key-Value pairs matching BI object names to new user defined names. Keys supported
            ['bi_report', 'bi_datasource', 'bi_folder', 'bi_connection']. Values are the renamed
            user defined versions.
          additionalProperties:
            type: string
      required:
        - uri

    Servers:
      type: array
      items:
        $ref: "#/components/schemas/Server"

    BIObjectBase:
      type: object
      description: Common properties of a BI Object
      properties:
        id:
          type: integer
          readOnly: true
          description: The auto-generated id of the object
        name:
          type: string
          description: The user-friendly name of the object
        external_id:
          description: >
            The identifier of the object as generated by the BI Server. This is used as an
            identifier for bulk create/update operations.
          type: string
        created_at:
          type: string
          description: Date and time of object creation on the BI Server
          format: date-time
        last_updated:
          type: string
          description: Most recent update time of the object on the BI Server
          format: date-time
        source_url:
          type: string
          description: Path to the object on the BI Server
        bi_object_type:
          type: string
          description: The type of the object, as defined by the BI Server
        description_at_source:
          type: string
          description: Object description on the BI Server
      required:
        - id
        - external_id
        - name
        - source_url
        - bi_object_type
        - description_at_source

    BIColumnObjectBase:
      description: Common properties of a BI Column Object
      allOf:
        - $ref: "#/components/schemas/BIObjectBase"
        - type: object
          properties:
            data_type:
              type: string
              maxLength: 256
              nullable: true
              description: The type of the column data.
            role:
              type: string
              nullable: true
              description: The role of the column.
            expression:
              type: string
              nullable: true
              description: The expression used to transform the data into a column
            values:
              type: array
              nullable: true
              items:
                type: string
              description: Sample values from the column

    Folder:
      description: Properties of a Folder Object
      allOf:
        - $ref: "#/components/schemas/BIObjectBase"
        - type: object
          required: [owner]
          properties:
            owner:
              type: string
              description: Username of the folder owner
            num_reports:
              type: integer
              description: Count of reports contained within the folder
            num_report_accesses:
              type: integer
              description: Sum total of accesses of reports contained within the folder
            popularity:
              type: number
              readOnly: true
              description: Popularity index based partially on num_reports and num_report_accesses
            parent_folder:
              type: string
              nullable: true
              description: >
                external_id of the object's parent folder. To properly update, the parent must
                either exist on Alation or be contained in the POST request before the child.
            subfolders:
              type: array
              items:
                type: string
              uniqueItems: true
              readOnly: true
              description: external_ids of the object's subfolders.
            connections:
              type: array
              items:
                type: string
              uniqueItems: true
              readOnly: true
              description: external_ids of the connections within the folder.
            reports:
              type: array
              items:
                type: string
              uniqueItems: true
              readOnly: true
              description: external_ids of the reports within the folder.

    Folders:
      type: array
      items:
        $ref: "#/components/schemas/Folder"

    Connection:
      description: Properties of a Connection Object
      allOf:
        - $ref: "#/components/schemas/BIObjectBase"
        - type: object
          required: [display_connection_type, connection_type, host, database_type]
          properties:
            connection_type:
              type: string
              enum:
                - FILES
                - TABLE
                - SQL_QUERY
                - OTHER
              description: How the connection is gathering data.
            display_connection_type:
              type: string
              description: A user-friendly string conveying how the connection is gathering data
            host:
              type: string
              nullable: true
              description: The hostname used to build the connection
            port:
              type: integer
              description: The port used to build the connection
            database_type:
              type: string
              nullable: true
              description: "For TABLE and SQL_QUERY: The type of database being accessed"
            db_schema:
              type: string
              nullable: true
              description: "For TABLE and SQL_QUERY: The schema name"
            db_table:
              type: string
              nullable: true
              description: "For TABLE and SQL_QUERY: The table name"
            sql:
              type: string
              nullable: true
              description: "For SQL_QUERY: The sql query"
            data_files:
              type: array
              items:
                type: string
              nullable: true
              description: "For FILES: The file paths"
            parent_folder:
              type: string
              nullable: true
              description: >
                external_id of the parent folder. Note that the folder must exist on Alation to
                properly update this
            connection_columns:
              type: array
              items:
                type: string
              uniqueItems: true
              readOnly: true
              description: external_id of the columns derived from this connection
            datasources:
              type: array
              items:
                type: string
              uniqueItems: true
              readOnly: true
              description: external_id of the datasource associated with this connection

    Connections:
      type: array
      items:
        $ref: "#/components/schemas/Connection"

    ConnectionColumn:
      description: Properties of a Connection Column Object
      allOf:
        - $ref: "#/components/schemas/BIColumnObjectBase"
        - type: object
          required: [data_type, role, expression, values]
          properties:
            connection:
              type: string
              nullable: true
              description: >
                external_id of the parent connection. Note that the connection must exist on
                Alation to properly update this

    ConnectionColumns:
      type: array
      items:
        $ref: "#/components/schemas/ConnectionColumn"

    Datasource:
      description: Properties of a Datasource Object
      allOf:
        - $ref: "#/components/schemas/BIObjectBase"
        - type: object
          properties:
            parent_folder:
              type: string
              nullable: true
              description: >
                external_id of the parent folder. Note that the folder must exist on Alation to
                properly update this
            parent_connections:
              type: array
              items:
                type: string
              uniqueItems: true
              description: >
                external_ids of all parent connections. Note that the connections must exist on
                Alation to properly update this
            datasource_columns:
              type: array
              items:
                type: string
              uniqueItems: true
              readOnly: true
              description: external_ids of all columns derived from this datasource."
            derived_reports:
              type: array
              items:
                type: string
              uniqueItems: true
              readOnly: true
              description: external_ids of all reports derived from this datasource."

    Datasources:
      type: array
      items:
        $ref: "#/components/schemas/Datasource"

    DatasourceColumn:
      description: Properties of a Datasource Column Object
      allOf:
        - $ref: "#/components/schemas/BIColumnObjectBase"
        - type: object
          required: [data_type, role, expression, values]
          properties:
            datasource:
              type: string
              nullable: true
              description: >
                external_id of the parent datasource. Note that the datasource must exist on
                Alation to properly update this
            parent_connection_columns:
              type: array
              items:
                type: string
              uniqueItems: true
              description: >
                external_id of the parent connection columns. Note that the columns must exist on
                Alation to properly update this
            parent_datasource_columns:
              type: array
              items:
                type: string
              uniqueItems: true
              description: >
                external_id of the parent datasource columns. Note that the columns must exist on
                Alation, or exist earlier in the POST payload, to properly update this
            derived_datasource_columns:
              type: array
              items:
                type: string
              uniqueItems: true
              readOnly: true
              description: external_id of the datasource columns derived from ths one
            derived_report_columns:
              type: array
              items:
                type: string
              uniqueItems: true
              readOnly: true
              description: external_id of the report columns derived from ths one

    DatasourceColumns:
      type: array
      items:
        $ref: "#/components/schemas/DatasourceColumn"

    Report:
      description: Properties of a Report Object
      allOf:
        - $ref: "#/components/schemas/BIObjectBase"
        - type: object
          required: [report_type, owner]
          properties:
            report_type:
              type: string
              enum:
                - SIMPLE
                - DASHBOARD
              nullable: true
              description: >
                The type of report, whether it is a simple chart or a dashboard of reports.
            owner:
              type: string
              description: Username of the report's owner
            num_accesses:
              type: integer
              nullable: true
              description: Number of times the report has been accessed on the BI Server
            popularity:
              type: number
              nullable: true
              description: Index of popularity based partially on num_accesses
            parent_folder:
              type: string
              nullable: true
              description: >
                external_id of the parent folder. Note that the folder must exist on Alation to
                properly update this
            parent_reports:
              type: array
              items:
                type: string
              uniqueItems: true
              nullable: true
              description: >
                external_id of the parent reports. Note that the reports must exist on Alation, or
                occur earlier in the POST payload, to properly update this
            sub_reports:
              type: array
              items:
                type: string
              uniqueItems: true
              nullable: true
              readOnly: true
              description: external_id of all subreports
            parent_datasources:
              type: array
              items:
                type: string
              uniqueItems: true
              nullable: true
              description: >
                external_id of the parent datasources. Note that the reports must exist on Alation,
                or occur earlier in the POST payload, to properly update this
            report_columns:
              type: array
              items:
                type: string
              uniqueItems: true
              readOnly: true
              description: external_id of all contained report columns

    Reports:
      type: array
      items:
        $ref: "#/components/schemas/Report"

    ReportColumn:
      description: Properties of a Report Column Object
      allOf:
        - $ref: "#/components/schemas/BIColumnObjectBase"
        - type: object
          required: [data_type, role, expression, values]
          properties:
            report:
              type: string
              nullable: true
              description: >
                external_id of the parent report. Note that the report must exist on Alation to
                properly update this
            parent_datasource_columns:
              type: array
              items:
                type: string
              uniqueItems: true
              description: >
                external_id of the parent datasource columns. Note that the columns must exist on
                Alation to properly update this
            parent_report_columns:
              type: array
              items:
                type: string
              uniqueItems: true
              description: >
                external_id of the parent report columns. Note that the columns must exist on
                Alation, or occur earlier in the POST payload, to properly update this
            derived_report_columns:
              type: array
              items:
                type: string
              uniqueItems: true
              readOnly: true
              description: external_id of all report columns derived from this one.

    ReportColumns:
      type: array
      items:
        $ref: "#/components/schemas/ReportColumn"

    User:
      description: Properties of a User Object
      allOf:
        - $ref: "#/components/schemas/BIObjectBase"
        - type: object
          properties:
            username:
              type: string
              description: The name of the user of the BI Tool
            domain_name:
              type: string
              description: The domanin name of the user

    Users:
      type: array
      items:
        $ref: "#/components/schemas/User"

    Permission:
      description: Properties of a Permission Object
      type: object
      properties:
        id:
          type: integer
          readOnly: true
          description: The auto-generated id of the object
        object_otype:
          type: string
          description: The otype of the Permission object
          enum:
            - bi_report
            - bi_datasource
            - bi_folder
            - bi_connection
        object_external_id:
          type: string
          description: The external id of the Permission object
        user_external_id:
          description: The external id of the user associated.
          type: string

    Permissions:
      type: array
      items:
        $ref: "#/components/schemas/Permission"

    JobID:
      description: Properties of a Job Object
      type: object
      properties:
        job_id:
          type: integer
          description: >
            GET /api/v1/bulk_metadata/job/?id={job_id} will return the status of the underlying
            POST job

    BulkServerResponse:
      title: Server Patch Response
      description: Properties of a Bulk Server Response Object
      type: object
      properties:
        Status:
          type: string
          description: Status message
        Count:
          type: integer
          description: >
            Number of successful objects updated, use this field for enumerating over Server IDs
            and Errors for further action
          format: int32
        Server IDs:
          type: array
          items:
            type: integer
            format: int32
          description: >
            List of server ids that were successfully updated. None indicates error in the object
            that was not updated.
        Errors:
          type: array
          items:
            type: string
          description: List of errors if any. None indicates no error

    KeyField:
      title: KeyField Enum
      description: Field to use as an identifier of the resource (id, or external_id)
      type: string
      enum:
        - id
        - external_id
      default: id

    Error:
      type: object
      description: Properties of a Error Object
      properties:
        code:
          type: string
          description: A six digit code that identifies the problem. Refer the error documentation for more information.
        title:
          type: string
          description: The title of the error message
        detail:
          type: string
          description: More information about the error
      required:
        - code
        - title
        - detail

    Image:
      type: object
      description: Properties of a Image Object
      properties:
        image:
          type: string
          description: Upload a PNG image
          format: binary

  requestBodies:
    Image_Request_Body:
      description: Image to be Uploaded
      required: true
      content:
        multipart/form-data:
          schema:
            $ref: '#/components/schemas/Image'
        image/png:
          schema:
            $ref: '#/components/schemas/Image'

  responses:
    Image_200_Response:
      description: The object's current Image
      content:
        image/png:
          schema:
            $ref: '#/components/schemas/Image'

    Standard_400_Error_Response:
      description: Malformed Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "400XXX"
            title: "Malformed Request"
            detail: "The request sent by user is in the wrong format. (Refer the error documentation for specific details of the error)"

    Standard_401_Error_Response:
      description: Unauthorized bad/missing token
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "401XXX"
            title: "Unauthorized"
            detail: "You are unauthorized to access this resource. Please obtain valid credentials. (Refer the error documentation for specific details of the error)"
    Standard_403_Error_Response:
      description: Forbidden User cannot edit this resource
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "403XXX"
            title: "Forbidden Action"
            detail: "This is forbidden to access. Make sure you access the right resource. (Refer the error documentation for specific details of the error)"
    Standard_404_Error_Response:
      description: The specified resource was not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "404XXX"
            title: "Resource not found"
            detail: "This is not a valid resource. Please try something else. (Refer the error documentation for specific details of the error)"
    Standard_500_Error_Response:
      description: Internal Server Error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "500XXX"
            title: "Internal Server Error"
            detail: "Something went wrong, Please try again later. (Refer the error documentation for specific details of the error)"

  parameters:
    ObjectIds:
      name: oids
      description: List of object ids to be used
      explode: false
      in: query
      required: false
      schema:
        items:
          anyOf:
            - type: integer
            - type: string
        type: array
      examples:
        oids_value:
          value: [1,2]
          summary: A sample list of object id's

    ObjectIdsRequired:
      name: oids
      description: List of object ids to be used
      explode: false
      in: query
      required: true
      schema:
        items:
          anyOf:
            - type: integer
            - type: string
        type: array
      examples:
        oids_value:
          value: [1,2]
          summary: A sample list of object id's

    KeyField:
      description: Field to use as an identifier of the resource (id, or external_id)
      in: query
      name: keyField
      required: false
      schema:
        $ref: "#/components/schemas/KeyField"

    ServerId:
      description: ID of the containing server
      in: path
      name: server_id
      required: true
      schema:
        type: integer
      examples:
        ids:
          value: 1001
          summary: A sample integer server id

    ObjectId:
      description: The ID of the object
      in: path
      name: id
      required: true
      schema:
        type: integer
      examples:
        ids:
          value: 123
          summary: A sample integer object id

    Limit:
      description: >
        Specifies the number of objects to be fetched in one paginated request.
      in: query
      name: limit
      required: false
      schema:
        type: integer
        default: 100

    Offset:
      description: >
        Specifies the number of objects to be skipped in one request. Used together with
        `Limit` parameter to fetch paginated requests.
      in: query
      name: offset
      required: false
      schema:
        type: integer
        default: 0
