info:
  description: Public API for working with workflows and requests
  title: Workflows API
  version: 1.0.0
openapi: 3.0.0
paths:
  /workflows/:
    get:
      description: List all accessible workflows based on alation config
      operationId: listWorkflows
      parameters:
      - $ref: common/parameters.yaml#/components/parameters/ItemSkip
      - $ref: common/parameters.yaml#/components/parameters/PageLimit
      - $ref: '#/components/parameters/status'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Workflows'
          description: List Workflows
        400:
          $ref: common/responses.yaml#/components/responses/Standard_400_Error_Response
        401:
          $ref: common/responses.yaml#/components/responses/Standard_401_Error_Response
        403:
          $ref: common/responses.yaml#/components/responses/Standard_403_Error_Response
        404:
          $ref: common/responses.yaml#/components/responses/Standard_404_Error_Response
        500:
          $ref: common/responses.yaml#/components/responses/Standard_500_Error_Response
      summary: List workflows
      tags:
      - workflows
security:
- ApiKeyAuth: []
servers:
- url: '{protocol}://{base-url}/integration/v2'
  variables:
    base-url:
      default: localhost
      description: BASE_URL setting for this instance.
    protocol:
      default: http
      enum:
      - http
      - https
tags:
- description: A workflow allows you to automate actions related to data governance
    in Alation.
  name: workflows


components:
  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: TOKEN
      description: API Key for the user

  schemas:
    Workflows:
      description: A collection of workflows
      type: array
      items:
        $ref: "#/components/schemas/Workflow"
      example:
        - id: 1
          title: "CR - Workflow on all glossaries"
          type: "CHANGE_REQUEST"
          state: "PUBLISHED"
          state_info: ""
          status: "PUBLISHED"
          rule:
            subjects:
              - object_type: "glossary_v3"
                object_id: null
                scope: ["glossary_term"]
                subject_excluded: false
            objects_excluded:
              - object_type: "glossary_v3"
                object_id: 3
              - object_type: "glossary_term"
                object_id: 15
            type: "change_request"
          settings: null
          steps:
            - id: 28
              settings:
                review_settings:
                  required_approving_review_count: null
                  require_all_reviewers_approval: true
              reviewers:
                - rtype_name: "USER"
                  rid: 36
                - rtype_name: "GROUPPROFILE"
                  rid: 5
              ordinal: 1
              next_step: 29
              url: "/workflows/1/steps/28"
            - id: 29
              settings:
                review_settings:
                  required_approving_review_count: 1
                  require_all_reviewers_approval: false
              reviewers:
                - rtype_name: "USER"
                  rid: 37
                - rtype_name: "people_set"
                  rid: 8
              ordinal: 2
              next_step: null
              url: "/workflows/1/steps/29"
          start_step: 28
          is_reviewer: false
          can_bypass: true
          creator_id: 1
          last_updator_id: 1
          ts_created: "2023-02-01T07:45:46.236895Z"
          ts_updated: "2023-03-15T03:56:38.315992Z"
          url: "/workflows/1/"
        - id: 2
          title: "AR - Workflow on Retail Glossary"
          type: "ADD_REQUEST"
          state: "PUBLISHED"
          state_info: ""
          status: "PUBLISHED"
          rule:
            otypes:
              - "glossary_term"
            to_subjects:
              - object_type: "glossary_v3"
                object_id: 1
            objects_excluded: []
            type: "add_request"
          settings: null
          steps:
            - id: 2
              settings:
                review_settings:
                  required_approving_review_count: 2
                  require_all_reviewers_approval: false
              reviewers:
                - rtype_name: "USER"
                  rid: 36
                - rtype_name: "USER"
                  rid: 32
                - rtype_name: "GROUPPROFILE"
                  rid: 6
              ordinal: 1
              next_step: null
              url: "/workflows/2/steps/2"
          start_step: 2
          is_reviewer: true
          can_bypass: true
          creator_id: 2
          last_updator_id: 1
          ts_created: "2023-02-01T07:46:21.601914Z"
          ts_updated: "2023-03-11T10:48:53.855166Z"
          url: "/workflows/2/"
        - id: 32
          title: "Workflow on datasources title"
          type: "CHANGE_REQUEST"
          state: "PUBLISHED"
          state_info: ""
          status: "PUBLISHED"
          rule:
            subjects:
              - object_type: "data"
                object_id: 1
                scope: ["schema", "table", "attribute"]
                subject_excluded: false
              - object_type: "data"
                object_id: 2
                scope: ["schema"]
                subject_excluded: true
            fields:
              - otype: "custom_field"
                oid: 3
            objects_excluded:
              - object_type: "schema"
                object_id: 11
              - object_type: "table"
                object_id: 22
            type: "change_request"
          settings: null
          steps:
            - id: 29
              settings:
                review_settings:
                  required_approving_review_count: 1
                  require_all_reviewers_approval: false
              reviewers:
                - rtype_name: "USER"
                  rid: 36
                - rtype_name: "USER"
                  rid: 37
              ordinal: 1
              next_step: null
              url: "/workflows/32/steps/29"
          start_step: 29
          is_reviewer: false
          can_bypass: true
          creator_id: 1
          last_updator_id: 3
          ts_created: "2023-03-20T06:18:55.666669Z"
          ts_updated: "2023-03-20T06:18:55.666719Z"
          url: "/workflows/32/"

    Workflow:
      description: Workflow resource
      type: object
      required:
        - title
        - type
        - rule
        - steps
      properties:
        id:
          description: ID of Workflow
          type: integer
          readOnly: true
          example: 1
        title:
          description: Unique title of Workflow
          type: string
          example: Change request flow on specific data sources
        type:
          $ref: "#/components/schemas/Workflow_type"
        state:
          description: Workflow state
          type: string
          readOnly: true
          example: PUBLISHED
          enum:
            - PUBLISHED
            - UNPUBLISHED
            - CLEANING
            - DELETED
            - ERROR
        state_info:
          description: An optional state information string of Workflow
          type: string
          readOnly: true
          example: "Encountered a flaky error during publishing"
        status:
          description: A more human-understandable Workflow state compared to `state`
          type: string
          readOnly: true
          example: PUBLISHED
          enum:
            - PUBLISHED
            - UNPUBLISHED
            - WORKING
            - DELETED
            - FAILED
        rule:
          oneOf:
            - $ref: "#/components/schemas/ChangeRequest_Workflow_rule"
            - $ref: "#/components/schemas/AddRequest_Workflow_rule"
        steps:
          description: review steps of Workflow
          type: array
          items:
            $ref: "#/components/schemas/Workflow_step"
        start_step:
          description: ID of step to start with for the workflow
          type: number
          readOnly: true
          example: 1
        is_reviewer:
          description: if the invoking user is a reviewer, in any step, of workflow
          type: boolean
          readOnly: true
          example: false
        can_bypass:
          description: if the invoking user can bypass the review flow of workflow
          type: boolean
          readOnly: true
          example: true
        creator_id:
          description: ID of user who created the workflow
          type: number
          example: 8
        last_updator_id:
          description: ID of user who last modified the workflow
          type: number
          example: 9
        ts_created:
          description: Timestamp when the workflow was created
          type: string
          format: date-time
          example: "2023-02-01T07:45:46.236895Z"
        ts_updated:
          description: Timestamp when the workflow was last updated
          type: string
          format: date-time
          example: "2023-03-15T03:56:38.315992Z"

    ChangeRequest_Workflow_rule:
      description: JSON based rule for scoping the objects under change request Workflow
      type: object
      properties:
        subjects:
          description: collection of top level objects under Workflow influence
          type: array
          items:
            $ref: "#/components/schemas/ChangeRequest_Workflow_rule_subject"
        fields:
          description: collection of fields under Workflow influence
          type: array
          items:
            $ref: "#/components/schemas/ChangeRequest_Workflow_rule_field"
          nullable: true
          minItems: 1
        type:        
          enum:
            - change_request
          readOnly: true
        objects_excluded:
          description: >
            collection of top level objects excluded from Workflow influence. For any excluded
            object in the collection, all its child objects are also exlcuded from the workflow
            influence.
          type: array
          items:
            $ref: "#/components/schemas/ObjectKey"
      required:
        - subjects
      example:
        subjects:
          - object_type: "data"
            object_id: 1
            scope: ["schema", "table", "attribute"]
            subject_excluded: false
          - object_type: "data"
            object_id: 2
            scope: ["schema"]
            subject_excluded: true
        objects_excluded:
          - object_type: "schema"
            object_id: 11
          - object_type: "table"
            object_id: 22
        type: "change_request"

    AddRequest_Workflow_rule:
      description: JSON based rule for scoping the objects under add request Workflow
      type: object
      properties:
        otypes:
          description: >
            object types whose association (under collection type object) or creation gets
            influenced by the add request Workflow
          type: array
          items:
            allOf:
              - $ref: "#/components/schemas/otype"
              - description: only `glossary_term` supported
        to_subjects:
          description: >
            list of collection type objects under which new and existing `otypes` object's
            inclusion gets influenced by the add request Workflow
          type: array
          items:
            $ref: "#/components/schemas/AddRequest_Workflow_rule_subject"
        type:        
          enum:
            - add_request
          readOnly: true
        objects_excluded:
          description: >
            collection of objects excluded from Workflow influence.
          type: array
          items:
            $ref: "#/components/schemas/ObjectKey"
      required:
        - otypes
        - to_subjects
      example:
        otypes:
          - glossary_term
        to_subjects:
          - object_type: "glossary_v3"
            object_id: null
        objects_excluded:
          - object_type: "glossary_v3"
            object_id: 10
          - object_type: "glossary_v3"
            object_id: 11
        type: "add_request"

    Workflow_step:
      description: Workflow review step related information and settings
      type: object
      properties:
        id:
          description: ID of Workflow step
          type: number
          readOnly: true
          example: 1
        settings:
          description: step settings
          type: object
          properties:
            review_settings:
              description: review settings of workflow step
              type: object
              properties:
                require_all_reviewers_approval:
                  description: >
                    if step requires approval from all reviewers to proceed to next
                    configured step
                  type: boolean
                required_approving_review_count:
                  description: >
                    no. approvals from reviewers required to proceed to next configured step
                  type: number
                  exclusiveMinimum: false
                  minimum: 1
                  maximum: 9
          required:
            - review_settings
        reviewers:
          description: collection of Workflow step reviewers
          type: array
          items:
            $ref: "#/components/schemas/Workflow_step_reviewer"
          minItems: 1
        ordinal:
          description: ordinal value of step
          type: number
          minimum: 1
        next_step:
          description: ID of subsequent step to start with of the workflow
          type: number
          readOnly: true
          nullable: true
          example: 2
      required:
        - settings
        - reviewers
        - ordinal
      example:
        id: 1
        settings:
          review_settings:
            required_approving_review_count: 2
            require_all_reviewers_approval: false
        reviewers:
          - rtype_name: "user"
            rid: 4
          - rtype_name: "groupprofile"
            rid: 5
          - rtype_name: "people_set"
            rid: 8
        ordinal: 1
        next_step: null

    ChangeRequest_Workflow_rule_subject:
      description: >
        top level object along with its underneath scope of object types included within the span
        of this workflow. This is relevant for change request Workflow.
      type: object
      properties:
        object_type:
          allOf:
            - $ref: "#/components/schemas/otype"
            - description: type of object as subject
        object_id:
          allOf:
            - $ref: "#/components/schemas/oid"
            - description: ID of object as subject
        scope:
          description: >
            collection of descendant object types of the subject included in Workflow span
          type: array
          items:
            $ref: "#/components/schemas/otype"
        subject_excluded:
          description: if subject itself is excluded from the workflow span
          type: boolean

    ChangeRequest_Workflow_rule_field:
      description: >
        custom fields and object catalog page defined fields of subjects included in workflow
        span. This makes the workflow effective on fields of subjects as specified.
      type: object
      properties:
        otype:
          description: type of field
          type: string
          enum:
            - custom_field
          example: custom_field
        oid:
          description: ID of field
          type: number
          example: 3

    AddRequest_Workflow_rule_subject:
      description: >
        collection type object under which new and existing `otypes` object's inclusion gets
        influenced by the workflow. This is relevant for add request Workflow.
      type: object
      properties:
        object_type:
          allOf:
            - $ref: "#/components/schemas/otype"
            - description: type of object as collection type subject. Only `glossary_v3` supported
        object_id:
          allOf:
            - $ref: "#/components/schemas/oid"
            - description: ID of object as collection type subject

    Workflow_step_reviewer:
      description: Workflow step level reviewer
      type: object
      properties:
        rtype_name:
          description: type of reviewer
          type: string
          enum:
            - user
            - groupprofile
            - people_set
          example: user
        rid:
          description: ID of reviewer
          type: number
          example: 4
      required:
        - rtype_name
        - rid

    Workflow_type:
      description: Workflow type
      type: string
      enum:
        - change_request
        - add_request
      example: change_request

    ObjectKey:
      type: object
      properties:
        object_type:
          $ref: "#/components/schemas/otype"
        object_id:
          $ref: "#/components/schemas/oid"

    otype:
      description: type of object
      type: string
      enum:
        - data
        - schema
        - table
        - attribute
        - glossary_term
        - policy
        - business_policy
      example: schema

    oid:
      description: ID of object
      type: number
      nullable: true
      example: 5

  parameters:
    status:
      description: >
        Only workflows with the specified status will be returned. Workflow in any of
        `published`, `unpublished`, `failed` status get treated as `alive`.
      in: query
      name: status
      required: false
      schema:
        type: string
        enum:
          - alive
          - published
          - unpublished
          - failed
          - deleted
          - all
        default: alive
      examples:
        alive:
          value: alive
          summary: >
            Alive workflows. Alive workflows can be `published`, `unpublished`, or `failed`
        published:
          value: published
          summary: Published workflows
        unpublished:
          value: unpublished
          summary: Unpublished workflows
        failed:
          value: failed
          summary: Failed workflows.
        deleted:
          value: deleted
          summary: Deleted workflows
        all:
          value: all
          summary: All workflows without any filtering on status
