info:
  description: Public API for reading Groups
  title: Group Public API
  version: 1.0.0
openapi: 3.0.0
paths:
  /group/:
    get:
      description: This API is used to list all Groups.
      operationId: getGroups
      parameters:
      - $ref: '#/components/parameters/GroupList_display_name'
      - $ref: '#/components/parameters/GroupList_display_name__contains'
      - $ref: '#/components/parameters/GroupList_display_name__icontains'
      - $ref: '#/components/parameters/GroupList_email'
      - $ref: '#/components/parameters/GroupList_email__contains'
      - $ref: '#/components/parameters/GroupList_email__icontains'
      - $ref: '#/components/parameters/GroupList_id'
      - $ref: ./common/parameters.yaml#/components/parameters/Limit
      - $ref: '#/components/parameters/GroupList_profile_id'
      - $ref: ./common/parameters.yaml#/components/parameters/Offset
      - $ref: '#/components/parameters/GroupList_order_by'
      - $ref: ./common/parameters.yaml#/components/parameters/Values
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupList'
          description: Requested Groups
        400:
          $ref: ./common/responses.yaml#/components/responses/Standard_400_Error_Response
        401:
          $ref: ./common/responses.yaml#/components/responses/Standard_401_Error_Response
        403:
          $ref: ./common/responses.yaml#/components/responses/Standard_403_Error_Response
        404:
          $ref: ./common/responses.yaml#/components/responses/Standard_404_Error_Response
        500:
          $ref: ./common/responses.yaml#/components/responses/Standard_500_Error_Response
      summary: GET multiple Groups
      tags:
      - Group
  /group/{id}/:
    get:
      description: This API fetches an individual Group.
      operationId: getGroupById
      parameters:
      - $ref: '#/components/parameters/Group_id'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Group'
          description: Confirmation that a Group has been fetched.
        400:
          $ref: ./common/responses.yaml#/components/responses/Standard_400_Error_Response
        401:
          $ref: ./common/responses.yaml#/components/responses/Standard_401_Error_Response
        403:
          $ref: ./common/responses.yaml#/components/responses/Standard_403_Error_Response
        404:
          $ref: ./common/responses.yaml#/components/responses/Standard_404_Error_Response
        500:
          $ref: ./common/responses.yaml#/components/responses/Standard_500_Error_Response
      summary: GET a Group
      tags:
      - Group
security:
- AlationAdminAPIKeyAuth: []
servers:
- url: '{protocol}://{base-url}/integration/v1'
  variables:
    base-url:
      default: localhost
      description: Alation BASE_URL setting for this instance.
    protocol:
      default: http
      enum:
      - http
      - https
tags:
- description: The Group object. Provides access to reading Groups.
  name: Group


components:
  schemas:
    display_name:
      description: The Group's display name.
      type: string
    email:
      description: The Group's email address.
      type: string
    id:
      description: The Group's id.
      minimum: 1
      type: integer
    profile_id:
      description: The Group's profile id.
      minimum: 1
      type: integer
    url:
      description: The url to the Group's profile page.
      type: string
    Group:
      type: object
      properties:
        display_name:
          allOf:
            - $ref: "#/components/schemas/display_name"
            - readOnly: true
        email:
          allOf:
            - $ref: "#/components/schemas/email"
            - readOnly: true
        id:
          allOf:
            - $ref: "#/components/schemas/id"
            - readOnly: true
        profile_id:
          allOf:
            - $ref: "#/components/schemas/profile_id"
            - readOnly: true
        url:
          allOf:
            - $ref: "#/components/schemas/url"
            - readOnly: true
    GroupList:
      type: array
      items:
        $ref: "#/components/schemas/Group"

  parameters:
    Group_id:
      description: The Group ID to retrieve.
      example: 1
      in: path
      name: id
      required: true
      schema:
        $ref: "#/components/schemas/id"
    GroupList_display_name:
      description: Filter by an exact match on display_name
      in: query
      name: display_name
      required: false
      schema:
        $ref: "#/components/schemas/display_name"
    GroupList_display_name__contains:
      description: Filter by a case-sensitive substring on display_name
      in: query
      name: display_name__contains
      required: false
      schema:
        $ref: "#/components/schemas/display_name"
    GroupList_display_name__icontains:
      description: Filter by a case-insensitive substring on display_name
      in: query
      name: display_name__icontains
      required: false
      schema:
        $ref: "#/components/schemas/display_name"
    GroupList_email:
      description: Filter by an exact match on email
      in: query
      name: email
      required: false
      schema:
        $ref: "#/components/schemas/email"
    GroupList_email__contains:
      description: Filter by a case-sensitive substring on email
      in: query
      name: email__contains
      required: false
      schema:
        $ref: "#/components/schemas/email"
    GroupList_email__icontains:
      description: Filter by a case-insensitive substring on email
      in: query
      name: email__icontains
      required: false
      schema:
        $ref: "#/components/schemas/email"
    GroupList_id:
      description: Filter by Group ID. Multiple IDs can be specified using the format ?id=1&id=2.
      in: query
      name: id
      required: false
      schema:
        $ref: "#/components/schemas/id"
    GroupList_profile_id:
      description: >
        Filter by Group Profile ID. Multiple IDs can be specified using the format
        ?profile_id=1&profile_id=2.
      in: query
      name: profile_id
      required: false
      schema:
        $ref: "#/components/schemas/profile_id"
    GroupList_order_by:
      description: >
        Sort by a specified property:
         * `display_name` - A to Z, by display_name
         * `-display_name` - Z to A, by display_name
         * `email` - A to Z, by email
         * `-email` - Z to A, by email
         * `id` - Ascending by id
         * `-id` - Descending by id
         * `profile_id` - Ascending by id
         * `-profile_id` - Descending by id
      in: query
      name: order_by
      required: false
      schema:
        allOf:
          - $ref: "./common/parameters.yaml#/components/schemas/OrderBy"
          - enum:
            - display_name
            - -display_name
            - email
            - -email
            - id
            - -id
            - profile_id
            - -profile_id

  securitySchemes:
    AlationAdminAPIKeyAuth:
      type: apiKey
      in: header
      name: TOKEN
      description: >
        API Key corresponding to a Server Admin user. See
        https://customerportal.alationdata.com/docs/GetToken/index.html and
        /openapi/api_authentication/ for more information.
