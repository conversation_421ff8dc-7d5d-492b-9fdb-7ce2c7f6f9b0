info:
  description: PublicAPI for creating, reading, updating and deleting Dataflow Objects
    and Lineage Paths
  title: Lineage v2 API
  version: 1.0.0
openapi: 3.0.0
paths:
  /dataflow/:
    delete:
      description: This API allows you to delete multiple dataflow objects and their
        associated lineage information.
      operationId: deleteDataflow
      parameters:
      - $ref: '#/components/parameters/KeyField'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Object_Ids'
        required: true
      responses:
        202:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JobID'
          description: Job Id of the delete job triggered
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        429:
          $ref: '#/components/responses/Standard_429_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Delete a list of Dataflow objects
      tags:
      - Dataflow
    get:
      description: 'API to fetch dataflow objects'' details and their associated lineage
        paths. Dataflow Objects can be filtered by providing an array of dataflow
        object IDs or external IDs in the request body payload. Note: Request body
        payload is optional and may not be supported in some API clients such as Swagger
        UI.'
      operationId: getDataflow
      parameters:
      - $ref: '#/components/parameters/KeyField'
      - $ref: '#/components/parameters/Limit'
      - $ref: '#/components/parameters/Skip'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Object_Ids'
        required: false
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Dataflow_Payload'
          description: Object containing dataflow object details and their lineage
            paths
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        429:
          $ref: '#/components/responses/Standard_429_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Get Dataflow object details and related paths
      tags:
      - Dataflow
    patch:
      description: 'This API updates title, description and content of multiple dataflow
        objects. Note: The Paths section cannot be updated using this API.'
      operationId: patchDataflow
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Dataflows_Patch_Payload'
        required: true
      responses:
        202:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JobID'
          description: Job Id of the dataflow job created
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        429:
          $ref: '#/components/responses/Standard_429_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Update a list of Dataflow objects
      tags:
      - Dataflow
    post:
      description: This API creates and replaces a list of dataflow objects along
        with its lineage paths. If an object with a matching external_id exists, it
        is replaced with the given payload. Otherwise, it is created.
      operationId: postDataflow
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Dataflow_Payload'
        required: true
      responses:
        202:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JobID'
          description: Job Id of the dataflow job created
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        429:
          $ref: '#/components/responses/Standard_429_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Create/Replace a list of Dataflow objects with lineage infomation
      tags:
      - Dataflow
  /lineage/:
    delete:
      description: This API lets you delete lineage information of data objects and
        dataflows.
      operationId: deleteLineage
      parameters:
      - $ref: '#/components/parameters/source_otype'
      - $ref: '#/components/parameters/source_key'
      - $ref: '#/components/parameters/target_otype'
      - $ref: '#/components/parameters/target_key'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JobID'
          description: Job Id of the delete job triggered
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        429:
          $ref: '#/components/responses/Standard_429_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Delete lineage information
      tags:
      - Lineage
    get:
      description: 'API to fetch lineage information for objects. These paths are
        the links from the nearest sources and target objects in their lineage graph.
        **Note**: Objects not existing in Alation will have `is_temp = True`. Supported
        `otypes` are `Table` , `Column/Attribute`, `BIDatasource`, `BIDatasourceColumn`,
        `BIReport`, `BIReportColumn`, `File`, `Directory`, `External` and `Dataflow`.'
      operationId: getLineage
      parameters:
      - $ref: '#/components/parameters/Lineage_get_object_type'
      - $ref: '#/components/parameters/Lineage_get_object_id'
      - $ref: '#/components/parameters/Lineage_get_object_keyField'
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Lineage_get_Response'
          description: Lineage paths associated with the Object.
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        429:
          $ref: '#/components/responses/Standard_429_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Get lineage information
      tags:
      - Lineage
    post:
      description: This API lets you upload new lineage information of objects existing
        in/outside of Alation. This API is superseded by Post Dataflow API allowing
        create/updates to title and description and better error handling.
      operationId: postLineage
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Lineage_Payload'
        required: true
      responses:
        200:
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JobID'
          description: Job Id of the lineage job created
        400:
          $ref: '#/components/responses/Standard_400_Error_Response'
        401:
          $ref: '#/components/responses/Standard_401_Error_Response'
        403:
          $ref: '#/components/responses/Standard_403_Error_Response'
        404:
          $ref: '#/components/responses/Standard_404_Error_Response'
        429:
          $ref: '#/components/responses/Standard_429_Error_Response'
        500:
          $ref: '#/components/responses/Standard_500_Error_Response'
      summary: Post lineage information
      tags:
      - Lineage
security:
- ApiKeyAuth: []
servers:
- url: '{protocol}://{base-url}/integration/v2/'
  variables:
    base-url:
      default: <base-url>
      description: Django BASE_URL setting for this instance.
    protocol:
      default: http
      enum:
      - http
      - https
tags:
- description: Represents a Dataflow in Alation
  name: Dataflow
- description: Represents a Lineage in Alation
  name: Lineage


components:
  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: TOKEN
      description: API Key for the user

  schemas:
    DataflowBase:
      description: Properties of a Dataflow Object
      type: object
      properties:
        title:
          type: string
          description: Title of the dataflow object
          example: Purchase transaction Transformation
        description:
          type: string
          description: >
            The description of the transformation
          example: Data flow from customer table to purchase history table
        content:
          type: string
          description: The transformation logic, code or sql query
          example: select c.id, c.amount from customers c inner join purchases p on c.id = p.cid;
        group_name:
          type: string
          description: The dataflow source group name to which the dataflow belongs to. Case sensitive. 
            If an exact match is found then it would be used, otherwise it will create a new dataflow source group.
          example: "Snowflake-1"

    Dataflow:
      allOf:
      - type: object
        properties:
          id:
            type: number
            description: The Alation ID of the dataflow object
            example: 1
            readOnly: true
          external_id:
            type: string
            description: >
              The external id of the dataflow object. To access this dataflow object uniquely.
            example: api/df101

        required:
          - external_id
      - $ref: '#/components/schemas/DataflowBase'

    Dataflows:
      type: array
      items:
        $ref: "#/components/schemas/Dataflow"

    Dataflow_Paths:
      description: >
        "paths" is an array of "path"s. Each "path" specifies the details of sources (-> dataflows)
        -> targets lineage by listing elements of each step, or "segment", of the lineages in order.
         Each "segment" may contain data objects and/or dataflows, but the first and the last
         "segment" of a "path" SHOULD NOT contain any dataflows.
      type: array
      items:
        description: Combination of source array, dataflow array, target array
        type: array
        items:
          description: Array of otype and keys
          type: array
          items:
            type: object
            properties:
              otype:
                type: string
                enum: [dataflow, table, column, file, directory, bi_report, bi_report_column,
                bi_datasource, bi_datasource_column, external]
                description: >
                  Alation object type of source/target
                   * `dataflow` - Represents a dataflow
                   * `table` - Represents a table
                   * `column/attribute` - Represents a column
                   * `file` - Represents a file
                   * `directory` - Represents a directory
                   * `bi_report` - Represents a report of a BI server
                   * `bi_report_column` - Represents a column of a report of a BI server
                   * `bi_datasource` - Represents a data source of a BI server
                   * `bi_datasource_column` - Represents a column of a data source of a BI server
                   * `external` - Placeholder for anything Alation doesn't support natively
              key:
                type: string
                enum: [dataflow, table, column, file, directory, bi_report, bi_report_column,
                bi_datasource, bi_datasource_column, external]
                description: >
                  Unique identifier of an object
                   * `dataflow` - api/&lt;unique identifier of dataflow>
                   * `table` - <datasource_id>.[&lt;dbname>/<catalog_name>.]<schema_name>.<table_name> NOTE:
                   <db_name> has to be specified for SQL Server, Redshift, Snowflake and Netezza.
                   * `column/attribute` - <datasource_id>.[&lt;dbname>/<catalog_name>.]<schema_name>.<table_name>.<column_name>
                    NOTE: <db_name> has to be specified for SQL Server, Redshift, Snowflake and Netezza.
                   * `file` - <filesystem_id>.<full path of a file delimited by '/'>
                   * `directory` - <filesystem_id>.<full path of a directory delimited by '/'>
                   * `bi_report` - <bi_server_id>.bi_report.&lt;unique identifier of bi_report on
                   the server>
                   * `bi_report_column` - <bi_server_id>.bi_report_column.&lt;unique identifier of
                   bi_report_column on the server>
                   * `bi_datasource` - <bi_server_id>.bi_datasource.&lt;unique identifier of
                   bi_datasource on the server>
                   * `bi_datasource_column` - <bi_server_id>.bi_datasource_column.&lt;unique
                   identifier of bi_datasource_column on the server>
                   * `external` - <unique identifier/name of external object>

            required:
              - otype
              - key
      example:
        - - - otype: table
              key: 1.schema.Customers
          - - otype: dataflow
              key: api/df101
          - - otype: table
              key: 1.schema.Purchases

    Lineage_Paths:
      description: >
        "paths" is an array of "path"s. Each "path" specifies the details of sources -> targets
        lineage by listing elements of each step, or "segment", of the lineages in order. Each
        "segment" may contain data objects.
      type: array
      items:
        description: Combination of source array, target array
        type: array
        items:
          description: Array of otype and keys
          type: array
          items:
            type: object
            properties:
              otype:
                type: string
                enum: [dataflow, table, column, file, directory, bi_report, bi_report_column,
                bi_datasource, bi_datasource_column, external]
                description: >
                  Alation object type of source/target
                   * `dataflow` - Represents a dataflow
                   * `table` - Represents a table
                   * `column/attribute` - Represents a column
                   * `file` - Represents a file
                   * `directory` - Represents a directory
                   * `bi_report` - Represents a report of a BI server
                   * `bi_report_column` - Represents a column of a report of a BI server
                   * `bi_datasource` - Represents a data source of a BI server
                   * `bi_datasource_column` - Represents a column of a data source of a BI server
                   * `external` - Placeholder for anything Alation doesn't support natively
              key:
                type: string
                enum: [dataflow, table, column, file, directory, bi_report, bi_report_column,
                bi_datasource, bi_datasource_column, external]
                description: >
                  Unique identifier of an object
                   * `dataflow` - api/&lt;unique identifier of dataflow>
                   * `table` - <datasource_id>.[&lt;dbname>/<catalog_name>.]<schema_name>.<table_name> NOTE:
                   <db_name> has to be specified for SQL Server, Redshift, Snowflake and Netezza.
                   * `column/attribute` - <datasource_id>.[&lt;dbname>/<catalog_name>.]<schema_name>.<table_name>.<column_name>
                    NOTE: <db_name> has to be specified for SQL Server, Redshift, Snowflake and Netezza.
                   * `file` - <filesystem_id>.<full path of a file delimited by '/'>
                   * `directory` - <filesystem_id>.<full path of a directory delimited by '/'>
                   * `bi_report` - <bi_server_id>.bi_report.&lt;unique identifier of bi_report on
                   the server>
                   * `bi_report_column` - <bi_server_id>.bi_report_column.&lt;unique identifier of
                   bi_report_column on the server>
                   * `bi_datasource` - <bi_server_id>.bi_datasource.&lt;unique identifier of
                   bi_datasource on the server>
                   * `bi_datasource_column` - <bi_server_id>.bi_datasource_column.&lt;unique
                   identifier of bi_datasource_column on the server>
                   * `external` - <unique identifier/name of external object>

            required:
              - otype
              - key
      example:
        - - - otype: table
              key: 1.schema.Customers
          - - otype: table
              key: 1.schema.Purchases

    Lineage_Payload:
      type: object
      properties:
        paths:
          $ref: '#/components/schemas/Lineage_Paths'

      required:
        - paths

    Dataflow_Payload:
      type: object
      properties:
        dataflow_objects:
          $ref: '#/components/schemas/Dataflows'
        paths:
          $ref: '#/components/schemas/Dataflow_Paths'

    JobID:
      description: Properties of a Job Object
      type: object
      properties:
        job_id:
          type: integer
          description: >
            GET /api/v1/bulk_metadata/job/?id={job_id} will return the status of the underlying
            POST job

    Object_Ids:
      description: List of dataflow object ids or external ids
      type: array
      items:
        anyOf:
          - type: integer
          - type: string
      example: [ 1, 2, 3]

    KeyField:
      title: KeyField Enum
      description: Field to use as an identifier of the resource (id, or external_id)
      type: string
      enum:
        - id
        - external_id
      default: id

    Error:
      type: object
      description: Properties of a Error Object
      properties:
        errors:
          type: object
          description: Explicit information about the error message
        code:
          type: string
          description: >
            A six digit code that identifies the problem. Refer the error documentation
            for more information.
        detail:
          type: string
          description: Details about the error message
        title:
          type: string
          description: The title of the error message

    Dataflow_Patch_Payload:
      allOf:
      - type: object
        properties:
          id:
            type: integer
            description: >
              The id of the dataflow object in Alation.
            example: 1

        required:
          - id
      - $ref: '#/components/schemas/DataflowBase'

    Dataflows_Patch_Payload:
      type: array
      items:
        $ref: "#/components/schemas/Dataflow_Patch_Payload"

    Lineage_get_Response:
      description: >
        "paths" is an array of "path"s. Each "path" specifies the details of sources (-> dataflows)
        -> targets lineage by listing elements of each step, or "segment", of the lineages in order.
         Each "segment" may contain data objects and/or dataflows, but the first and the last
         "segment" of a "path" SHOULD NOT contain any dataflows.
      type: object
      properties:
        paths:
          type: array
          items:
            description: Combination of source array, dataflow array, target array
            type: array
            items:
              description: Array of otype and keys
              type: array
              items:
                type: object
                properties:
                  otype:
                    type: string
                    enum: [dataflow, table, column, file, directory, bi_report, bi_report_column,
                    bi_datasource, bi_datasource_column, external]
                    description: >
                      Alation object type of source/target
                       * `dataflow` - Represents a dataflow
                       * `table` - Represents a table
                       * `column/attribute` - Represents a column
                       * `file` - Represents a file
                       * `directory` - Represents a directory
                       * `bi_report` - Represents a report of a BI server
                       * `bi_report_column` - Represents a column of a report of a BI server
                       * `bi_datasource` - Represents a data source of a BI server
                       * `bi_datasource_column` - Represents a column of a data source of a BI server
                       * `external` - Placeholder for anything Alation doesn't support natively
                  key:
                    type: string
                    enum: [dataflow, table, column, file, directory, bi_report, bi_report_column,
                    bi_datasource, bi_datasource_column, external]
                    description: >
                      Unique identifier of an object
                       * `dataflow` - api/&lt;unique identifier of dataflow>
                       * `table` - <datasource_id>.[&lt;dbname>/<catalog_name>.]<schema_name>.<table_name> NOTE:
                       <db_name> has to be specified for SQL Server, Redshift, Snowflake and Netezza.
                       * `column/attribute` - <datasource_id>.[&lt;dbname>/<catalog_name>.]<schema_name>.<table_name>.<column_name>
                        NOTE: <db_name> has to be specified for SQL Server, Redshift, Snowflake and Netezza.
                       * `file` - <filesystem_id>.<full path of a file delimited by '/'>
                       * `directory` - <filesystem_id>.<full path of a directory delimited by '/'>
                       * `bi_report` - <bi_server_id>.bi_report.&lt;unique identifier of bi_report on
                       the server>
                       * `bi_report_column` - <bi_server_id>.bi_report_column.&lt;unique identifier of
                       bi_report_column on the server>
                       * `bi_datasource` - <bi_server_id>.bi_datasource.&lt;unique identifier of
                       bi_datasource on the server>
                       * `bi_datasource_column` - <bi_server_id>.bi_datasource_column.&lt;unique
                       identifier of bi_datasource_column on the server>
                       * `external` - <unique identifier/name of external object>
          example:
            - - - otype: table
                  key: 1.schema.Customers
              - - otype: table
                  key: 1.schema.Purchases

  requestBodies:
    Lineage_Payload_Request:
      description: Request body for Lineage API
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Lineage_Payload'

  responses:
    Standard_400_Error_Response:
      description: Malformed Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "400XXX"
            title: "Malformed Request"
            detail: "The request sent by user is in the wrong format.
            (Refer the error documentation for specific details of the error)"

    Standard_401_Error_Response:
      description: Unauthorized bad/missing token
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "401XXX"
            title: "Unauthorized"
            detail: "You are unauthorized to access this resource. Please obtain valid credentials.
            (Refer the error documentation for specific details of the error)"
    Standard_403_Error_Response:
      description: Forbidden User cannot edit this resource
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "403XXX"
            title: "Forbidden Action"
            detail: "This is forbidden to access. Make sure you access the right resource.
            (Refer the error documentation for specific details of the error)"
    Standard_404_Error_Response:
      description: The specified resource was not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "404XXX"
            title: "Resource not found"
            detail: "This is not a valid resource. Please try something else.
            (Refer the error documentation for specific details of the error)"
    Standard_429_Error_Response:
      description: Too many requests
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "429XXX"
            title: "Too many requests"
            detail: "Rate limit for read/ write requests reached. Expected available in X seconds."
    Standard_500_Error_Response:
      description: Internal Server Error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "500XXX"
            title: "Internal Server Error"
            detail: "Something went wrong, Please try again later.
            (Refer the error documentation for specific details of the error)"

  parameters:
    datasource_id:
      name: datasource_id
      description: id of the datasource to be retrieved
      in: path
      schema:
        type: integer
      required: true
      examples:
        id:
          value: 10
          summary: A sample integer datasource id

    source_otype:
      name: source_otype
      description: otype of the source object
      in: query
      required: true
      schema:
        type: string
      examples:
        source_otype:
          value: table

    source_key:
      name: source_key
      description: unique key of the source object
      in: query
      required: true
      schema:
        type: string
      examples:
        source_otype:
          value: <datasource_id>.[<dbname>.]<schema_name>.<table_name>

    target_otype:
      name: target_otype
      description: otype of the target object
      in: query
      required: true
      schema:
        type: string
      examples:
        target_otype:
          value: dataflow

    target_key:
      name: target_key
      description: unique key of the target object
      in: query
      required: true
      schema:
        type: string
      examples:
        target_key:
          value: api/<unique identifier of dataflow>

    ObjectIdsRequired:
      name: oids
      description: List of object ids to be used
      explode: false
      in: query
      required: true
      schema:
        items:
          anyOf:
            - type: integer
            - type: string
        type: array
      examples:
        oids_value:
          value: [1,2]
          summary: A sample list of object id's

    KeyField:
      description: Field to use as an identifier of the resource (id, or external_id)
      in: query
      name: keyField
      required: false
      schema:
        $ref: "#/components/schemas/KeyField"

    Limit:
      description: >
        Specifies the number of objects to be fetched in one paginated request. Response may
        have a X-Next-Page URL parameter in the header to fetch next set of objects or page
      in: query
      name: limit
      required: false
      schema:
        type: integer
        default: 100

    Skip:
      description: >
        Specifies the number of objects to be skipped in one request. Used together with
        Limit parameter to fetch paginated requests. This is automatically set in X-Next-Page
        response header if present
      in: query
      name: skip
      required: false
      schema:
        type: integer
        default: 0

    ObjectId:
      description: The ID of the object
      in: path
      name: id
      required: true
      schema:
        type: integer
      examples:
        ids:
          value: 123
          summary: A sample integer object id

    Lineage_get_object_type:
      name: otype
      description: >
        `otype` of the object
      in: query
      required: true
      schema:
        type: string
        enum:
          - table
          - column
          - bi_report
          - bi_report_column
          - bi_datasource
          - bi_datasource_column
          - dataflow
          - external
          - file
          - directory
        default: table

    Lineage_get_object_id:
      name: oid
      description: >
        The id of the object or the unique key of object. eg: If the **keyField** is `id` then
        **oid** is an integer `oid=1`, and if the **keyField** is a **key** then oid is the unique
        identifier of the object.
        Example of oid as key:
          * <ds_id>.[&lt;dbname>/<catalog_name>.]&lt;schema>.&lt;table> for otype `table`
          * <ds_id>.[&lt;dbname>/<catalog_name>.]&lt;schema>.&lt;table>.&lt;column> for otype
          `column/attribute`
          * <bi_server_id>.bi_report.&lt;unique identifier of bi_report on the server> for
          otype  `bi_report`
          * <bi_server_id>.bi_report_column.&lt;unique identifier of bi_report_column on the
          server> for otype `bi_report_column`
          * <bi_server_id>.bi_datasource.&lt;unique identifier of bi_datasource on the server>
          for otype `bi_datasource`
          * <bi_server_id>.bi_datasource_column.&lt;unique identifier of bi_datasource_column on
          the server> for otype `bi_datasource_column`
          * <filesystem_id>.<full path of a file delimited by '/'> for otype `file`
          * <filesystem_id>.<full path of a directory delimited by '/'> for otype `directory`
          * <unique identifier/name of external object> for otype `external`

        Note: `file`, `directory` and `external` otype only allow `key` as `keyField` parameter.
        Other otypes support both `id` and `key` as the `keyField` parameter.
      in: query
      required: true
      schema:
        type: string

    Lineage_get_object_keyField:
      name: keyField
      description: >
        Field to identify the query parameter `oid`, is of type `id` or `key`.
      in: query
      required: true
      schema:
        type: string
        enum:
          - id
          - key
        default: id
