<svg width="71" height="64" viewBox="0 0 71 64" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_30836_55024)">
<rect x="8" y="8" width="48" height="48" rx="4" fill="#222C67"/>
<rect x="8.5" y="8.5" width="47" height="47" rx="3.5" stroke="#222C67"/>
</g>
<g clip-path="url(#clip0_30836_55024)">
<path d="M20.0358 25.1426H43.8452V42.9997H20.0358V25.1426Z" fill="#D7E0FF"/>
<path d="M24.2024 36.4531H20.0358V42.9329H43.8452V36.4531H39.6786L37.2976 38.8341H26.5834L24.2024 36.4531Z" fill="#222C67"/>
<path d="M20.0358 25.1426H43.8452V42.9997H20.0358V25.1426Z" stroke="white" stroke-width="1.5"/>
<path d="M20.0358 36.4531H24.2024L26.5834 38.8341H37.2976L39.6786 36.4531H43.8452" stroke="white" stroke-width="1.5"/>
<path d="M31.9405 25.1428V18" stroke="white" stroke-width="1.5"/>
<path d="M27.1786 29.9053V32.8815" stroke="#222C67" stroke-width="1.5"/>
<path d="M36.7025 29.9053V32.8815" stroke="#222C67" stroke-width="1.5"/>
</g>
<defs>
<filter id="filter0_d_30836_55024" x="-7" y="-3" width="78" height="78" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="7.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.07 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_30836_55024"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_30836_55024" result="shape"/>
</filter>
<clipPath id="clip0_30836_55024">
<rect width="28" height="28" fill="white" transform="translate(18 18)"/>
</clipPath>
</defs>
</svg>
