/* eslint-disable @typescript-eslint/no-var-requires */
const path = require("path");
const unusedImports = require("eslint-plugin-unused-imports");
const {version} = require('./package.json');

// TODO: move to common functions file and import.
const EnvironmentEnum = {
    Dev: "development",
    PreProd: "test",
    Prod: "production",
};

// TODO: move to common functions file and import.
const getCurrentEnv = () => {
    if (process.env.NODE_ENV === EnvironmentEnum.PreProd) {
        return EnvironmentEnum.PreProd;
    } else if (process.env.NODE_ENV === EnvironmentEnum.Prod) {
        return EnvironmentEnum.Prod;
    }

    return EnvironmentEnum.Dev;
};

// TODO: move to common functions file and import.
const isDevEnv = () => {
    return process.env.NODE_ENV === EnvironmentEnum.Dev;
};

/**
 * Next.js configuration
 * @type {import('next').NextConfig}
 */
module.exports = {
    // Enable trailing slashes for URLs
    trailingSlash: false,

    // // Output directory for static exports
    output: "standalone",

    //Base path for the application
    basePath: process.env.NEXT_PUBLIC_BASE_PATH,

    // Disable React strict mode
    reactStrictMode: false,

    env: {
        version
    },

    //Packages required for runtime
    serverExternalPackages: ['pino', 'next-logger'],

    //Proxy for local
    async rewrites() {
        if (isDevEnv()) {
            return [
                {
                    source: '/gateway/:path*',
                    destination: 'http://127.0.0.1:8004/gateway/:path*',
                },
            ];
        } else return [];
    }

};
