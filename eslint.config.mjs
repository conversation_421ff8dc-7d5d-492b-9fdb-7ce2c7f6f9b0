import js from '@eslint/js'
import {FlatCompat} from '@eslint/eslintrc'
import unusedImports from 'eslint-plugin-unused-imports'

const compat = new FlatCompat({
    baseDirectory: import.meta.dirname,
    recommendedConfig: js.configs.recommended,
})

const eslintConfig = [
    ...compat.config({
        extends: ["next/core-web-vitals", "next/typescript", 'eslint:recommended', 'next'],
    }),
    {
        plugins: {
            'unused-imports': unusedImports,
        },
        rules: {
            'no-unused-vars': 'off',
            "@typescript-eslint/no-unused-vars": "off",
            "@typescript-eslint/no-explicit-any":"off",
            'unused-imports/no-unused-imports': 'off',
            "@typescript-eslint/no-require-imports": "off",
            'unused-imports/no-unused-vars': [
                'warn',
                {vars: 'all', varsIgnorePattern: '^_', args: 'after-used', argsIgnorePattern: '^_'}
            ],
        },
    },
]

export default eslintConfig